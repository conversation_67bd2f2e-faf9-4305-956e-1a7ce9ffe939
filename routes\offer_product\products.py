from flask import jsonify, request
from models import UnifiedStoreofferProducts
from sqlalchemy import or_
from . import bp
import logging

logger = logging.getLogger(__name__)

@bp.route('/products')
def get_products():
    """获取产品列表的路由处理函数"""
    try:
        # 获取查询参数
        store_id = request.args.get('store_id')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 30, type=int)
        search = request.args.get('search', '').strip()
        status = request.args.get('status', '')
        country = request.args.get('country', '')
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        sort_by = request.args.get('sort_by', '')
        sort_order = request.args.get('sort_order', 'asc')
        
        # 构建基础查询
        query = UnifiedStoreofferProducts.query
        
        # 应用店铺筛选
        if store_id:
            query = query.filter(UnifiedStoreofferProducts.seller_id == store_id)
        
        # 应用搜索条件
        if search:
            query = query.filter(
                or_(
                    UnifiedStoreofferProducts.title.ilike(f'%{search}%'),
                    UnifiedStoreofferProducts.sku.ilike(f'%{search}%'),
                    UnifiedStoreofferProducts.ean.ilike(f'%{search}%')
                )
            )
        
        # 应用状态筛选
        if status:
            if status == 'buybox':
                # 购物车产品筛选：店铺出价等于购物车价格的商品
                query = query.filter(UnifiedStoreofferProducts.sell_price == UnifiedStoreofferProducts.buybox_price)
            else:
                # 普通状态筛选
                query = query.filter(UnifiedStoreofferProducts.status == status)
        
        # 应用国家筛选
        if country:
            query = query.filter(UnifiedStoreofferProducts.app_name == country)
            
        # 应用价格范围筛选
        if min_price is not None:
            query = query.filter(UnifiedStoreofferProducts.sell_price >= min_price)
        if max_price is not None:
            query = query.filter(UnifiedStoreofferProducts.sell_price <= max_price)
            
        # 应用排序
        if sort_by:
            sort_column = getattr(UnifiedStoreofferProducts, sort_by, None)
            if sort_column is not None:
                if sort_order == 'desc':
                    sort_column = sort_column.desc()
                query = query.order_by(sort_column)
        
        # 获取总数
        total_count = query.count()
        
        # 获取分页数据
        products = query.offset((page - 1) * per_page).limit(per_page).all()
        
        # 转换数据格式
        products_data = [{
            'id': product.id,
            'app_name': product.app_name,
            'delivery_hours': product.delivery_hours,
            'amount': product.amount,
            'sell_price': product.sell_price,
            'sell_price_after_discount': product.sell_price_after_discount,
            'status': product.status,
            'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else None,
            'updated_at': product.updated_at.strftime('%Y-%m-%d %H:%M:%S') if product.updated_at else None,
            'ean': product.ean,
            'sku': product.sku,
            'title': product.title,
            'insult_price': product.insult_price,
            'buybox_price': product.buybox_price,
            'base_price': product.base_price,
            'external_id': product.external_id,
            'pigu_external_id': product.pigu_external_id,
            'relevant_market_price': product.relevant_market_price
        } for product in products]
        
        return jsonify({
            'products': products_data,
            'pagination': {
                'current_page': page,
                'per_page': per_page,
                'total_pages': (total_count + per_page - 1) // per_page,
                'total_count': total_count
            }
        })
        
    except Exception as e:
        # logger.error(f"获取产品列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500 