from flask import Blueprint, jsonify, request
from datetime import datetime, timedelta
from models import db, UploadTask, Store, UploadTaskDetail, UnifiedListProducts, beijing_now
from sqlalchemy import desc
import requests
import logging
import json
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

bp = Blueprint('upload_task', __name__)

@bp.route('/api/upload-tasks', methods=['GET'])
def get_upload_tasks():
    """获取上传任务列表"""
    try:
        # 获取查询参数
        seller_id = request.args.get('seller_id')
        status = request.args.get('status')
        date_start = request.args.get('date_start')
        date_end = request.args.get('date_end')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        # 构建查询
        query = UploadTask.query
        
        # 应用筛选条件
        if seller_id:
            query = query.filter(UploadTask.seller_id == seller_id)
            
        # 处理状态筛选
        if status:
            now = datetime.utcnow()
            # logger.info(f"当前UTC时间: {now}")
            if status == 'expired':
                query = query.filter(UploadTask.expires_at <= now)
            elif status == 'valid':
                query = query.filter(UploadTask.expires_at > now)
                
        # 处理日期筛选
        if date_start:
            start_date = datetime.strptime(date_start, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
            query = query.filter(UploadTask.created_at >= start_date)
        if date_end:
            end_date = datetime.strptime(date_end, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
            query = query.filter(UploadTask.created_at <= end_date)
            
        # 按创建时间倒序排序
        query = query.order_by(desc(UploadTask.created_at))
        
        # 分页
        pagination = query.paginate(page=page, per_page=per_page)
        
        # 构建响应数据
        tasks = []
        now = datetime.utcnow()
        
        for task in pagination.items:
            is_expired = task.expires_at <= now
            
            task_data = {
                'task_id': task.execution_id,
                'seller_id': task.seller_id,
                'upload_time': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'task_type': task.task_type,
                'total_products': task.total_products,
                'success_count': task.success_count,
                'failed_count': task.failed_count,
                'status': 'expired' if is_expired else 'valid',
                'processing_count': task.processing_count,
                'success_count_final': task.success_count_final,
                'error_count_final': task.error_count_final,
                'expires_at': task.expires_at.strftime('%Y-%m-%d %H:%M:%S') if task.expires_at else None,
                'remaining_days': max(0, (task.expires_at - now).days) if task.expires_at else 0
            }
            tasks.append(task_data)
            
        return jsonify({
            'code': 0,
            'message': 'success',
            'data': {
                'items': tasks,
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            }
        })
        
    except Exception as e:
        # logger.error(f"获取上传任务列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': str(e)
        }), 500

@bp.route('/api/upload-tasks/<execution_id>', methods=['GET'])
def get_upload_task_detail(execution_id):
    """获取上传任务详情"""
    try:
        task = UploadTask.query.filter_by(execution_id=execution_id).first()
        if not task:
            return jsonify({
                'code': 404,
                'message': '任务不存在'
            }), 404
            
        return jsonify({
            'code': 0,
            'message': 'success',
            'data': {
                'task_id': task.execution_id,
                'seller_id': task.seller_id,
                'upload_time': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'total_products': task.total_products,
                'success_count': task.success_count,
                'failed_count': task.failed_count,
                'status': task.status_text,
                'expires_at': task.expires_at.strftime('%Y-%m-%d %H:%M:%S') if task.expires_at else None,
                'remaining_days': task.remaining_days,
                'processing_count': task.processing_count,
                'success_count_final': task.success_count_final,
                'error_count_final': task.error_count_final,
                'check_status': task.check_status,
                'last_check_time': task.last_check_time.strftime('%Y-%m-%d %H:%M:%S') if task.last_check_time else None
            }
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': str(e)
        }), 500

@bp.route('/api/upload-tasks', methods=['POST'])
def create_upload_task():
    """创建上传任务记录"""
    try:
        data = request.get_json()
        
        # 使用UTC时间创建任务
        now = datetime.utcnow()
        expires_at = now + timedelta(days=14)
        
        # 创建新任务
        task = UploadTask(
            execution_id=data['execution_id'],
            seller_id=data['seller_id'],
            total_products=data.get('total_products', 0),
            success_count=data.get('success_count', 0),
            failed_count=data.get('failed_count', 0),
            expires_at=expires_at  # 使用UTC时间
        )
        
        db.session.add(task)
        db.session.commit()
        
        return jsonify({
            'code': 0,
            'message': 'success',
            'data': {
                'task_id': task.execution_id,
                'expires_at': expires_at.strftime('%Y-%m-%d %H:%M:%S')
            }
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'message': str(e)
        }), 500

@bp.route('/api/upload-tasks/<execution_id>', methods=['PUT'])
def update_upload_task(execution_id):
    """更新上传任务状态"""
    try:
        task = UploadTask.query.filter_by(execution_id=execution_id).first()
        if not task:
            return jsonify({
                'code': 404,
                'message': '任务不存在'
            }), 404
            
        data = request.get_json()
        
        # 更新任务信息
        if 'total_products' in data:
            task.total_products = data['total_products']
        if 'success_count' in data:
            task.success_count = data['success_count']
        if 'failed_count' in data:
            task.failed_count = data['failed_count']
        if 'status' in data:
            task.status = data['status']
            
        db.session.commit()
        
        return jsonify({
            'code': 0,
            'message': 'success'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'message': str(e)
        }), 500 

@bp.route('/api/upload-tasks/<execution_id>/check', methods=['POST'])
def check_upload_task(execution_id):
    """检查上传任务的最终状态"""
    try:
        # 获取任务信息
        task = UploadTask.query.filter_by(execution_id=execution_id).first()
        if not task:
            return jsonify({
                'code': 404,
                'message': '任务不存在'
            }), 404
            
        # 检查任务是否过期
        if not task.is_valid:
            return jsonify({
                'code': 400,
                'message': '任务已过期，无法更新'
            }), 400
            
        # 获取店铺信息
        store = Store.query.filter_by(seller_id=str(task.seller_id)).first()
        if not store:
            return jsonify({
                'code': 404,
                'message': '店铺不存在'
            }), 404
            
        # 获取token
        token = store.get_valid_token()
        
        # 重置计数器
        task.processing_count = 0
        task.success_count_final = 0
        task.error_count_final = 0
        processed_skus = set()
        
        # 获取第一页数据
        url = f"https://pmpapi.pigugroup.eu/v3/sellers/{task.seller_id}/product/import/execution/{execution_id}/results"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        # 设置初始参数
        params = {
            "limit": 100,
            "offset": 0
        }
        
        while True:
            response = requests.get(
                url,
                headers=headers,
                params=params,

            )
            
            if response.status_code != 200:
                return jsonify({
                    'code': response.status_code,
                    'message': '查询失败请检查网络是否通畅'
                }), 500
                
            result_data = response.json()
            
            #上传任务检查ID 断点 保存API更新任务ID详情响应到JSON文件
            filename = f'logs/api_response_{execution_id}.json'
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            
            # logger.info(f"API响应已保存到文件: {filename}")
            
            # 处理当前页的数据
            items = result_data.get('items', [])
            for item in items:
                sku = item['sku']
                if sku in processed_skus:
                    continue
                    
                processed_skus.add(sku)
                
                # 映射平台状态
                status_mapping = {
                    'processing': 'phh_processing',
                    'success': 'phh_success',
                    'error': 'phh_error'
                }
                platform_status = status_mapping.get(item['status'], 'phh_processing')

                #更新platform_message产品表
                product = UnifiedListProducts.query.filter_by(
                    seller_id=task.seller_id,
                    sku=sku
                ).first()
                if product:
                    product.platform_message = item['message']
                
                # 创建或更新任务详情记录
                detail = UploadTaskDetail.query.filter_by(
                    task_id=task.id,
                    sku=sku
                ).first()
                
                if not detail:
                    # 创建新记录
                    detail = UploadTaskDetail(
                        task_id=task.id,
                        sku=sku,
                        status='success',
                        response_id=item['id'],
                        platform_status=platform_status,
                        platform_message=item['message'],
                        check_time=beijing_now()
                    )
                    db.session.add(detail)
                else:
                    # 更新现有记录
                    detail.platform_status = platform_status
                    detail.platform_message = item['message']
                    detail.check_time = beijing_now()
                
                # 更新计数
                if platform_status == 'phh_processing':
                    task.processing_count += 1
                elif platform_status == 'phh_success':
                    task.success_count_final += 1
                elif platform_status == 'phh_error':
                    task.error_count_final += 1
                
                # 更新产品状态
                product = UnifiedListProducts.query.filter_by(
                    seller_id=task.seller_id,
                    sku=sku
                ).first()
                
                if product:
                    product.status = platform_status
            
            # 获取meta信息
            meta = result_data.get('meta', {})
            next_url = meta.get('next')
            
            # 如果没有下一页，检查并更新total_products
            if not next_url:
                # 获取API返回的总数
                api_total_count = meta.get('total_count', 0)
                
                # 如果与数据库中的total_products不一致，更新它
                if task.total_products != api_total_count:
                    # logger.info(f"更新任务 {execution_id} 的total_products: {task.total_products} -> {api_total_count}")
                    task.total_products = api_total_count
                
                break
                
            # 更新offset用于下一页请求
            params['offset'] = meta.get('offset', 0) + meta.get('limit', 100)

        
        # 更新任务状态
        task.check_status = 'checked'
        task.last_check_time = beijing_now()
        
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise
        
        return jsonify({
            'code': 0,
            'message': 'success',
            'data': {
                'processing_count': task.processing_count,
                'success_count': task.success_count_final,
                'error_count': task.error_count_final,
                'total_count': len(processed_skus),
                'total_products': task.total_products
            }
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'message': str(e)
        }), 500 

@bp.route('/api/upload-tasks/<execution_id>', methods=['DELETE'])
def delete_upload_task(execution_id):
    """删除上传任务及相关数据"""
    try:
        # 获取任务信息
        task = UploadTask.query.filter_by(execution_id=execution_id).first()
        if not task:
            return jsonify({
                'code': 404,
                'message': '任务不存在'
            }), 404
            
        try:
            # 先删除任务详情
            UploadTaskDetail.query.filter_by(task_id=task.id).delete()
            
            # 删除任务主记录
            db.session.delete(task)
            
            # 提交事务
            db.session.commit()
            
            return jsonify({
                'code': 0,
                'message': '删除成功'
            })
            
        except Exception as e:
            db.session.rollback()
            raise
            
    except Exception as e:
        # logger.error(f"删除任务失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': str(e)
        }), 500 