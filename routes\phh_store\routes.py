from flask import jsonify, request
from models import db, Store
from sqlalchemy import text
from datetime import datetime
import requests
import logging
from . import bp

# 配置日志
logger = logging.getLogger(__name__)

@bp.route('/stores')
def get_stores():
    """获取所有店铺列表"""
    try:
        stores = Store.query.all()
        return jsonify([{
            'id': store.id,
            'seller_id': store.seller_id,
            'name': store.name,
            'username': store.username,
            'status': store.status,
            'created_at': store.created_at.strftime('%Y-%m-%d %H:%M:%S') if store.created_at else None,
            'updated_at': store.updated_at.strftime('%Y-%m-%d %H:%M:%S') if store.updated_at else None
        } for store in stores])
    except Exception as e:
        # logger.error(f"获取店铺列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/stores', methods=['POST'])
def create_store():
    try:
        data = request.json
        
        # 检查必要字段
        required_fields = ['seller_id', 'name', 'username', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必要字段: {field}'}), 400
                
        # 检查seller_id是否重复
        existing_store = Store.query.filter_by(seller_id=data['seller_id']).first()
        if existing_store:
            return jsonify({'error': '该店铺ID已存在'}), 400

        # 开始事务
        store = Store(
            seller_id=data['seller_id'],
            name=data['name'],
            username=data['username'],
            password=data['password'],
            status='active'
        )
        
        try:
            # 添加店铺
            db.session.add(store)
            db.session.flush()  # 获取store.id
            
            # 创建产品表
            store.create_products_table()
            
            # 创建产品列表表
            store.create_list_products_table()
            
            # 获取初始token
            try:
                token = store.refresh_token()
                if not token:
                    raise Exception("无法获取店铺token")
                # logger.info(f"成功获取店铺token: {store.name}")
            except Exception as e:
                # logger.error(f"获取店铺token失败: {str(e)}")
                raise Exception(f"获取店铺token失败: {str(e)}")
            
            # 提交事务
            db.session.commit()
            
            return jsonify({
                'message': '店铺创建成功',
                'store': {
                    'id': store.id,
                    'seller_id': store.seller_id,
                    'name': store.name,
                    'username': store.username,
                    'status': store.status,
                    'created_at': store.created_at.strftime('%Y-%m-%d %H:%M:%S') if store.created_at else None,
                    'updated_at': store.updated_at.strftime('%Y-%m-%d %H:%M:%S') if store.updated_at else None
                }
            })
            
        except Exception as e:
            db.session.rollback()
            # logger.error(f"创建店铺失败: {str(e)}")
            return jsonify({'error': f'创建店铺失败: {str(e)}'}), 400
            
    except Exception as e:
        # logger.error(f"处理请求失败: {str(e)}")
        return jsonify({'error': f'处理请求失败: {str(e)}'}), 400

@bp.route('/stores/<int:store_id>', methods=['PUT'])
def update_store(store_id):
    try:
        store = Store.query.get_or_404(store_id)
        data = request.json
        for key, value in data.items():
            if hasattr(store, key):
                setattr(store, key, value)
        db.session.commit()
        return jsonify({'message': 'Store updated successfully'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@bp.route('/stores/<int:store_id>', methods=['PATCH'])
def update_store_name(store_id):
    try:
        store = Store.query.get_or_404(store_id)
        data = request.json
        
        # 验证请求数据
        if 'name' not in data:
            return jsonify({'error': '缺少店铺名称参数'}), 400
            
        new_name = data['name'].strip()
        if not new_name:
            return jsonify({'error': '店铺名称不能为空'}), 400
            
        # 更新店铺名称
        store.name = new_name
        store.updated_at = datetime.now()
        
        db.session.commit()
        return jsonify({
            'message': '店铺名称更新成功',
            'store': {
                'id': store.id,
                'name': store.name,
                'updated_at': store.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@bp.route('/stores/<int:store_id>', methods=['DELETE'])
def delete_store(store_id):
    try:
        store = Store.query.get_or_404(store_id)
        
        # 删除产品表
        if store.table_name:
            try:
                # 删除产品表
                sql = text(f'DROP TABLE IF EXISTS {store.table_name}')
                db.session.execute(sql)
                
                # 删除产品列表表
                list_products_table = f'list_products_{store.seller_id}'
                sql = text(f'DROP TABLE IF EXISTS {list_products_table}')
                db.session.execute(sql)
                
                db.session.commit()
                logger.info(f"成功删除相关表: {store.table_name}, {list_products_table}")
            except Exception as e:
                db.session.rollback()
                logger.error(f"删除相关表失败: {str(e)}")
                return jsonify({'error': f'删除相关表失败: {str(e)}'}), 400
        
        # 删除店铺记录
        db.session.delete(store)
        db.session.commit()
        # logger.info(f"成功删除店铺: {store.name}")
        return jsonify({'message': '店铺删除成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@bp.route('/test-connection', methods=['POST'])
def test_connection():
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')
        # print(username, password)
        login_url = "https://pmpapi.pigugroup.eu/v3/login"
        response = requests.post(
            login_url, 
            json={
                "username": username,
                "password": password
            }
            # proxies={
            #     "http": "http://127.0.0.1:10809",
            #     "https": "http://127.0.0.1:10809",
            # }
        )
        # #打印请求地址请求头以及请求数据
        # print(response.request.url)
        # print(response.request.headers)
        # print(response.request.body)


        if response.status_code != 200:
            if response.status_code == 401:
                return jsonify({'error': '连接失败：用户名或密码错误'}), 400
            elif response.status_code == 400:
                return jsonify({'error': '连接失败：返回错误请检查网络环境'}), 400
            else:
                return jsonify({'error': '连接失败：服务器返回错误'}), 400


        token = response.json().get('token')
        if not token:
            return jsonify({'error': '连接失败：获取token失败请联系管理员'}), 400
            
        return jsonify({'message': '连接成功', 'token': token})
        
    except Exception as e:
        return jsonify({'error': f'连接失败：{str(e)}'}), 400 