from flask import jsonify, request
from models import Store
from . import bp
from .utils import ProductEditor

@bp.route('/update', methods=['POST'])
def update_products():
    """更新产品的路由处理函数"""
    data = request.get_json()
    store_id = data.get('store_id')
    offer_ids = data.get('offer_ids', [])
    update_data = data.get('update_data', {})
    is_batch = data.get('is_batch', False)
    
    try:
        store = Store.query.get(store_id)
        if not store:
            raise Exception("店铺不存在")
        
        editor = ProductEditor(store)
        if is_batch:
            result = editor.batch_update_products(offer_ids, update_data)
        else:
            result = editor.update_product(offer_ids[0], update_data)
            
        return jsonify(result)
            
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"更新失败: {str(e)}"
        }) 