from datetime import datetime, timedelta
from sqlalchemy import or_
from models import db, TaskCenter, UnifiedListProducts, beijing_now
import logging

logger = logging.getLogger(__name__)

class TaskService:
    """任务服务类 - 处理任务的业务逻辑"""
    
    @staticmethod
    def get_executable_count(task):
        """获取任务可执行的产品数量"""
        try:
            if task.task_type == 'translation':
                # 对于翻译任务，查找需要翻译的产品数量
                return UnifiedListProducts.query.filter(
                    UnifiedListProducts.seller_id == task.seller_id,
                    or_(
                        UnifiedListProducts.title_lt == '',
                        UnifiedListProducts.title_lv == '',
                        UnifiedListProducts.title_et == '',
                        UnifiedListProducts.title_fi == '',
                        UnifiedListProducts.description_lt == '',
                        UnifiedListProducts.description_lv == '',
                        UnifiedListProducts.description_et == '',
                        UnifiedListProducts.description_fi == ''
                    )
                ).count()
            elif task.task_type == 'publish':
                # 对于发布任务，查找待发布的产品数量，并且需要拥有四站标题和四站描述，缺一不可
                return UnifiedListProducts.query.filter(
                    UnifiedListProducts.seller_id == task.seller_id,
                    UnifiedListProducts.status.in_(['pending_edit', 'upload_failed']),
                    UnifiedListProducts.title_lt != '',
                    UnifiedListProducts.title_lv != '',
                    UnifiedListProducts.title_et != '',
                    UnifiedListProducts.title_fi != '',
                    UnifiedListProducts.description_lt != '',
                    UnifiedListProducts.description_lv != '',
                ).count()
            return 0
        except Exception as e:
            logger.error(f"获取可执行产品数量失败: {str(e)}")
            return 0

    @staticmethod
    def get_current_batch_size(task):
        """获取当前应该执行的批次大小"""
        executable_count = TaskService.get_executable_count(task)
        return min(task.batch_size, executable_count)

    @staticmethod
    def get_batch_products(task):
        """获取当前批次要处理的产品"""
        try:
            current_batch_size = TaskService.get_current_batch_size(task)
            # logger.info(f"当前批次大小: {current_batch_size}")
            
            if current_batch_size == 0:
                return []

            if task.task_type == 'translation':
                products = UnifiedListProducts.query.filter(
                    UnifiedListProducts.seller_id == task.seller_id,
                    or_(
                        UnifiedListProducts.title_lt == '',
                        UnifiedListProducts.title_lv == '',
                        UnifiedListProducts.title_et == '',
                        UnifiedListProducts.title_fi == '',
                        UnifiedListProducts.description_lt == '',
                        UnifiedListProducts.description_lv == '',
                        UnifiedListProducts.description_et == '',
                        UnifiedListProducts.description_fi == ''
                    )
                ).limit(current_batch_size).all()
                
                # logger.info(f"获取到 {len(products)} 个待翻译产品")
                return products
            elif task.task_type == 'publish':
                # 获取待发布的产品
                return UnifiedListProducts.query.filter(
                    UnifiedListProducts.seller_id == task.seller_id,
                    UnifiedListProducts.status.in_(['pending_edit', 'upload_failed'])
                ).limit(current_batch_size).all()
            # logger.info(f"获取到 {len(products)} 个待发布产品")
            return []
        except Exception as e:
            logger.error(f"获取批次产品失败: {str(e)}")
            return []

    @staticmethod
    def update_progress(task, success_count=0, failed_count=0, error_message=None):
        """
        更新任务进度和统计数据
        :param task: 任务对象
        :param success_count: 本次成功处理的数量
        :param failed_count: 本次失败处理的数量
        :param error_message: 错误信息
        """
        try:
            # 更新当前执行的统计
            task.processed_count += success_count
            task.failed_count += failed_count
            task.processing_count = max(0, task.total_count - task.processed_count - task.failed_count)
            
            # 计算进度
            total = task.total_count or 1  # 避免除以0
            task.progress = min(100, ((task.processed_count + task.failed_count) / total) * 100)
            
            # 更新错误信息
            if error_message:
                task.last_error = error_message
            
            # 如果当前批次处理完成
            if task.processing_count == 0:
                # 更新累计统计
                task.total_success_count += task.processed_count
                task.total_failed_count += task.failed_count
                
                # 检查是否还有可执行的数据
                if TaskService.should_continue(task):
                    # 重置当前执行的统计
                    task.processed_count = 0
                    task.failed_count = 0
                    task.progress = 0
                    # 更新下次执行时间
                    TaskService.update_next_run_time(task)
                else:
                    # 如果没有可执行的数据，暂停任务
                    TaskService.pause(task)
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {str(e)}")
            db.session.rollback()

    @staticmethod
    def should_continue(task):
        """判断任务是否应该继续执行"""
        if task.status != 'running':
            return False
        
        executable_count = TaskService.get_executable_count(task)
        if executable_count == 0:
            return False
            
        return True

    @staticmethod
    def pause(task):
        """暂停任务"""
        if task.status == 'running':
            task.status = 'stopped'
            task.next_run_at = None
            # 更新累计统计
            task.total_success_count += task.processed_count
            task.total_failed_count += task.failed_count
            # 重置当前执行的统计
            task.processing_count = 0
            task.processed_count = 0
            task.failed_count = 0
            task.progress = 0
            db.session.commit()
            # logger.info(f"任务 {task.id} 已暂停")

    @staticmethod
    def start_batch(task):
        """开始处理新的批次"""
        try:
            current_batch_size = TaskService.get_current_batch_size(task)
            if current_batch_size > 0:
                task.total_count = current_batch_size
                task.processing_count = current_batch_size
                task.processed_count = 0
                task.failed_count = 0
                task.progress = 0
                task.last_error = None  # 清除上次的错误信息
                db.session.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"开始新批次失败: {str(e)}")
            db.session.rollback()
            return False

    @staticmethod
    def update_next_run_time(task):
        """更新下次执行时间"""
        if task.status == 'running':
            executable_count = TaskService.get_executable_count(task)
            if executable_count > 0:
                # 基于当前时间计算下次执行时间
                task.next_run_at = beijing_now() + timedelta(minutes=task.interval)
            else:
                task.next_run_at = None
        else:
            task.next_run_at = None
        
        db.session.commit()

    @staticmethod
    def update_last_run_time(task):
        """更新最后执行时间"""
        task.last_run_at = beijing_now()
        task.execution_count += 1
        
        # 处理完成后再更新下次执行时间
        if TaskService.should_continue(task):
            TaskService.update_next_run_time(task)
        
        db.session.commit()

    @staticmethod
    def start(task):
        """启动任务"""
        executable_count = TaskService.get_executable_count(task)
        if executable_count == 0:
            logger.warning(f"任务 {task.id} 启动失败：无可执行数据")
            return False
            
        if task.status != 'running':
            task.status = 'running'
            if TaskService.start_batch(task):
                TaskService.update_next_run_time(task)
                # logger.info(f"任务 {task.id} 已启动，可执行数量：{executable_count}")
                return True
            else:
                task.status = 'stopped'
                # logger.error(f"任务 {task.id} 启动失败：无法开始批次处理")
                return False
        return True

    @staticmethod
    def stop(task):
        """停止任务"""
        if task.status == 'running':
            task.status = 'stopped'
            task.next_run_at = None
            # 更新累计统计
            task.total_success_count += task.processed_count
            task.total_failed_count += task.failed_count
            # 重置当前执行的统计
            task.processing_count = 0
            task.processed_count = 0
            task.failed_count = 0
            task.progress = 0
            db.session.commit()
            # logger.info(f"任务 {task.id} 已停止")
        return True

    @staticmethod
    def complete(task):
        """完成任务"""
        task.status = 'completed'
        task.next_run_at = None
        task.progress = 100
        # 更新累计统计
        task.total_success_count += task.processed_count
        task.total_failed_count += task.failed_count
        # 重置当前执行的统计
        task.processing_count = 0
        task.processed_count = 0
        task.failed_count = 0
        db.session.commit()
        # logger.info(f"任务 {task.id} 已完成")
        return True

    @staticmethod
    def to_dict_with_extra(task):
        """转换为字典格式，包含额外信息"""
        base_dict = task.to_dict()
        executable_count = TaskService.get_executable_count(task)
        current_batch_size = TaskService.get_current_batch_size(task)
        
        # 添加额外信息
        base_dict.update({
            'executable_count': executable_count,
            'current_batch_size': current_batch_size
        })
        
        return base_dict

    @staticmethod
    def create_task(data):
        """创建新任务"""
        try:
            task = TaskCenter(**data)
            db.session.add(task)
            db.session.commit()
            # logger.info(f"成功创建任务: {task.id}")
            return task
        except Exception as e:
            # logger.error(f"创建任务失败: {str(e)}")
            raise 