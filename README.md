# Web2.0 产品管理系统

## 项目概述
这是一个基于 Flask 的产品管理系统，主要用于管理和处理产品信息，支持多语言翻译和产品发布功能。系统采用异步任务处理机制，支持批量操作，并具有完善的任务调度和监控功能。

## 主要功能
1. 产品管理
   - 产品信息的增删改查
   - 产品状态追踪
   - 批量产品导入/导出
   - 产品分类管理

2. 多语言翻译
   - 支持多语言（LT、LV、ET、FI）翻译
   - 批量翻译功能
   - 翻译质量监控
   - 支持多种翻译服务提供商

3. 产品发布
   - 批量产品发布
   - 发布状态追踪
   - 失败重试机制
   - 发布统计报告

4. 任务中心
   - 异步任务处理
   - 任务进度监控
   - 任务状态管理
   - 定时任务调度

## 技术架构
### 后端框架
- Flask: Web 框架
- SQLAlchemy: ORM 数据库操作
- APScheduler: 任务调度系统
- aiohttp: 异步HTTP客户端
- asyncio: 异步IO处理

### 数据库
- SQLite: 本地数据存储

### 前端技术
- Bootstrap: UI框架
- jQuery: JavaScript库
- AJAX: 异步数据交互

## 项目结构
```
web2.0/
├── app.py                 # 应用入口文件
├── models.py             # 数据模型定义
├── scheduler.py          # 任务调度器
├── requirements.txt      # 项目依赖
├── static/              # 静态资源目录
├── templates/           # HTML模板目录
└── routes/              # 路由模块目录
    ├── product_list/    # 产品列表相关
    ├── task_center/     # 任务中心相关
    ├── product_publish/ # 产品发布相关
    └── settings/       # 系统设置相关
```

## 核心模块说明

### 1. 任务调度系统 (scheduler.py)
- 基于APScheduler的任务调度器
- 支持异步任务处理
- 任务状态管理和监控
- 防止任务重复执行的锁机制

### 2. 任务处理器 (handlers.py)
- 翻译任务处理
- 发布任务处理
- 异步处理机制
- 错误处理和重试机制

### 3. 数据模型 (models.py)
- 产品信息模型
- 任务中心模型
- 系统设置模型
- 数据关系管理

## 配置说明
1. 数据库配置
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///path/to/your/database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
```

2. 调度器配置
```python
app.config['SCHEDULER_API_ENABLED'] = True
app.config['SCHEDULER_TIMEZONE'] = 'Asia/Shanghai'
```

## 部署说明
1. 环境要求
   - Python 3.8+
   - pip 包管理器

2. 安装步骤
```bash
# 克隆项目
git clone [repository_url]

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
flask db upgrade

# 运行应用
python app.py
```

## 开发指南

### 1. 添加新的任务类型
1. 在 `TaskCenter` 模型中添加新的任务类型
2. 在 `handlers.py` 中实现对应的处理函数
3. 在 `scheduler.py` 中添加任务调度逻辑

### 2. 扩展翻译服务
1. 在 `translate` 目录下创建新的翻译服务类
2. 实现 `TranslatorInterface` 接口
3. 在 `translator_factory.py` 中注册新的翻译服务

### 3. 自定义产品发布逻辑
1. 修改 `product_publish.py` 中的发布逻辑
2. 更新产品状态处理机制
3. 添加新的错误处理逻辑

## 维护指南

### 1. 日志管理
- 日志位置: `logs/` 目录
- 日志级别: INFO, WARNING, ERROR
- 日志轮转策略: 按天轮转

### 2. 数据备份
- 数据库位置: `data/` 目录
- 建议定期备份数据库文件
- 保留最近30天的备份

### 3. 性能优化
- 定期清理过期任务
- 优化数据库查询
- 监控系统资源使用

## 常见问题解决

### 1. 任务执行问题
- 检查任务状态
- 查看错误日志
- 验证数据完整性

### 2. 翻译服务问题
- 检查API配置
- 验证网络连接
- 查看翻译日志

### 3. 发布失败处理
- 检查产品数据
- 验证API权限
- 查看错误详情

## 更新日志
### v1.0.0 (2024-03-05)
- 初始版本发布
- 实现基本功能
- 建立项目框架

# Web2.0 项目 Docker 部署指南

## 前提条件

- 安装 [Docker](https://docs.docker.com/get-docker/)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/)

## 快速开始

1. 克隆项目到本地：
```bash
git clone <你的项目仓库地址>
cd web2.0
```

2. 构建并启动容器：
```bash
docker-compose up -d
```

3. 访问应用：
打开浏览器访问 http://localhost:5000

## 常用命令

- 查看容器日志：
```bash
docker-compose logs -f
```

- 停止容器：
```bash
docker-compose down
```

- 重新构建镜像：
```bash
docker-compose build --no-cache
```

## 数据持久化

- 数据库文件保存在 `./data` 目录
- 日志文件保存在 `./logs` 目录

## 开发说明

1. 修改代码后，需要重新构建镜像：
```bash
docker-compose build
docker-compose up -d
```

2. 如果需要进入容器进行调试：
```bash
docker-compose exec web bash
```

## 注意事项

- 确保 5000 端口未被占用
- 首次启动可能需要一些时间来初始化数据库
- 建议定期备份 `data` 目录下的数据库文件