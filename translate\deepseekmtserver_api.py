import os
import json
import asyncio
import aiohttp
import logging
import time
from openai import AsyncOpenAI
from typing import List, Dict, Optional
from models import SystemSettings, TokenUsage, MultiModelConfig

logger = logging.getLogger(__name__)

class DeepSeekMTranServer:
    """组合翻译服务类，直接实现DeepSeek火山引擎翻译和MT翻译的功能 标题使用火山引擎翻译，描述使用MT翻译
    """
    
    def __init__(self):
        """初始化组合翻译器"""
        # 从专用设置字段加载配置
        self.deepseek_api_key = SystemSettings.get_setting('zh_deepseek_api_key', '')
        self.mtran_api_url = SystemSettings.get_setting('zh_mtran_api_key', '')
        self.translation_model = SystemSettings.get_setting('zh_translation_model', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        #self.base_url = "http://localhost:3001/proxy/tar/v1"
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"


        # 检查是否启用多模型
        multi_model = MultiModelConfig.get_available_model()
        if multi_model:
            self.deepseek_api_key = multi_model.api_key
            self.translation_model = multi_model.model_id
            self.zh_model_name = multi_model.model_name
            self.current_model = multi_model
            logger.info(f"使用组合模式-多模型模式，组合模式-模型别名: {self.zh_model_name}, 组合模式-模型ID: {self.translation_model}, 组合模式-API Key: {self.deepseek_api_key}")
        else:
            # 检查是否是因为所有模型都超出限制
            any_enabled_model = MultiModelConfig.query.filter_by(enabled=True).first()
            if any_enabled_model and not MultiModelConfig.query.filter_by(enabled=True, is_active=True).first():
                raise Exception("多模型额度用尽，请添加额外模型")
            
            # 使用默认设置 此时是组合模式下是应该使用组合的api_key和model_id
            self.deepseek_api_key = SystemSettings.get_setting('zh_deepseek_api_key', '')
            self.translation_model = SystemSettings.get_setting('zh_translation_model', '')
            self.current_model = None
            logger.info(f"使用组合模式-默认设置，组合模式-模型ID: {self.translation_model}, 组合模式-API Key: {self.deepseek_api_key}")
        
        # 创建一个长期存在的客户端实例
        self.client = AsyncOpenAI(
            api_key=self.deepseek_api_key,
            base_url=self.base_url,
        )
        # 设置标题提示词
        #self._title_prompt = """你是电商标题优化翻译专家。请将输入的内容翻译成四种语言并以JSON格式输出。\n\n目标语言：立陶宛语(LT)、拉脱维亚语(LV)、爱沙尼亚语(ET)、芬兰语(FI)\n\n标题处理规则：\n1. 提取并重组信息：\n   - 主要产品名称（必须）\n   - 数量信息（如有）\n   - 颜色/材质（如有）\n   - 用途/场合（如有）\n   - 如果产品有多个用途，只保留最主要的用途\n   - 如果产品标题有知名品牌信息或者产品兼容型号，那么输出的内容中在品牌前或者型号前加上'兼容于'这个信息，并且要带上品牌或者型号，因为知名品牌会侵权，只能销售配件而不是销售成品\n\n2. 标题格式化：\n   - 产品名称+数量+材质/颜色+用途+兼容型号或品牌(型号或品牌如果存在则加上,否则不加)\n   - 删除不必要的修饰词\n   - 确保语法正确\n   - 不要带有.,!等标点符号\n\n3. 长度控制：\n   - 保持简洁（建议20-40字符）\n   - 突出关键信息\n\n输出格式要求：\n1. 必须使用标准JSON格式\n2. 包含且仅包含四个语言代码的键值对\n3. 示例输出：\n{\n    "LT": "Moteriškas raudonas sijonas",\n    "LV": "Sarkani sieviešu zīda svārki",\n    "ET": "Punane naiste siidiseelik",\n    "FI": "Punainen naisten silkkihame"\n}\n\n请确保输出的是可解析的JSON格式，无需进行任何解释。"""
        self._title_prompt = """
请将以下产品标题翻译并重写为指定语言版本的标题，并严格按照以下标题规范进行处理： 【平台标题格式要求】（请注意！！一定要严格按顺序，如果有数量以数量作为结尾） 产品类型 + 适用对象（如有）+ 品牌（如有）+ 系列/产品线（如有）+ 颜色（如有）+ 数量（如有） ⚠ 规则说明： 1. 标题中最多保留 1~2 个核心关键词，不允许关键词堆砌。按照平台标题格式以小写逗号分隔； 2. 如果英文原始标题中没有出现某个字段（如适用对象/系列/颜色等），可以省略，不要编造或从其他字段推测； 3. 允许为了符合标题格式要求而删减冗余词语、修饰词或营销词汇（例如“High Quality”、“Perfect for travel”应删除）； 4. 保持品牌名、型号等不翻译、原样保留； 5. 使用目标语言中适当的单位（例如：pcs → vnt.（LT），gab.（LV）等）； 6. 输出语言应简洁自然、符合当地书写习惯，不要全部大写； 示例（目标语言为立陶宛语）： * 英文标题：Razor heads GILLETTE FUSION Proglide 8 pcs → 输出：Skutimosi peiliukai GILLETTE FUSION Proglide, 8 vnt. * 英文标题：4 set Butter Silicone Spatula Small Bulk(Color) → 输出：Spatula BUTTER Silicone, mėlyna, 4 vnt.  以下是需要翻译的标题列表，请根据上方规则进行处理，并输出翻译后的立陶宛语、拉脱维亚语、爱沙尼亚语、芬兰语标题。输出仅包含四个键的JSON对象：
```json
{
    "LT": "翻译标题",
    "LV": "翻译标题",
    "ET": "翻译标题",
    "FI": "翻译标题"
}
"""


    def refresh_settings(self):
        """刷新设置，只更新认证信息和模型ID，不重新创建客户端"""
        # 重新获取组合模式的特殊配置
        self.deepseek_api_key = SystemSettings.get_setting('zh_deepseek_api_key', '')
        self.mtran_api_url = SystemSettings.get_setting('zh_mtran_api_key', '')
        self.translation_model = SystemSettings.get_setting('zh_translation_model', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        
        # 检查是否启用多模型
        multi_model = MultiModelConfig.get_available_model()
        if multi_model:
            self.deepseek_api_key = multi_model.api_key
            self.translation_model = multi_model.model_id
            self.current_model = multi_model    
        else:
            # 使用默认设置
            self.deepseek_api_key = SystemSettings.get_setting('zh_deepseek_api_key', '')
            self.translation_model = SystemSettings.get_setting('zh_translation_model', '')
            self.current_model = None
        
        # 更新客户端的认证信息
        self.client.api_key = self.deepseek_api_key
    #================ 火山引擎翻译相关方法 ================
    
    async def _translate_with_huoshan(self, text: str) -> Dict:
        """指定客户端使用火山引擎翻译标题文本"""
        try:
            # 每次翻译前刷新设置 但不重新创建客户端
            start_time = time.time()
            self.refresh_settings()

            # logger.info(f"发送火山引擎翻译请求，文本长度: {len(text)}, 模型ID: {self.translation_model}")

            # 打印请求URL和参数
            request_url = f"{self.base_url}/chat/completions"
            request_params = {
                "model": self.translation_model,
                "messages": [
                    {"role": "system", "content": self._title_prompt},
                    {"role": "user", "content": f"Translate this title: {text}"}
                ]
            }
            #print(f"=== 火山引擎翻译请求信息 ===")
            #print(f"请求URL: {request_url}")
            #print(f"请求参数: {request_params}")

            completion = await self.client.chat.completions.create(
                model=self.translation_model,
                messages=[
                    {"role": "system", "content": self._title_prompt},
                    {"role": "user", "content": f"Translate this title: {text}"}
                ],
                stream=False
            )

            # 打印响应信息
            #print(f"响应信息: {completion}")

            result = completion.choices[0].message.content

            # 打印火山引擎翻译结果
            #logger.info(f"火山引擎翻译结果: {result}")
            
            # 记录 token 使用情况
            request_tokens = completion.usage.prompt_tokens
            response_tokens = completion.usage.completion_tokens
            total_tokens = completion.usage.total_tokens

            # 如果是多模型模式，更新对应模型的使用量
            if self.current_model:
                self.current_model.update_token_usage(total_tokens)
                # 如果当前模型超出限制，自动切换到下一个可用模型
                if not self.current_model.is_active:
                    self.refresh_settings()
            
            # 记录token统计
            TokenUsage.add_usage(request_tokens, response_tokens, api_provider='deepseek_huoshan')
            
            
            # 打印请求耗时
            end_time = time.time()
            # logger.info(f"火山引擎翻译请求耗时: {end_time - start_time:.2f}秒")
            
            try:
                # 清理可能存在的Markdown代码块标记
                cleaned_result = self._clean_json_response(result)
                # 解析JSON响应
                translated_data = json.loads(cleaned_result)
                # 验证返回的数据包含所有必需的语言代码
                required_langs = ['LT', 'LV', 'ET', 'FI']
                
                # 检查所有必要的语言是否都已翻译
                missing_langs = [lang for lang in required_langs if lang not in translated_data]
                
                if not missing_langs:
                    # 所有语言都已翻译
                    return {
                        'success': True,
                        'text': translated_data,
                        'usage': {
                            'request_tokens': request_tokens,
                            'response_tokens': response_tokens,
                            'total_tokens': total_tokens
                        }
                    }

                else:
                    # 记录缺失的语言
                    error_msg = f'缺少语言翻译: {", ".join(missing_langs)}'
                    logger.error(error_msg)
                    return {
                        'success': False,
                        'error': error_msg,
                        'usage': {
                            'request_tokens': request_tokens,
                            'response_tokens': response_tokens,
                            'total_tokens': total_tokens
                        }
                    }
            except json.JSONDecodeError as e:
                error_msg = f'无效的JSON响应: {str(e)}，原始响应: {result}'
                # logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'usage': {
                        'request_tokens': request_tokens,
                        'response_tokens': response_tokens,
                        'total_tokens': total_tokens
                    }
                }
        except Exception as e:
            error_msg = f"火山引擎翻译失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def _clean_json_response(self, response_text):
        """清理响应中可能存在的Markdown代码块标记
        
        Args:
            response_text (str): 原始响应文本
            
        Returns:
            str: 清理后的JSON字符串
        """
        # 检查是否包含Markdown代码块标记
        if response_text.startswith("```") and response_text.endswith("```"):
            # 移除开头的```json或```标记
            if "```json" in response_text[:10]:
                response_text = response_text[7:]  # 移除```json
            else:
                response_text = response_text[3:]  # 移除```
                
            # 移除结尾的```标记
            response_text = response_text[:-3].strip()
        
        # 处理其他可能的格式问题
        response_text = response_text.strip()
        
        return response_text
    
    #================ MT翻译相关方法 ================
    
    async def _translate_with_mt(self, text: str, source_lang: str = "en", target_lang: str = "lt", content_type: str = "description") -> Dict:
        """使用MT翻译描述文本
        """
        try:
            # 确保语言代码是小写的
            source_lang = source_lang.lower()
            target_lang = target_lang.lower()

            request_data = {
                "from": source_lang,
                "to": target_lang,
                "text": text
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.mtran_api_url}/translate",
                    json=request_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        # 确保返回的是字符串类型
                        if isinstance(result, dict) and 'result' in result:
                            translated_text = result['result']
                            if not translated_text or translated_text == '[object Object]':
                                raise Exception(f"翻译结果异常")
                        else:
                            translated_text = text
                        
                        return {
                            'success': True,
                            'text': translated_text
                        }
                    else:
                        error_text = await response.text()
                        logger.info(f"MT翻译请求失败: {error_text}")
                        return {
                            'success': False,
                            'error': f'翻译请求失败: {error_text}'
                        }
        except Exception as e:
            logger.error(f"MT翻译失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _mt_translate_product_async(self, text, source_lang="en", target_langs=["lt", "lv", "et", "fi"], content_type="description"):
        """MT翻译产品描述到四个目标语言
        
        在组合翻译中，此方法只用于翻译描述文本
        """
        translations = {lang.upper(): None for lang in target_langs}
        failed_langs = []
        start_time = time.time()
        # logger.info(f"开始批量翻译描述到多语言: 文本长度={len(text)}, 目标语言={target_langs}")
        
        # 为每个目标语言创建翻译任务
        tasks = []
        for lang in target_langs:
            tasks.append(self._translate_with_mt(text, source_lang, lang, content_type))
        
        # 并行执行所有翻译任务
        results = await asyncio.gather(*tasks)
        
        # 处理结果
        for i, lang in enumerate(target_langs):
            result = results[i]
            if result['success']:
                translations[lang.upper()] = result['text']
            else:
                failed_langs.append(lang)
                logger.warning(f"翻译到 {lang} 失败，将重试")
        
        end_time = time.time()
        # logger.info(f"mt翻译描述到多语言耗时: {end_time - start_time:.2f}秒")

        # 重试失败的语言
        for lang in failed_langs:
            for retry in range(self.retry_count):
                logger.info(f"重试翻译到 {lang}，第 {retry+1} 次")
                await asyncio.sleep(self.retry_interval)
                result = await self._translate_with_mt(text, source_lang, lang, content_type)
                if result['success']:
                    translations[lang.upper()] = result['text']
                    logger.info(f"重试翻译到 {lang} 成功")
                    break
                else:
                    logger.warning(f"重试翻译到 {lang} 失败: {result.get('error')}")
            
            if not translations[lang.upper()]:
                logger.error(f"翻译到 {lang} 失败，已达到最大重试次数")
        
        return translations
    
    #================ 组合翻译API方法 ================
        
    def translate(self, text: str, source_lang: str = "AUTO", target_lang: str = "auto", content_type: str = "title") -> Dict:
        """翻译文本，适用于单个文本翻译
        
        标题使用DeepSeek火山引擎翻译
        描述使用MTran服务翻译
        """
        logger.debug(f"组合翻译服务 - 翻译请求: content_type={content_type}, target_lang={target_lang}, 文本长度={len(text)}")
        
        # 刷新设置
        self.refresh_settings()
        
        try:
            # 根据内容类型选择翻译器
            if content_type.lower() == 'title':
                # logger.info(f"使用DeepSeek火山引擎翻译标题")
                
                # 创建新的事件循环
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                
                try:
                    # 使用火山引擎翻译
                    result = new_loop.run_until_complete(self._translate_with_huoshan(text))
                finally:
                    new_loop.close()
                
                if result['success']:
                    # 火山引擎返回的是所有语言的翻译，需要根据target_lang处理
                    if isinstance(target_lang, str) and target_lang.upper() in result['text']:
                        # 单语言请求，返回指定语言的翻译
                        return {
                            'success': True,
                            'text': result['text'][target_lang.upper()],
                            'source_lang': source_lang,
                            'target_lang': target_lang
                        }
                    else:
                        # 返回所有语言翻译结果
                        return {
                            'success': True,
                            'text': result['text'],
                            'source_lang': source_lang,
                            'target_lang': 'all'
                        }
                return {
                    'success': False,
                    'error': result.get('error', 'Translation failed')
                }
            else:
                logger.info(f"使用MTran服务翻译描述")
                
                # 创建新的事件循环
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                
                try:
                    # 无论传入什么content_type，都强制设为description
                    result = new_loop.run_until_complete(
                        self._translate_with_mt(text, source_lang, target_lang, content_type="description")
                    )
                finally:
                    new_loop.close()
                
            return result
        except Exception as e:
            logger.error(f"组合翻译失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    async def translate_product_full_async(self, title: str, description: str, source_lang: str = "AUTO") -> Dict:
        """异步翻译产品的标题和描述
        
        标题使用DeepSeek火山引擎翻译
        描述使用MTran服务翻译
        """
        logger.debug(f"组合翻译服务 - 产品翻译请求: 标题长度={len(title)}, 描述长度={len(description)}")
        
        # 刷新设置
        self.refresh_settings()
        
        try:
            # 创建标题翻译任务 - 使用火山引擎
            title_task = self._translate_with_huoshan(title)
            
            # 创建描述翻译任务 - 使用MT，强制使用description的content_type
            desc_task = self._mt_translate_product_async(
                description, 
                source_lang=source_lang, 
                content_type="description"  # 强制指定content_type
            )
            
            # 并行执行翻译任务
            title_result, desc_result = await asyncio.gather(title_task, desc_task)
            
            # 处理标题翻译结果
            title_translations = title_result.get('text', {}) if title_result.get('success', False) else None
            
            # 返回组合翻译结果
            return {
                'title': title_translations,
                'description': desc_result
            }
            
        except Exception as e:
            logger.error(f"组合翻译产品失败: {str(e)}")
            return {
                'title': None,
                'description': None,
                'error': str(e)
            }

    async def translate_batch_products_async(self, products_data):
        """批量翻译多个产品的标题和描述
        
        标题使用DeepSeek火山引擎翻译
        描述使用MTran服务翻译
        """
        start_time = time.time()
        # logger.info(f"=== 组合翻译服务开始批量翻译 {len(products_data)} 个产品 ===")

        # 刷新设置
        self.refresh_settings()
        
        # 存储所有产品的翻译结果
        results = []
        success_count = 0
        failed_count = 0
        
        try:
            # 将产品按标题和描述分组
            titles = []
            descriptions = []
            skus = []
            
            for product in products_data:
                sku = product.get('sku', 'unknown')
                title = product.get('title', '')
                description = product.get('description', '')
                
                # 检查输入数据
                if not title or not isinstance(title, str):
                    results.append({
                        'sku': sku,
                        'error': '标题为空或格式错误',
                        'title': None,
                        'description': None
                    })
                    failed_count += 1
                    logger.warning(f"产品 {sku} 标题为空或格式错误，跳过翻译")
                    continue
                
                skus.append(sku)
                titles.append(title)
                descriptions.append(description or "")
            
            if not titles:
                logger.warning("没有有效的产品数据需要翻译")
                return results
            
            # 创建批量翻译任务
            title_tasks = []
            desc_tasks = []
            
            # 为所有标题创建翻译任务
            for title in titles:
                title_tasks.append(self._translate_with_huoshan(title))
            
            # 为所有描述创建翻译任务
            for description in descriptions:
                if description:  # 只处理非空描述
                    desc_tasks.append(self._mt_translate_product_async(
                        description,
                        source_lang="EN",
                        content_type="description"
                    ))
                else:
                    desc_tasks.append(None)
            
            # 并行执行所有翻译任务
            title_results = await asyncio.gather(*title_tasks, return_exceptions=True)
            desc_results = []
            
            # 处理描述翻译任务
            for task in desc_tasks:
                if task:
                    try:
                        result = await task
                        desc_results.append(result)
                    except Exception as e:
                        desc_results.append(None)
                        logger.error(f"描述翻译失败: {str(e)}")
                else:
                    desc_results.append(None)
            
            # 处理翻译结果
            for i, sku in enumerate(skus):
                try:
                    title_result = title_results[i]
                    desc_result = desc_results[i]
                    
                    if isinstance(title_result, Exception):
                        raise title_result
                    
                    if title_result.get('success', False):
                        title_translations = title_result.get('text', {})
                    else:
                        raise Exception(title_result.get('error', '标题翻译失败'))
                    
                    results.append({
                        'sku': sku,
                        'title': title_translations,
                        'description': desc_result
                    })
                    success_count += 1
                    logger.debug(f"产品 {sku} 翻译成功")
                except Exception as e:
                    # logger.error(f"产品 {sku} 翻译失败: {str(e)}")
                    results.append({
                        'sku': sku,
                        'error': str(e),
                        'title': None,
                        'description': None
                    })
                    failed_count += 1
        
        except Exception as e:
            logger.error(f"批量翻译过程中发生错误: {str(e)}")
            return results
        
        finally:
            # 记录统计信息
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"=== 组合翻译服务批量翻译统计 ===")
            logger.info(f"总产品数: {len(products_data)}")
            logger.info(f"成功数: {success_count}")
            logger.info(f"失败数: {failed_count}")
            logger.info(f"总耗时: {duration:.2f}秒")
            logger.info(f"平均每个产品耗时: {(duration/len(products_data) if products_data else 0):.2f}秒")
        
        return results

if __name__ == "__main__":
    translator = DeepSeekMTranServer()
