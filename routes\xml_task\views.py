import json
import os
from flask import Blueprint, request, jsonify, send_file, current_app
from sqlalchemy import desc, and_, text
from models import db, XmlConversionTask, UnifiedListProducts, Store, SystemSettings
import uuid
from datetime import datetime
import xml.etree.ElementTree as ET
from xml.dom import minidom
from . import xml_task_bp
import aiohttp
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

# 加载分类数据
CATEGORIES_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'json', 'categories.json')
TEMPLATE_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'docs', 'XmlProduct.xml')

with open(CATEGORIES_FILE, 'r', encoding='utf-8') as f:
    CATEGORIES = json.load(f)

# 加载XML模板
with open(TEMPLATE_FILE, 'r', encoding='utf-8') as f:
    TEMPLATE_XML = f.read()

# 添加分页函数
def paginate(query, page, per_page):
    """通用分页函数"""
    # 获取总记录数
    total = query.count()
    
    # 计算总页数
    total_pages = (total + per_page - 1) // per_page if total > 0 else 0
    
    # 确保页码在有效范围内
    page = max(1, min(page, total_pages)) if total_pages > 0 else 1
    
    # 获取当前页的记录
    items = query.offset((page - 1) * per_page).limit(per_page).all()
    
    # 转换为字典列表
    if items and hasattr(items[0], 'to_dict'):
        items = [item.to_dict() for item in items]
    else:
        items = []
    
    return {
        'items': items,
        'total': total,
        'page': page,
        'per_page': per_page,
        'total_pages': total_pages
    }

# XML输出目录
XML_OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'xmloutput')
# 确保目录存在
os.makedirs(XML_OUTPUT_DIR, exist_ok=True)

UPLOAD_FOLDER = 'uploads/xml'

def generate_xml_file(products, file_path):
    """使用模板生成XML文件"""
    # 读取模板文件
    with open(TEMPLATE_FILE, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 提取模板中的单个产品模板
    product_template = template_content.split('<product>')[1].split('</product>')[0]
    
    # 创建产品XML字符串列表
    product_xmls = []
    
    for product in products:
        # 准备图片XML
        images_xml = ''
        for img_field in ['image_url1', 'image_url2', 'image_url3', 'image_url4', 'image_url5']:
            if getattr(product, img_field):
                images_xml += f'''                    <image>
                        <url>{getattr(product, img_field)}</url>
                    </image>
'''
        # 移除最后一个多余的换行
        images_xml = images_xml.rstrip()
        
        # 准备替换数据
        replace_dict = {
            '{{category_id}}': str(product.category_id or ''),
            '{{category_name}}': CATEGORIES.get(str(product.category_id), ''),
            '{{title}}': product.title_lt or '',
            '{{title_lv}}': product.title_lv or '',
            '{{title_ee}}': product.title_et or '',
            '{{title_fi}}': product.title_fi or '',
            '{{description}}': (product.description_lt or '').replace('\n', '<br>'),
            '{{description_lv}}': (product.description_lv or '').replace('\n', '<br>'),
            '{{description_ee}}': (product.description_et or '').replace('\n', '<br>'),
            '{{description_fi}}': (product.description_fi or '').replace('\n', '<br>'),
            '{{images}}': images_xml,
            '{{weight}}': str(product.package_weight if product.package_weight else 0.2),
            '{{length}}': str(product.package_length if product.package_length else 0.2),
            '{{height}}': str(product.package_height if product.package_height else 0.1),
            '{{width}}': str(product.package_width if product.package_width else 0.1),
            '{{barcode}}': product.ean or '',
            '{{supplier_code}}': product.sku or ''
        }
        
        # 使用模板生成产品XML
        product_xml = product_template
        for key, value in replace_dict.items():
            product_xml = product_xml.replace(key, value)
        
        product_xmls.append(product_xml)
    
    # 组合最终的XML
    final_xml = '<products>\n'
    for product_xml in product_xmls:
        final_xml += '    <product>' + product_xml + '</product>\n'
    final_xml += '</products>'
    
    # 写入文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(final_xml)

@xml_task_bp.route('/', methods=['GET'])
def get_xml_tasks():
    """获取XML转换任务列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 30, type=int)
        
        # 筛选条件
        filters = {}
        
        # 店铺筛选
        seller_id = request.args.get('seller_id')
        if seller_id:
            filters['seller_id'] = seller_id
            
        # 状态筛选
        status = request.args.get('status')
        if status:
            filters['status'] = status
            
        # 日期筛选
        date_start = request.args.get('date_start')
        date_end = request.args.get('date_end')
        
        query = XmlConversionTask.query
        
        if filters:
            for key, value in filters.items():
                query = query.filter(getattr(XmlConversionTask, key) == value)
                
        if date_start:
            query = query.filter(XmlConversionTask.create_time >= f"{date_start} 00:00:00")
        if date_end:
            query = query.filter(XmlConversionTask.create_time <= f"{date_end} 23:59:59")
            
        # 按创建时间倒序排序
        query = query.order_by(desc(XmlConversionTask.create_time))
        
        # 分页结果
        paginated_data = paginate(query, page, per_page)
        
        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': paginated_data
        })
    except Exception as e:
        current_app.logger.error(f"获取任务列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取任务列表失败: {str(e)}'
        }), 500

@xml_task_bp.route('/', methods=['POST'])
def create_task():
    """创建新的XML转换任务"""
    try:
        data = request.get_json()
        seller_id = data.get('seller_id')
        product_count = data.get('product_count', 10)
        
        if not seller_id:
            return jsonify({'code': 400, 'message': '缺少seller_id参数'}), 400
            
        # 查询可转换的产品
        products = UnifiedListProducts.query.filter(
            and_(
                UnifiedListProducts.seller_id == seller_id,
                UnifiedListProducts.status == 'pending_edit',
                UnifiedListProducts.title_lt.isnot(None),
                UnifiedListProducts.title_lt != '',
                UnifiedListProducts.title_lv.isnot(None),
                UnifiedListProducts.title_lv != '',
                UnifiedListProducts.title_et.isnot(None),
                UnifiedListProducts.title_et != '',
                UnifiedListProducts.title_fi.isnot(None),
                UnifiedListProducts.title_fi != '',
                UnifiedListProducts.description_lt.isnot(None),
                UnifiedListProducts.description_lt != '',
                UnifiedListProducts.description_lv.isnot(None),
                UnifiedListProducts.description_lv != '',
                UnifiedListProducts.description_et.isnot(None),
                UnifiedListProducts.description_et != '',
                UnifiedListProducts.description_fi.isnot(None),
                UnifiedListProducts.description_fi != ''
            )
        ).limit(product_count).all()
        
        if not products:
            return jsonify({'code': 400, 'message': '没有可转换的产品'}), 400
            
        # 生成唯一的任务ID和文件名
        task_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        file_name = f"XML_{seller_id}_{timestamp}.xml"
        file_path = os.path.join(XML_OUTPUT_DIR, file_name)
        
        try:
            # 生成XML文件
            generate_xml_file(products, file_path)
        except Exception as e:
            current_app.logger.error(f"生成XML文件失败: {str(e)}")
            return jsonify({'code': 500, 'message': f'生成XML文件失败: {str(e)}'}), 500
        
        # 创建任务记录
        task = XmlConversionTask(
            task_id=task_id,
            seller_id=seller_id,
            product_count=len(products),
            file_name=file_name,
            file_path=file_path,
            status='completed',  # 直接设置为完成状态
            create_time=datetime.now(),
            complete_time=datetime.now()
        )
        
        db.session.add(task)
        db.session.commit()
        for product in products:
            product.status = 'xml_import'
        db.session.commit()
        
        return jsonify({
            'code': 200,
            'message': '任务创建成功',
            'data': task.to_dict()
        })
        
    except Exception as e:
        current_app.logger.error(f"创建任务失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'创建任务失败: {str(e)}'}), 500

@xml_task_bp.route('/<task_id>', methods=['GET'])
def get_task(task_id):
    """获取任务详情"""
    try:
        task = XmlConversionTask.query.filter_by(task_id=task_id).first()
        if not task:
            return jsonify({'code': 404, 'message': '任务不存在'}), 404
            
        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': task.to_dict()
        })
        
    except Exception as e:
        current_app.logger.error(f"获取任务失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'获取任务失败: {str(e)}'}), 500

@xml_task_bp.route('/<task_id>/download', methods=['GET'])
def download_xml_file(task_id):
    """下载XML文件"""
    try:
        task = XmlConversionTask.query.filter_by(task_id=task_id).first()
        if not task:
            return jsonify({
                'code': 1,
                'message': '任务不存在'
            })
            
        if task.status != 'completed':
            return jsonify({
                'code': 1,
                'message': '任务尚未完成'
            })
            
        if not os.path.exists(task.file_path):
            return jsonify({
                'code': 1,
                'message': '文件不存在'
            })
            
        return send_file(
            task.file_path,
            as_attachment=True,
            download_name=task.file_name,
            mimetype='application/xml'
        )
    except Exception as e:
        return jsonify({
            'code': 1,
            'message': str(e)
        })

@xml_task_bp.route('/<task_id>', methods=['DELETE'])
def delete_task(task_id):
    """删除任务"""
    try:
        task = XmlConversionTask.query.filter_by(task_id=task_id).first()
        if not task:
            return jsonify({
                'code': 404,
                'message': '任务不存在'
            })
            
        # 如果文件存在,删除文件
        if task.file_path and os.path.exists(task.file_path):
            try:
                os.remove(task.file_path)
            except:
                pass
                
        # 删除任务记录
        db.session.delete(task)
        db.session.commit()
        
        return jsonify({
            'code': 200,
            'message': '删除成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'message': str(e)
        })

@xml_task_bp.route('/available-products/', methods=['GET'])
def get_available_products():
    """获取店铺可转换的产品数量"""
    try:
        seller_id = request.args.get('seller_id')
        if not seller_id:
            return jsonify({'code': 400, 'message': '缺少seller_id参数'}), 400

        # 查询满足条件的产品数量
        count = UnifiedListProducts.query.filter(
            and_(
                UnifiedListProducts.seller_id == seller_id,
                UnifiedListProducts.status == 'pending_edit',
                UnifiedListProducts.title_lt.isnot(None),
                UnifiedListProducts.title_lt != '',
                UnifiedListProducts.title_lv.isnot(None),
                UnifiedListProducts.title_lv != '',
                UnifiedListProducts.title_et.isnot(None),
                UnifiedListProducts.title_et != '',
                UnifiedListProducts.title_fi.isnot(None),
                UnifiedListProducts.title_fi != '',
                UnifiedListProducts.description_lt.isnot(None),
                UnifiedListProducts.description_lt != '',
                UnifiedListProducts.description_lv.isnot(None),
                UnifiedListProducts.description_lv != '',
                UnifiedListProducts.description_et.isnot(None),
                UnifiedListProducts.description_et != '',
                UnifiedListProducts.description_fi.isnot(None),
                UnifiedListProducts.description_fi != ''
            )
        ).count()

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'count': count
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取可转换产品数量失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取可转换产品数量失败: {str(e)}'
        }), 500

async def check_single_barcode(session, ean, token):
    """异步查询单个EAN码信息"""
    try:
        url = "https://pmpapi.pigugroup.eu/v3/products/product-modifications/barcodes"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        # 构建请求参数
        params = {
            "ean": ean
        }
        
        # 获取代理设置
        PROXY_HOST = SystemSettings.get_setting('proxy_host')
        PROXY_PORT = SystemSettings.get_setting('proxy_port')
        PROXY_URL = f"http://{PROXY_HOST}:{PROXY_PORT}"
        
        try:
            async with session.get(url, headers=headers, params=params, proxy=PROXY_URL) as response:
                # 如果状态码是200，更新产品状态为xml_success
                if response.status == 200:
                    # 查找对应的产品并更新状态
                    product = UnifiedListProducts.query.filter_by(ean=ean).first()
                    if product:
                        product.status = 'xml_success'
                    db.session.commit()
                
                # 返回状态码和EAN
                return {
                    'status_code': response.status,
                    'ean': ean
                }
                    
        except Exception as e:
            return {
                'status_code': 0,  # 使用0表示请求异常
                'ean': ean
            }
            
    except Exception as e:
        return {
            'status_code': 0,  # 使用0表示请求异常
            'ean': ean
        }

def extract_eans_from_xml(xml_file_path):
    """从XML文件中提取所有EAN码"""
    try:
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
        
        eans = []
        for product in root.findall('.//product'):
            barcode = product.find('.//barcode')
            if barcode is not None and barcode.text:
                eans.append(barcode.text.strip())
                    
        return eans
    except Exception as e:
        current_app.logger.error(f"从XML文件提取EAN码失败: {str(e)}")
        return []

def chunk_list(lst, chunk_size):
    """将列表分割成指定大小的批次"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

async def process_ean_batch(eans, token):
    """处理一批EAN码查询"""
    async with aiohttp.ClientSession() as session:
        tasks = []
        for ean in eans:
            task = check_single_barcode(session, ean, token)
            tasks.append(task)
        return await asyncio.gather(*tasks)

@xml_task_bp.route('/<task_id>/check-barcodes', methods=['GET'])
def check_task_barcodes(task_id):
    """检查任务中的EAN码信息"""
    try:
        # 获取任务信息
        task = XmlConversionTask.query.filter_by(task_id=task_id).first()
        if not task:
            return jsonify({'code': 404, 'message': '任务不存在'}), 404
            
        if not os.path.exists(task.file_path):
            return jsonify({'code': 404, 'message': 'XML文件不存在'}), 404
            
        # 从XML文件中提取所有EAN码
        all_eans = extract_eans_from_xml(task.file_path)
        if not all_eans:
            return jsonify({'code': 400, 'message': '未找到有效的EAN码'}), 400
            
        # 获取店铺信息
        store = Store.query.filter_by(seller_id=task.seller_id).first()
        if not store:
            return jsonify({'code': 404, 'message': '未找到店铺信息'}), 404
            
        # 获取token
        token = store.get_valid_token()
        if not token:
            return jsonify({'code': 400, 'message': '获取token失败'}), 400

        # 将EAN列表分成200个一批
        ean_batches = chunk_list(all_eans, 100)
        all_results = []
            
        # 使用ThreadPoolExecutor来运行异步代码
        with ThreadPoolExecutor() as executor:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 处理每一批EAN码查询
                for batch in ean_batches:
                    # 处理当前批次
                    batch_results = loop.run_until_complete(process_ean_batch(batch, token))
                    all_results.extend(batch_results)
                    
                    # 如果还有下一批，等待3秒
                    if batch != ean_batches[-1]:
                        time.sleep(3)
            finally:
                loop.close()
                
        # 统计不同状态码的数量
        created_count = 0      # 200状态码：已创建
        not_created_count = 0  # 404状态码：未创建
        rate_limit_count = 0   # 429状态码：请求频率限制
        error_count = 0        # 其他状态码：EAN错误或其他错误
        
        for result in all_results:
            status_code = result['status_code']
            if status_code == 200:
                created_count += 1
            elif status_code == 404:
                not_created_count += 1
            elif status_code == 429:
                rate_limit_count += 1
            else:
                error_count += 1
                
        return jsonify({
            'code': 200,
            'message': '查询完成',
            'data': {
                'total_eans': len(all_eans),
                'created_count': created_count,          # 已创建的产品数量
                'not_created_count': not_created_count,  # 未创建的产品数量
                'rate_limit_count': rate_limit_count,    # 请求频率限制数量
                'error_count': error_count,              # EAN错误数量
                'batch_count': len(ean_batches)          # 总批次数
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"检查EAN码失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'检查EAN码失败: {str(e)}'
        }), 500 