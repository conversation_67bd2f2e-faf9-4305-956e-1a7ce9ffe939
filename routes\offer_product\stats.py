from flask import jsonify, request
from models import UnifiedStoreofferProducts
from . import bp
import logging

logger = logging.getLogger(__name__)

@bp.route('/stats')
def get_dashboard_stats():
    """获取仪表盘统计数据的路由处理函数"""
    try:
        store_id = request.args.get('store_id')
        query = UnifiedStoreofferProducts.query
        
        if store_id:
            query = query.filter(UnifiedStoreofferProducts.seller_id == store_id)
        
        # 获取总数
        total = query.count()
        
        # 获取活跃数量
        active = query.filter(UnifiedStoreofferProducts.status == 'active').count()
        
        # 获取不活跃数量
        inactive = query.filter(UnifiedStoreofferProducts.status == 'inactive').count()
        
        # 获取购物车匹配数量（价格等于购物车价格的商品）
        buybox_match = query.filter(
            UnifiedStoreofferProducts.sell_price == UnifiedStoreofferProducts.buybox_price
        ).count()
        
        return jsonify({
            'total': total,
            'active': active,
            'inactive': inactive,
            'buybox_match': buybox_match
        })
    except Exception as e:
        # logger.error(f"获取统计数据失败: {str(e)}")
        return jsonify({'error': str(e)}), 500 