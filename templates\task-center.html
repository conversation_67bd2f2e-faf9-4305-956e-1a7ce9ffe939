{% extends "base.html" %}

{% block title %}商品在线管理 - 任务中心{% endblock %}

{% block styles %}
<style>
    .dashboard-card {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 2rem);
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }

    .fixed-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem;
        background: white;
    }

    .scrollable-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
        width: 100%;
    }

    /* 表格样式 */
    .task-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        table-layout: fixed;
    }

    .task-table thead {
        background-color: #f9fafb;
    }

    .task-table th {
        padding: 0.75rem 1rem;
        text-align: left;
        font-size: 0.75rem;
        font-weight: 500;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 1px solid #e5e7eb;
        white-space: nowrap;
    }

    .task-table td {
        padding: 0.75rem 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
        font-size: 0.875rem;
        color: #374151;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 0;
        vertical-align: middle;
    }

    /* 设置各列的宽度和对齐方式 */
    .task-table th:nth-child(1) { width: 10%; text-align: left; }   /* 店铺名称/任务类型 */
    .task-table th:nth-child(2) { width: 10%; text-align: left; }   /* 执行间隔/任务优先级 */
    .task-table th:nth-child(3) { width: 8%; text-align: center; }  /* 可执行数量 */
    .task-table th:nth-child(4) { width: 12%; text-align: center; } /* 当次执行进度 */
    .task-table th:nth-child(5) { width: 8%; text-align: center; }  /* 总执行次数 */
    .task-table th:nth-child(6) { width: 12%; text-align: center; } /* 累计处理成功/失败 */
    .task-table th:nth-child(7) { width: 12%; text-align: center; } /* 创建时间/最后执行 */
    .task-table th:nth-child(8) { width: 12%; text-align: center; } /* 下次执行时间 */
    .task-table th:nth-child(9) { width: 6%; text-align: center; }  /* 状态 */
    .task-table th:nth-child(10) { width: 5%; text-align: center; } /* 操作 */

    /* 对应的td也需要相同的对齐方式 */
    .task-table td:nth-child(1) { text-align: left; }
    .task-table td:nth-child(2) { text-align: left; }
    .task-table td:nth-child(3) { text-align: center; }
    .task-table td:nth-child(4) { text-align: center; }
    .task-table td:nth-child(5) { text-align: center; }
    .task-table td:nth-child(6) { text-align: center; }
    .task-table td:nth-child(7) { text-align: center; }
    .task-table td:nth-child(8) { text-align: center; }
    .task-table td:nth-child(9) { text-align: center; }
    .task-table td:nth-child(10) { text-align: center; }

    /* 数字列的样式 */
    .task-table td .number {
        font-family: 'SF Mono', Monaco, Consolas, monospace;
        font-size: 0.875rem;
    }

    /* 状态标签样式优化 */
    .status-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        min-width: 72px;
    }

    .status-running {
        background-color: #ecfdf5;
        color: #059669;
    }

    .status-stopped {
        background-color: #fef2f2;
        color: #dc2626;
    }

    .status-completed {
        background-color: #eff6ff;
        color: #1d4ed8;
    }

    .status-executing {
        background-color: #e0f7fa;
        color: #00796b;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 50;
    }

    .modal.show {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        color: #374151;
    }

    .form-select, .form-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        color: #374151;
        background-color: white;
    }

    .form-select:focus, .form-input:focus {
        outline: none;
        ring: 2px;
        ring-color: #3b82f6;
        border-color: #3b82f6;
    }

    /* 操作按钮样式 */
    .action-button {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        background: transparent;
    }

    .action-button.start {
        color: #059669;
    }

    .action-button.stop {
        color: #dc2626;
    }

    .action-button.delete {
        color: #dc2626;
    }

    .action-button:hover {
        background-color: #f3f4f6;
    }

    /* 店铺信息样式 */
    .store-info {
        font-size: 0.875rem;
    }

    .store-id {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .store-stats {
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: #6b7280;
    }

    /* 错误信息提示样式 */
    .error-tooltip {
        position: relative;
        cursor: help;
    }

    .error-tooltip:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        padding: 0.5rem;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        white-space: normal;
        width: max-content;
        max-width: 300px;
        z-index: 50;
    }

    /* 添加鼠标悬停时的工具提示效果 */
    .task-table td[title] {
        cursor: help;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-card">
    <!-- 固定顶部区域 -->
    <div class="fixed-header">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">任务中心</h2>
            <button onclick="showCreateTaskModal()" 
                    class="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>添加任务
            </button>
        </div>
    </div>

    <!-- 可滚动的内容区域 -->
    <div class="scrollable-content">
        <table class="task-table">
            <thead>
                <tr>
                    <th>店铺名称/任务类型</th>
                    <th>执行间隔/任务优先级</th>
                    <th>可执行数量</th>
                    <th>当次执行进度</th>
                    <th>总执行次数</th>
                    <th>累计处理成功/失败</th>
                    <th>创建时间/最后执行</th>
                    <th>下次执行时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="taskList">
                <!-- 任务列表将通过JavaScript动态加载 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 创建任务模态框 -->
<div id="createTaskModal" class="modal">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">创建新任务</h2>
            <button onclick="hideCreateTaskModal()" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="createTaskForm" onsubmit="handleCreateTask(event)">
            <!-- 店铺选择 -->
            <div class="form-group">
                <label class="form-label">选择店铺<span class="text-red-500">*</span></label>
                <select class="form-select" name="store_id" required onchange="updateTaskTypeOptions(this.value)">
                    <option value="">请选择店铺</option>
                    {% for store in stores %}
                    <option value="{{ store.seller_id }}">{{ store.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- 任务类型 -->
            <div class="form-group">
                <label class="form-label">任务类型<span class="text-red-500">*</span></label>
                <select class="form-select" name="task_type" required>
                    <option value="">请选择任务类型</option>
                    <option value="translation">翻译产品</option>
                    <option value="publish">发布产品</option>
                </select>
                <div class="text-xs text-gray-500 mt-1">每个店铺只能创建一个翻译任务和一个发布任务</div>
            </div>

            <!-- 任务处理数量 -->
            <div class="form-group">
                <label class="form-label">单次处理数量<span class="text-red-500">*</span></label>
                <div class="flex items-center space-x-2">
                    <input type="number" class="form-input" name="batch_size" min="1" max="500" value="200" required>
                </div>
                <div class="text-xs text-gray-500 mt-1">每次执行时处理的产品数量</div>
            </div>

            <!-- 间隔时间 -->
            <div class="form-group">
                <label class="form-label">执行间隔（分钟）<span class="text-red-500">*</span></label>
                <input type="number" class="form-input" name="interval" min="1" value="30" required>
            </div>

            <!-- 优先级 -->
            <div class="form-group">
                <label class="form-label">优先级<span class="text-red-500">*</span></label>
                <select class="form-select" name="priority" required>
                    <option value="1">最高</option>
                    <option value="2">高</option>
                    <option value="3">中</option>
                    <option value="4">低</option>
                    <option value="5">最低</option>
                </select>
            </div>

            <!-- 任务描述 -->
            <div class="form-group">
                <label class="form-label">任务描述</label>
                <input type="text" class="form-input" name="description" placeholder="请输入任务描述">
            </div>

            <!-- 提交按钮 -->
            <div class="flex justify-end mt-6">
                <button type="button" onclick="hideCreateTaskModal()" class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md mr-2 hover:bg-gray-200">
                    取消
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    创建任务
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑任务模态框 -->
<div id="editTaskModal" class="modal">
    <div class="modal-content p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">编辑任务</h2>
            <button onclick="hideEditTaskModal()" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="editTaskForm" onsubmit="handleEditTask(event)">
            <input type="hidden" name="task_id">
            
            <!-- 店铺选择 -->
            <div class="form-group">
                <label class="form-label">店铺</label>
                <input type="text" class="form-input" name="store_name" disabled>
            </div>

            <!-- 任务类型 -->
            <div class="form-group">
                <label class="form-label">任务类型</label>
                <input type="text" class="form-input" name="task_type" disabled>
            </div>

            <!-- 可执行产品数量 -->
            <div class="form-group">
                <label class="form-label">可执行产品数量</label>
                <div class="text-sm text-gray-700" id="executableCount">-</div>
            </div>

            <!-- 任务处理数量 -->
            <div class="form-group">
                <label class="form-label">单次处理数量<span class="text-red-500">*</span></label>
                <div class="flex items-center space-x-2">
                    <input type="number" class="form-input" name="batch_size" min="1" max="500" value="200" required>
                </div>
            </div>

            <!-- 间隔时间 -->
            <div id="editIntervalGroup" class="form-group">
                <label class="form-label">执行间隔（分钟）<span class="text-red-500">*</span></label>
                <input type="number" class="form-input" name="interval" min="1" value="30" required>
            </div>

            <!-- 优先级 -->
            <div class="form-group">
                <label class="form-label">优先级<span class="text-red-500">*</span></label>
                <select class="form-select" name="priority" required>
                    <option value="1">最高</option>
                    <option value="2">高</option>
                    <option value="3">中</option>
                    <option value="4">低</option>
                    <option value="5">最低</option>
                </select>
            </div>

            <!-- 底部按钮 -->
            <div class="flex justify-between mt-6">
                <button type="button" onclick="deleteTask()" class="px-4 py-2 text-red-600 bg-red-100 rounded-md hover:bg-red-200">
                    删除任务
                </button>
                <div class="space-x-2">
                    <button type="button" onclick="hideEditTaskModal()" class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存更改
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 存储每个店铺的任务类型状态
    let storeTaskTypes = {};

    // 初始化时加载所有店铺的任务状态
    async function loadStoreTaskTypes() {
        try {
            const response = await fetch('/api/tasks');
            const tasks = await response.json();
            
            // 重置状态
            storeTaskTypes = {};
            
            // 更新每个店铺的任务类型状态
            tasks.forEach(task => {
                if (!storeTaskTypes[task.seller_id]) {
                    storeTaskTypes[task.seller_id] = new Set();
                }
                storeTaskTypes[task.seller_id].add(task.task_type);
            });
        } catch (error) {
            console.error('加载店铺任务状态失败:', error);
        }
    }

    // 更新任务类型选项
    function updateTaskTypeOptions(sellerId) {
        const taskTypeSelect = document.querySelector('select[name="task_type"]');
        const options = taskTypeSelect.options;
        
        // 重置所有选项
        for (let i = 1; i < options.length; i++) {
            const option = options[i];
            option.disabled = false;
            option.classList.remove('text-gray-400', 'bg-gray-100');
        }
        
        // 如果选择了店铺，检查并禁用已存在的任务类型
        if (sellerId && storeTaskTypes[sellerId]) {
            const existingTypes = storeTaskTypes[sellerId];
            for (let i = 1; i < options.length; i++) {
                const option = options[i];
                if (existingTypes.has(option.value)) {
                    option.disabled = true;
                    option.classList.add('text-gray-400', 'bg-gray-100');
                }
            }
        }
    }

    // 显示创建任务模态框时加载任务状态
    async function showCreateTaskModal() {
        await loadStoreTaskTypes();
        document.getElementById('createTaskModal').classList.add('show');
        // 重置表单
        document.getElementById('createTaskForm').reset();
        // 更新任务类型选项
        updateTaskTypeOptions('');
    }

    // 隐藏创建任务模态框
    function hideCreateTaskModal() {
        document.getElementById('createTaskModal').classList.remove('show');
    }

    // 加载任务列表
    async function loadTasks() {
        try {
            const response = await fetch('/api/tasks');
            const tasks = await response.json();
            
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = tasks.map(task => `
                <tr>
                    <td>
                        <div class="font-medium text-gray-900">${task.store_name || '-'}</div>
                        <div class="text-sm text-gray-500">${task.task_type === 'translation' ? '翻译任务' : '上架任务'}</div>
                    </td>
                    <td>
                        <div class="font-medium text-gray-900">${task.interval}分钟/次</div>
                        <div class="text-sm text-gray-500">优先级: ${getPriorityText(task.priority)}</div>
                    </td>
                    <td>
                        <div class="number text-red-600 font-medium">${task.executable_count}</div>
                    </td>
                    <td>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-1">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: ${task.progress}%"></div>
                        </div>
                        <div class="text-xs text-gray-600">${task.progress.toFixed(2)}% (${task.current_batch_size}个/次)</div>
                    </td>
                    <td>
                        <div class="number font-medium">${task.execution_count}</div>
                    </td>
                    <td>
                        <div class="flex flex-col items-center gap-1">
                            <div class="number text-green-600">${task.total_success_count}</div>
                            <div class="number text-red-600">${task.total_failed_count}</div>
                        </div>
                    </td>
                    <td>
                        <div class="flex flex-col items-center gap-1">
                            <div class="text-xs text-gray-900">${formatDate(task.created_at)}</div>
                            <div class="text-xs text-gray-500">${formatDate(task.last_run_at)}</div>
                        </div>
                    </td>
                    <td>
                        <div class="text-xs text-gray-900">${task.status === 'executing' ? '-' : formatDate(task.next_run_at)}</div>
                    </td>
                    <td>
                        <div class="flex flex-col items-center gap-1">
                            <span class="status-badge ${getStatusClass(task.status)}">
                                ${getStatusText(task.status)}
                            </span>
                        </div>
                    </td>
                    <td>
                        <div class="flex justify-center space-x-2">
                            <button onclick="editTask('${task.id}')" class="action-button" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${task.status === 'executing' ? '' : `
                                <button onclick="handleTaskAction(event, '${task.id}', '${task.status}')" 
                                        class="action-button ${task.status === 'running' ? 'stop' : 'start'}"
                                        title="${task.status === 'running' ? '停止' : '启动'}"
                                        data-task-id="${task.id}"
                                        data-action="${task.status === 'running' ? 'stop' : 'start'}">
                                    <i class="fas fa-${task.status === 'running' ? 'pause' : 'play'}"></i>
                                </button>
                            `}
                        </div>
                    </td>
                </tr>
            `).join('');
        } catch (error) {
            console.error('Error:', error);
            notification.error('加载任务列表失败');
        }
    }

    // 获取优先级文本
    function getPriorityText(priority) {
        const texts = {
            1: '最高',
            2: '高',
            3: '中',
            4: '低',
            5: '最低'
        };
        return texts[priority] || '中';
    }

    // 获取任务状态样式
    function getStatusClass(status) {
        switch (status) {
            case 'running': return 'status-running';
            case 'stopped': return 'status-stopped';
            case 'completed': return 'status-completed';
            case 'executing': return 'status-executing';
            default: return '';
        }
    }

    // 获取任务状态文本
    function getStatusText(status) {
        switch (status) {
            case 'running': return '等待执行';
            case 'stopped': return '任务暂停';
            case 'completed': return '执行完成';
            case 'executing': return '执行中...';
            default: return status;
        }
    }

    // 编辑任务
    function editTask(taskId) {
        // 显示加载状态
        const loadingModal = document.createElement('div');
        loadingModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        loadingModal.innerHTML = `
            <div class="bg-white p-4 rounded-lg shadow-lg">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-spinner fa-spin text-blue-600"></i>
                    <span>正在加载任务详情...</span>
                </div>
            </div>
        `;
        document.body.appendChild(loadingModal);

        // 获取任务详情
        fetch(`/api/tasks/${taskId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(task => {
                if (task.error) {
                    throw new Error(task.error);
                }

                const form = document.getElementById('editTaskForm');
                form.querySelector('[name="task_id"]').value = task.id;
                form.querySelector('[name="store_name"]').value = task.store_name;
                form.querySelector('[name="task_type"]').value = task.task_type === 'translation' ? '翻译任务' : '上架任务';
                form.querySelector('[name="batch_size"]').value = task.batch_size;
                form.querySelector('[name="interval"]').value = task.interval;
                form.querySelector('[name="priority"]').value = task.priority;
                
                // 更新可执行数量
                document.getElementById('executableCount').textContent = task.executable_count || '0';

                // 显示模态框
                document.getElementById('editTaskModal').classList.add('show');
            })
            .catch(error => {
                notification.error(`获取任务详情失败: ${error.message}`);
            })
            .finally(() => {
                // 移除加载状态
                document.body.removeChild(loadingModal);
            });
    }

    // 隐藏编辑任务模态框
    function hideEditTaskModal() {
        document.getElementById('editTaskModal').classList.remove('show');
    }

    // 处理编辑任务表单提交
    async function handleEditTask(event) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);
        const taskId = formData.get('task_id');
        
        try {
            const response = await fetch(`/api/tasks/${taskId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(Object.fromEntries(formData))
            });

            const result = await response.json();
            
            if (result.success) {
                notification.success('任务更新成功');
                hideEditTaskModal();
                loadTasks();
            } else {
                notification.error(result.error || '更新任务失败');
            }
        } catch (error) {
            notification.error('更新任务时发生错误');
        }
    }

    // 显示错误提示模态框
    function showErrorModal(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorModal').classList.add('show');
    }

    // 隐藏错误提示模态框
    function hideErrorModal() {
        document.getElementById('errorModal').classList.remove('show');
    }

    // 处理任务启动/停止操作
    async function handleTaskAction(event, taskId, status) {
        try {
            // 阻止事件冒泡
            event.preventDefault();
            event.stopPropagation();

            if (!taskId) {
                notification.error('任务ID无效');
                return;
            }

            // 获取按钮元素和操作类型
            const button = event.currentTarget;
            const action = button.dataset.action;

            // 禁用按钮，防止重复点击
            button.disabled = true;

            // 添加加载状态
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            const response = await fetch(`/api/tasks/${taskId}/${action}`, { 
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                notification.success(`任务已${action === 'start' ? '启动' : '停止'}`);
                // 立即更新按钮状态
                button.dataset.action = action === 'start' ? 'stop' : 'start';
                button.classList.toggle('start');
                button.classList.toggle('stop');
                button.title = action === 'start' ? '停止' : '启动';
                // 重新加载任务列表
                await loadTasks();
            } else {
                throw new Error(result.error || `${action === 'start' ? '启动' : '停止'}任务失败`);
            }
        } catch (error) {
            notification.error(error.message || `操作失败`);
            showErrorModal(error.message || '操作失败');
        } finally {
            // 恢复按钮状态
            if (event.currentTarget) {
                const button = event.currentTarget;
                button.disabled = false;
                const action = button.dataset.action;
                button.innerHTML = `<i class="fas fa-${action === 'start' ? 'play' : 'pause'}"></i>`;
            }
        }
    }

    // 删除任务
    async function deleteTask() {
        const form = document.getElementById('editTaskForm');
        const taskId = form.querySelector('[name="task_id"]').value;
        
        if (!taskId) {
            notification.error('任务ID无效');
            return;
        }

        try {
            const confirmResult = await notification.confirm('确定要删除这个任务吗？');
            if (!confirmResult) {
                return;
            }

            const response = await fetch(`/api/tasks/${taskId}`, { 
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                notification.success('任务已删除');
                hideEditTaskModal();
                loadTasks();
            } else {
                throw new Error(result.error || '删除任务失败');
            }
        } catch (error) {
            notification.error(error.message || '删除任务时发生错误');
        }
    }

    // 处理创建任务表单提交
    async function handleCreateTask(event) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);
        
        // 构建请求数据
        const data = {
            seller_id: formData.get('store_id'),
            task_type: formData.get('task_type'),
            batch_size: parseInt(formData.get('batch_size')),
            interval: parseInt(formData.get('interval')),
            priority: parseInt(formData.get('priority')),
            description: formData.get('description') || ''
        };
        
        try {
            const response = await fetch('/api/tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                notification.success('任务创建成功');
                hideCreateTaskModal();
                await loadTasks();  // 重新加载任务列表
                await loadStoreTaskTypes();  // 重新加载任务状态
            } else {
                notification.error(result.error || '创建任务失败');
            }
        } catch (error) {
            console.error('Error:', error);
            notification.error('创建任务时发生错误');
        }
    }

    // 添加日期格式化函数
    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).replace(/\//g, '-');
    }

    // 页面加载时加载任务列表
    document.addEventListener('DOMContentLoaded', loadTasks);
</script>
{% endblock %} 