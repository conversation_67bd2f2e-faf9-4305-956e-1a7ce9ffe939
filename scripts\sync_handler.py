import logging
import time
from models import db, Store
from datetime import datetime
import requests
from typing import Dict, List, Optional
import threading
import aiohttp
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import os
from logging.handlers import RotatingFileHandler

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建logs目录（如果不存在）
if not os.path.exists('logs'):
    os.makedirs('logs')

# 配置文件日志处理器
file_handler = RotatingFileHandler(
    'logs/sync_details.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
file_handler.setFormatter(file_formatter)

# 获取logger并添加文件处理器
logger = logging.getLogger(__name__)
logger.addHandler(file_handler)
logger.setLevel(logging.DEBUG)

class SyncProgress:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SyncProgress, cls).__new__(cls)
                cls._instance.reset()
            return cls._instance
    
    def reset(self):
        self.progress = 0
        self.status = "准备中..."
        self.success_count = 0
        self.error_count = 0
        self.is_completed = False
        self.errors = []
        self.start_time = None
        self.is_cancelled = False
        self.current_count = 0
        self.total_count = 0
    
    def cancel(self):
        self.is_cancelled = True
        self.status = "已取消"
        self.is_completed = True

    def to_dict(self) -> Dict:
        return {
            "progress": self.progress,
            "status": self.status,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "is_completed": self.is_completed,
            "errors": self.errors[:5],  # 只返回前5个错误
            "duration": str(datetime.now() - self.start_time) if self.start_time else None,
            "is_cancelled": self.is_cancelled,
            "current_count": self.current_count,
            "total_count": self.total_count
        }

class ProductSynchronizer:
    def __init__(self, store: Store):
        self.store = store
        self.progress = SyncProgress()
        self.session = requests.Session()
        # self.session.proxies = {
        #     "http": "http://127.0.0.1:10809",
        #     "https": "http://127.0.0.1:10809",
        # }
        self.retry_count = 5
        self.retry_delay = 10
        self.batch_size = 100  # 每页请求100个商品

    def _get_token(self) -> Optional[str]:
        """获取API token"""
        try:
            # 发送POST请求到PIGU的登录API
            response = self.session.post(
                "https://pmpapi.pigugroup.eu/v3/login",
                json={
                    "username": self.store.username,
                    "password": self.store.password
                }
            )
            if response.status_code == 200:
                return response.json().get('token')
        except Exception as e:
            logger.error(f"获取token失败: {str(e)}")
        return None

    def sync_products(self):
        """同步店铺产品"""
        try:
            self.progress.reset()
            self.progress.start_time = datetime.now()
            
            ProductModel = self.store.get_products_model()
            if not ProductModel:
                raise Exception("无法获取产品表模型")

            token = self._get_token()
            if not token:
                raise Exception("无法获取token")

            headers = {
                "Authorization": f"Pigu-mp {token}",
                "Content-Type": "application/json"
            }
            
            # 初始化分页参数，设置每页大小为100
            base_url = f"https://pmpapi.pigugroup.eu/v3/sellers/{self.store.seller_id}/offers"
            next_url = f"{base_url}?limit={self.batch_size}"  # 添加limit参数
            total_count = 0
            processed_count = 0

            while next_url and not self.progress.is_cancelled:
                response = self._make_request_with_retry(next_url, headers)
                if not response:
                    continue

                data = response.json()
                if not total_count:
                    total_count = data['meta'].get('total_count', 0)
                    self.progress.total_count = total_count

                offers = data['offers']
                
                # 实时处理每个商品数据
                for offer in offers:
                    try:
                        self._process_single_offer(offer, ProductModel)
                        self.progress.success_count += 1
                        processed_count += 1
                        self.progress.current_count = processed_count
                    except Exception as e:
                        self.progress.error_count += 1
                        error_msg = f"处理商品失败 (SKU: {offer.get('modification', {}).get('sku')}): {str(e)}"
                        # logger.error(error_msg)
                        self.progress.errors.append(error_msg)
                    
                    # 更新进度
                    if total_count > 0:
                        self.progress.progress = min(float(processed_count) / total_count, 0.99)
                    self.progress.status = f"已同步 {processed_count}/{total_count} 个产品"

                # 获取下一页URL
                next_url = data['meta'].get('next')
                
                # 添加延迟避免请求过快
                time.sleep(0.1)

            if not self.progress.is_cancelled:
                self.progress.progress = 1.0
                self.progress.status = "同步完成"
            self.progress.is_completed = True

        except Exception as e:
            self.progress.status = f"同步失败: {str(e)}"
            self.progress.is_completed = True

    def _make_request_with_retry(self, url: str, headers: Dict) -> Optional[requests.Response]:
        """带重试机制的请求"""
        for attempt in range(self.retry_count):
            try:
                # 检查token是否过期，如果过期则重新获取
                if attempt > 0:  # 如果是重试，先刷新token
                    token = self._get_token()
                    if token:
                        headers["Authorization"] = f"Pigu-mp {token}"  # 修改为正确的认证格式
                    else:
                        raise Exception("无法刷新token")

                response = self.session.get(url, headers=headers)
                response.raise_for_status()
                return response
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 401:  # Unauthorized
                    logger.error(f"Token无效或过期 (尝试 {attempt + 1}/{self.retry_count})")
                else:
                    logger.error(f"请求失败 (尝试 {attempt + 1}/{self.retry_count}): {str(e)}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
            except Exception as e:
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
        return None

    def _process_single_offer(self, offer: Dict, ProductModel) -> None:
        """处理单个商品数据并实时写入数据库"""
        try:
            modification = offer['modification']
            
            # 使用offer的id作为主键
            product_data = {
                'id': offer['id'],
                'external_id': str(modification.get('external_id')),
                'pigu_external_id': modification.get('pigu_external_id'),
                'app_name': offer.get('app_name'),
                'title': modification.get('title'),
                'insult_price': modification.get('insult_price'),
                'buybox_price': modification.get('buybox_price'),
                'relevant_market_price': modification.get('relevant_market_price'),
                'sku': modification.get('sku'),
                'ean': modification.get('ean'),
                'delivery_hours': offer.get('delivery_hours'),
                'amount': offer.get('amount'),
                'sell_price': offer.get('sell_price'),
                'sell_price_after_discount': offer.get('sell_price_after_discount'),
                'status': offer.get('status'),
                'updated_at': datetime.now()
            }

            # 查找或创建产品记录（使用id查找）
            product = ProductModel.query.get(offer['id'])
            if product:
                # 更新现有记录，但保持 base_price 不变
                for key, value in product_data.items():
                    setattr(product, key, value)
            else:
                # 创建新记录，设置 base_price
                # 优先使用 sell_price_after_discount，如果没有则使用 sell_price
                base_price = offer.get('sell_price_after_discount') or offer.get('sell_price')
                product_data['base_price'] = base_price
                product = ProductModel(**product_data)
                db.session.add(product)

            # 立即提交更改
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise Exception(f"处理商品数据失败: {str(e)}")

def sync_store_products(app, store_id):
    """同步店铺产品"""
    try:
        with app.app_context():
            # 获取店铺信息
            store = Store.query.filter_by(seller_id=store_id).first()
            if not store:
                # logger.error(f"店铺不存在: seller_id={store_id}")
                raise Exception(f"店铺不存在: seller_id={store_id}")
            
            # logger.info(f"开始同步店铺 {store.name} (seller_id: {store.seller_id}) 的产品")
            
            # 初始化进度
            progress = SyncProgress()
            progress.reset()
            progress.start_time = datetime.now()
            progress.status = "正在同步..."
            
            # 获取产品表模型
            ProductModel = store.get_products_model()
            if not ProductModel:
                raise Exception("无法获取产品表模型")

            # 获取token
            session = requests.Session()
            #session.proxies = {"http": "http://127.0.0.1:10809", "https": "http://127.0.0.1:10809"}
            
            # 使用用户名密码获取token
            try:
                response = session.post(
                    "https://pmpapi.pigugroup.eu/v3/login",
                    json={"username": store.username, "password": store.password}
                )
                if response.status_code != 200:
                    raise Exception(f"获取token失败: HTTP {response.status_code}")
                token = response.json().get('token')
                if not token:
                    raise Exception("获取token失败: 响应中没有token")
            except Exception as e:
                # logger.error(f"获取token失败: {str(e)}")
                raise
            
            headers = {
                "Authorization": f"Pigu-mp {token}",
                "Content-Type": "application/json"
            }
            
            # 初始化请求参数
            base_url = f"https://pmpapi.pigugroup.eu/v3/sellers/{store.seller_id}/offers"
            next_url = f"{base_url}?limit=100"
            processed_count = 0
            
            # 获取第一页数据以获取总数
            response = session.get(next_url, headers=headers)
            if response.status_code != 200:
                raise Exception(f"API请求失败: HTTP {response.status_code}")
                
            data = response.json()
            total_count = data['meta'].get('total_count', 0)
            progress.total_count = total_count
            
            while next_url and not progress.is_cancelled:
                # logger.info(f"请求URL: {next_url}")
                response = session.get(next_url, headers=headers)
                if response.status_code != 200:
                    raise Exception(f"API请求失败: HTTP {response.status_code}")
                
                data = response.json()
                offers = data['offers']
                # logger.info(f"获取到 {len(offers)} 个产品")
                
                # 处理本页数据
                products_data = []
                for offer in offers:
                    try:
                        product_data = process_offer(offer, store.seller_id)
                        products_data.append(product_data)
                        processed_count += 1
                    except Exception as e:
                        # logger.error(f"处理商品数据失败: {str(e)}")
                        progress.error_count += 1
                        continue
                
                # 立即写入数据库
                if products_data:
                    success_count = process_batch(products_data, ProductModel, progress)
                    progress.success_count += success_count
                
                # 更新进度
                progress.current_count = processed_count
                progress.progress = min(float(processed_count) / total_count, 0.99)
                progress.status = f"已同步 {processed_count}/{total_count} 个产品"
                
                # 获取下一页URL
                next_url = data['meta'].get('next')
                if next_url:
                    time.sleep(0.2)  # 避免请求过快
            
            progress.progress = 1.0
            progress.status = "同步完成"
            progress.is_completed = True
            
            # logger.info(f"店铺 {store.name} 的产品同步完成:")
            # logger.info(f"  - 总数: {progress.success_count}")
            
    except Exception as e:
        # logger.error(f"同步过程中出错: {str(e)}")
        progress = SyncProgress()
        progress.status = f"同步失败: {str(e)}"
        progress.is_completed = True
        raise

def process_offer(offer, seller_id):
    """处理单个商品数据"""
    modification = offer['modification']
    return {
        'id': offer['id'],
        'external_id': str(modification.get('external_id', '')),
        'pigu_external_id': modification.get('pigu_external_id'),
        'app_name': offer.get('app_name', ''),
        'title': modification.get('title', ''),
        'insult_price': modification.get('insult_price', 0),
        'buybox_price': modification.get('buybox_price', 0),
        'relevant_market_price': modification.get('relevant_market_price', 0),
        'sku': modification.get('sku', ''),
        'ean': modification.get('ean', ''),
        'delivery_hours': offer.get('delivery_hours', 0),
        'amount': offer.get('amount', 0),
        'sell_price': offer.get('sell_price', 0),
        'sell_price_after_discount': offer.get('sell_price_after_discount', 0),
        'status': offer.get('status', 'active'),
        'seller_id': seller_id,
        'updated_at': datetime.now(),
        'base_price': offer.get('sell_price_after_discount') or offer.get('sell_price', 0)
    }

def process_batch(products, ProductModel, progress):
    """处理一批产品数据"""
    if not products:
        return 0
        
    # logger.info(f"开始写入 {len(products)} 条数据...")
    success_count = 0
    
    try:
        existing_ids = set(id_tuple[0] for id_tuple in 
            db.session.query(ProductModel.id).filter(
                ProductModel.id.in_([p['id'] for p in products])
            ).all()
        )
        
        to_insert = []
        to_update = []
        
        for product_data in products:
            if product_data['id'] in existing_ids:
                to_update.append(product_data)
            else:
                to_insert.append(product_data)
        
        try:
            if to_insert:
                for i in range(0, len(to_insert), 100):
                    batch = to_insert[i:i+100]
                    db.session.bulk_insert_mappings(ProductModel, batch)
                    db.session.flush()
                    success_count += len(batch)
            
            if to_update:
                for i in range(0, len(to_update), 100):
                    batch = to_update[i:i+100]
                    db.session.bulk_update_mappings(ProductModel, batch)
                    db.session.flush()
                    success_count += len(batch)
            
            db.session.commit()
            return success_count
            
        except Exception as e:
            db.session.rollback()
            # logger.error(f"写入数据库失败: {str(e)}")
            progress.error_count += len(products)
            return 0
            
    except Exception as e:
        # logger.error(f"处理批量数据失败: {str(e)}")
        progress.error_count += len(products)
        return 0