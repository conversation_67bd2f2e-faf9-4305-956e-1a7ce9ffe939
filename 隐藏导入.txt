flask
flask_sqlalchemy
flask_apscheduler
flask.blueprints
flask.json
flask.templating
sqlalchemy
sqlalchemy.orm
sqlalchemy.sql
sqlalchemy.exc
werkzeug
werkzeug.security
jinja2
win32api
win32con
win32gui
win32process
win32event
requests
aiohttp
httpx
urllib3
pandas
numpy
openpyxl
openpyxl.utils
openpyxl.styles
asyncio
asyncio.events
asyncio.locks
aiosqlite
cryptography
jwt
PyJWT
pymysql
alembic
alembic.config
alembic.script
python-dateutil
pytz
datetime
tqdm
loguru
loguru.logger
tenacity
tenacity.retry
python-magic
python-magic-bin
Pillow
PIL.Image
os
sys
threading
multiprocessing
socket
json
logging
pathlib

# 文件（Add Files）
C:/Users/<USER>/Desktop/web2.0/requirements.txt -> .
C:/Users/<USER>/Desktop/web2.0/models.py -> .
C:/Users/<USER>/Desktop/web2.0/scheduler -> .

# 文件夹（Add Folder）
C:/Users/<USER>/Desktop/web2.0/data -> data/
C:/Users/<USER>/Desktop/web2.0/docs -> docs/
C:/Users/<USER>/Desktop/web2.0/logs -> logs/
C:/Users/<USER>/Desktop/web2.0/routes -> routes/
C:/Users/<USER>/Desktop/web2.0/scripts -> scripts/
C:/Users/<USER>/Desktop/web2.0/static -> static/
C:/Users/<USER>/Desktop/web2.0/templates -> templates/
C:/Users/<USER>/Desktop/web2.0/translate -> translate/
C:/Users/<USER>/Desktop/web2.0/API Doc -> API Doc