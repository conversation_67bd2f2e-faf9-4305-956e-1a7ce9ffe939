from flask import Flask, render_template, jsonify, request
from models import db, Store, UnifiedStoreofferProducts, UnifiedListProducts, SystemSettings
from sqlalchemy import or_, text
from datetime import datetime
import requests
import threading
import os
import time
from threading import Thread
import logging
from routes.product_list import product_bp
from routes.upload_task import bp as upload_task_bp
from routes.product_publish import bp as product_publish_bp
from routes.offer_product import bp as offer_product_bp
from routes.phh_store import bp as phh_store_bp
from routes.settings import system, multi_model
from routes.task_center import task_center_bp
from routes.xml_task import xml_task_bp
import webbrowser
import sys
import socket
from functools import partial
from translate.deeplx_api import DeepLXTranslator
from scheduler import init_scheduler, shutdown_scheduler


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# 设置 Werkzeug 日志级别为 WARNING 或更高
log = logging.getLogger('werkzeug')
log.setLevel(logging.WARNING)
# 禁用 httpx 的请求日志
logging.getLogger('httpx').setLevel(logging.WARNING)
# 如果你想完全禁用 Werkzeug 日志
# log.disabled = True

app = Flask(__name__)
# 注册product blueprint
app.register_blueprint(product_bp)

# 注册上传任务路由
app.register_blueprint(upload_task_bp)

# 注册产品发布路由
app.register_blueprint(product_publish_bp)

# 注册offer_product blueprint
app.register_blueprint(offer_product_bp, url_prefix='/api/offer_product')

# 注册store blueprint
app.register_blueprint(phh_store_bp, url_prefix='/api/phh_store')

# 注册系统设置蓝图
app.register_blueprint(system.bp)

# 注册多模型配置蓝图
app.register_blueprint(multi_model.bp)

# 注册任务中心蓝图
app.register_blueprint(task_center_bp)

# 注册XML任务路由
app.register_blueprint(xml_task_bp, url_prefix='/api/xml-tasks')

# 获取应用程序路径
def get_app_path():
    if getattr(sys, 'frozen', False):
        # 如果是打包后的程序
        return os.path.dirname(sys.executable)
    else:
        # 如果是开发环境
        return os.path.abspath(os.path.dirname(__file__))

# 确保必要的文件夹存在
def ensure_directories():
    app_path = get_app_path()
    required_dirs = ['data', 'logs']
    
    for dir_name in required_dirs:
        dir_path = os.path.join(app_path, dir_name)
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path)
                print(f"创建目录: {dir_path}")
            except Exception as e:
                print(f"创建目录失败 {dir_path}: {str(e)}")

# 设置数据库文件的绝对路径
def setup_database():
    app_path = get_app_path()
    data_dir = os.path.join(app_path, 'data')
    
    # 确保data目录存在
    if not os.path.exists(data_dir):
        try:
            os.makedirs(data_dir)
            print(f"创建数据目录: {data_dir}")
        except Exception as e:
            print(f"创建数据目录失败: {str(e)}")
    
    # 设置数据库路径
    DB_PATH = os.path.join(data_dir, 'phhwb.db')
    return DB_PATH

DB_PATH = setup_database()
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DB_PATH}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ECHO'] = False

# 确保数据库文件目录存在
db_dir = os.path.dirname(DB_PATH)
if not os.path.exists(db_dir):
    os.makedirs(db_dir)

db.init_app(app)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/store-management')
def store_management():
    return render_template('store_management.html', active_page='store-management')

@app.route('/product-publish')
def product_publish():
    # 获取所有店铺信息
    stores = Store.query.all()
    return render_template('product_publish.html', active_page='product-publish', stores=stores)

@app.route('/api/list-products')
def get_list_products():
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 30, type=int)
        search = request.args.get('search', '').strip()
        status = request.args.get('status', '')
        seller_id = request.args.get('seller_id')
        search_type = request.args.get('search_type', '')
        
        #logger.info(f"获取产品列表参数: page={page}, per_page={per_page}, search={search}, status={status}, seller_id={seller_id}")
        
        # 构建基础查询
        query = UnifiedListProducts.query
        
        # 应用店铺筛选
        if seller_id:
            query = query.filter(UnifiedListProducts.seller_id == seller_id)
        
        # 应用搜索条件
        if search:
            query = query.filter(
                or_(
                    UnifiedListProducts.title_en.ilike(f'%{search}%'),
                    UnifiedListProducts.title_cn.ilike(f'%{search}%'),
                    UnifiedListProducts.sku.ilike(f'%{search}%'),
                    UnifiedListProducts.ean.ilike(f'%{search}%')
                )
            )
        
        # 应用状态筛选
        if status:
            query = query.filter(UnifiedListProducts.status == status)
        
        # 获取总数
        total_count = query.count()
        
        # 获取分页数据
        products = query.offset((page - 1) * per_page).limit(per_page).all()
        # logger.info(f"查询到 {len(products)} 个产品")
        
        # 转换数据格式
        products_data = [{
            'id': product.id,
            'seller_id': product.seller_id,
            'sku': product.sku,
            'ean': product.ean,
            'category_id': product.category_id,
            'title_en': product.title_en,
            'title_cn': product.title_cn,
            'title_lt': product.title_lt,
            'title_lv': product.title_lv,
            'title_et': product.title_et,
            'title_fi': product.title_fi,
            'image_url1': product.image_url1,
            'image_url2': product.image_url2,
            'image_url3': product.image_url3,
            'image_url4': product.image_url4,
            'image_url5': product.image_url5,
            'video_url': product.video_url,
            'status': product.status,
            'description_en': product.description_en,
            'description_lt': product.description_lt,
            'description_lv': product.description_lv,
            'description_et': product.description_et,
            'description_fi': product.description_fi,
            'package_length': product.package_length,
            'package_width': product.package_width,
            'package_height': product.package_height,
            'package_weight': product.package_weight,
            'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else None,
            'notes': product.notes,
            'platform_message': product.platform_message,
        } for product in products]
        
        return jsonify({
            'products': products_data,
            'pagination': {
                'current_page': page,
                'per_page': per_page,
                'total_pages': (total_count + per_page - 1) // per_page,
                'total_count': total_count
            }
        })
        
    except Exception as e:
        #logger.error(f"获取产品列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/auto-pricing')
def auto_pricing():
    return render_template('auto_pricing.html', active_page='auto-pricing')

# 添加新的辅助函数
def is_port_in_use(port):
    """检查端口是否被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return False
        except socket.error:
            return True

def handle_close():
    """处理程序关闭"""
    print("[INFO] 正在关闭程序...")
    # 在这里添加任何需要的清理代码
    sys.exit(0)

def open_browser():
    """在新线程中打开浏览器"""
    time.sleep(1.5)  # 等待服务器启动
    url = f"http://localhost:5000"  # 使用你的端口
    webbrowser.open(url)

if __name__ == '__main__':
    try:
        # 确保所有必要的目录存在
        ensure_directories()
        
        # 添加窗口关闭处理
        if os.name == 'nt':  # Windows系统
            import win32api
            win32api.SetConsoleCtrlHandler(lambda x: handle_close(), True)
        
        # 检查端口是否被占用
        if is_port_in_use(5000):  # 使用你的端口
            print("[ERROR] 端口 5000 已被占用，请关闭占用该端口的程序后重试")
            input("按回车键退出...")
            sys.exit(1)
        
        # 创建数据库表和初始化设置
        with app.app_context():
            db.create_all()
            SystemSettings.init_settings()
        
        # 只在重载器进程中初始化调度器
        if os.environ.get('WERKZEUG_RUN_MAIN'):
            init_scheduler(app)
            
            # 注册程序退出时的清理函数
            import atexit
            atexit.register(shutdown_scheduler)
        else:
            # 在主进程中只启动浏览器
            print("[INFO] 服务启动成功正在打开浏览器")
            threading.Thread(target=open_browser).start()
        
        # 启动Flask应用
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=True
        )
    except Exception as e:
        print(f"[ERROR] 服务器启动失败: {e}")
        input("按回车键退出...") 
        sys.exit(1)