from .deeplx_api import DeepL<PERSON>ranslator
from .deepseek_api import DeepSeekTranslator
from .selling51_api import Selling51Translator
from .deeplvercel_api import DeepLVerCelTranslator
from .deepseekhuoshan_api import DeepSeekhuoshan
from .deepseekofficial_api import DeepSeekofficial
from models import SystemSettings
from .mtranserver_api import MTranServer
from .deepseekmtserver_api import DeepSeekMTranServer

class TranslatorFactory:
    @staticmethod
    def get_translator():
        """根据系统设置获取对应的翻译器实例"""
        service = SystemSettings.get_setting('translation_service', 'deeplx')
        
        if service == 'deepllinux':
            return DeepLXTranslator()
        elif service == 'deepseek':
            return DeepSeekTranslator()
        elif service == '51Selling':
            return Selling51Translator()
        elif service == 'deeplvercel':
            return DeepLVerCelTranslator()
        elif service == 'deepseekhuoshan':
            return DeepSeekhuoshan()
        elif service == 'deepseekofficial':
            return DeepSeekofficial()
        elif service == 'mtranserver':
            return MTranServer()
        elif service == 'deepseekmtserver':
            return DeepSeekMTranServer()
        else:
            raise ValueError(f'不支持的翻译服务: {service}') 