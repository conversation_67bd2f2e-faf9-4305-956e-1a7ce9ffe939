from flask import jsonify
from models import db, UnifiedStoreofferProducts
from . import bp

@bp.route('/countries')
def get_countries():
    """获取所有国家列表的路由处理函数"""
    try:
        # 从统一表中获取所有不同的国家
        countries = db.session.query(UnifiedStoreofferProducts.app_name)\
            .distinct()\
            .filter(UnifiedStoreofferProducts.app_name.isnot(None))\
            .all()
        
        return jsonify([country[0] for country in countries if country[0]])
    except Exception as e:
        return jsonify({"error": str(e)}), 500 