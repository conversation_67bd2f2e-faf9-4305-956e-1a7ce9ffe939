import requests
import time
import async<PERSON>
import logging
import json
from concurrent.futures import ThreadPoolExecutor
from models import SystemSettings

logger = logging.getLogger(__name__)

class DeepLVerCelTranslator:
    def __init__(self):
        """初始化翻译器"""
        self.api_url = SystemSettings.get_setting('translation_api_key', '')  # DeepL Vercel代理地址
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        self.thread_pool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="translator")
        
    def refresh_settings(self):
        """刷新设置"""
        self.api_url = SystemSettings.get_setting('translation_api_key', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
            
    def translate(self, text, source_lang="AUTO", target_lang="auto"):
        """翻译文本"""
        if SystemSettings.get_setting('translation_enabled', 'true').lower() != 'true':
            return {
                'success': False,
                'error': '翻译功能已禁用'
            }
            
        if not self.api_url:
            return {
                'success': False,
                'error': '检查DeepL Vercel代理地址'
            }
            
        if not text:
            return {
                'success': False,
                'error': '翻译文本不能为空'
            }
            
        data = {
            "text": text,
            "source_lang": source_lang,
            "target_lang": target_lang,
            "tag_handling": ""
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        for retry in range(self.retry_count):
            try:
                response = requests.post(self.api_url, headers=headers, json=data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if result and isinstance(result, dict):
                        return {
                            'success': True,
                            'text': result.get('data', ''),
                            'source_lang': source_lang,
                            'target_lang': target_lang
                        }
                
                if retry < self.retry_count - 1:
                    time.sleep(self.retry_interval)
                    
            except Exception as e:
                if retry < self.retry_count - 1:
                    time.sleep(self.retry_interval)
                    continue
                
                return {
                    'success': False,
                    'error': f'翻译请求失败: {str(e)}'
                }

        return {
            'success': False,
            'error': f'翻译服务返回错误: {response.text}'
        }

    def _translate_sync(self, text, source_lang, target_lang):
        """同步发送翻译请求"""
        data = {
            "text": text,
            "source_lang": source_lang,
            "target_lang": target_lang,
            "tag_handling": ""
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.post(
                self.api_url,
                json=data,
                headers=headers,
            )
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result and isinstance(result, dict):
                        return {
                            'success': True,
                            'text': result.get('data', ''),
                            'target_lang': target_lang,
                            'error': None
                        }
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {str(e)}")
            
            return {
                'success': False,
                'text': None,
                'target_lang': target_lang,
                'error': f'翻译失败: {response.text}'
            }
            
        except requests.Timeout:
            logger.error("请求超时")
            return {
                'success': False,
                'text': None,
                'target_lang': target_lang,
                'error': '请求超时'
            }
        except Exception as e:
            logger.error(f"翻译异常: {str(e)}")
            return {
                'success': False,
                'text': None,
                'target_lang': target_lang,
                'error': str(e)
            }

    async def _translate_single_async(self, session, text, source_lang, target_lang):
        """异步包装同步请求"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.thread_pool,
            self._translate_sync,
            text,
            source_lang,
            target_lang
        )
            
    async def translate_product_async(self, text, source_lang="AUTO"):
        """异步翻译产品的标题或描述到四个目标语言"""
        if not self.api_url:
            return {lang: None for lang in ["LT", "LV", "ET", "FI"]}
            
        if not text:
            return {lang: None for lang in ["LT", "LV", "ET", "FI"]}
        
        target_langs = ["LT", "LV", "ET", "FI"]
        translations = {lang: None for lang in target_langs}
        failed_langs = []

        # 创建所有语言的翻译任务
        tasks = [
            self._translate_single_async(None, text, source_lang, lang)
            for lang in target_langs
        ]
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理翻译结果
        for lang, result in zip(target_langs, results):
            if isinstance(result, Exception):
                failed_langs.append(lang)
                continue
                
            if result['success']:
                translations[lang] = result['text']
            else:
                failed_langs.append(lang)

        # 重试失败的翻译
        if failed_langs:
            for lang in failed_langs:
                for retry in range(self.retry_count):
                    try:
                        result = await self._translate_single_async(None, text, source_lang, lang)
                        if result['success']:
                            translations[lang] = result['text']
                            break
                    except Exception as e:
                        logger.error(f"重试异常: {str(e)}")
                    await asyncio.sleep(self.retry_interval)
        return translations

    async def translate_product_full_async(self, title, description, source_lang="AUTO"):
        """异步翻译产品的标题和描述"""
        title_task = self.translate_product_async(title, source_lang)
        desc_task = self.translate_product_async(description, source_lang)
        title_translations, desc_translations = await asyncio.gather(title_task, desc_task)
        return {
            'title': title_translations,
            'description': desc_translations
        }

    def __del__(self):
        """析构函数，确保线程池正确关闭"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True)

if __name__ == "__main__":
    translator = DeepLVerCelTranslator()