import logging
import requests
from typing import Dict, List, Optional
from models import db, Store, get_product_table
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductEditor:
    def __init__(self, store: Store):
        self.store = store
        self.session = requests.Session()
        # self.session.proxies = {
        #     "http": "http://127.0.0.1:10809",
        #     "https": "http://127.0.0.1:10809",
        # }
        self.max_retries = 3
        self.max_workers = 5  # 并发线程数
        
    def _get_token(self) -> Optional[str]:
        """获取API token"""
        try:
            response = self.session.post(
                "https://pmpapi.pigugroup.eu/v3/login",
                json={
                    "username": self.store.username,
                    "password": self.store.password
                }
            )
            if response.status_code == 200:
                return response.json().get('token')
        except Exception as e:
            logger.error(f"获取token失败: {str(e)}")
        return None

    def _get_headers(self) -> Optional[Dict]:
        """获取带有认证信息的请求头"""
        token = self._get_token()
        if not token:
            return None
        return {
            "Authorization": f"Pigu-mp {token}",
            "Content-Type": "application/json"
        }

    def update_product(self, offer_id: int, update_data: Dict, retry_count: int = 0) -> Dict:
        """
        更新单个产品信息（带重试机制）
        
        Args:
            offer_id: 产品ID
            update_data: 要更新的数据
            retry_count: 当前重试次数
        """
        try:
            headers = self._get_headers()
            if not headers:
                raise Exception("无法获取认证token")

            url = f"https://pmpapi.pigugroup.eu/v3/offers/{offer_id}"
            response = self.session.patch(url, headers=headers, json=update_data)
            response.raise_for_status()

            # 更新本地数据库
            ProductModel = get_product_table(self.store.id)
            product = ProductModel.query.get(offer_id)
            if product:
                for key, value in update_data.items():
                    setattr(product, key, value)
                product.updated_at = datetime.now()
                db.session.commit()

            return {
                "success": True,
                "message": "产品更新成功",
                "data": response.json()
            }

        except requests.exceptions.RequestException as e:
            # logger.error(f"更新产品 {offer_id} 失败: {str(e)}")
            
            # 如果是认证错误且未超过重试次数，则重试
            if (response.status_code == 401 or "token" in str(e).lower()) and retry_count < self.max_retries:
                # logger.info(f"正在重试更新产品 {offer_id}，第 {retry_count + 1} 次尝试")
                return self.update_product(offer_id, update_data, retry_count + 1)
                
            return {
                "success": False,
                "message": f"API请求失败: {str(e)}"
            }
        except Exception as e:
            # logger.error(f"更新产品 {offer_id} 失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新失败: {str(e)}"
            }

    def batch_update_products(self, offer_ids: List[int], update_data: Dict) -> Dict:
        """
        批量更新产品信息（使用线程池并发处理）
        
        Args:
            offer_ids: 要更新的产品ID列表
            update_data: 要更新的数据
        """
        results = {
            "success": [],
            "failed": []
        }
        
        # 使用线程池并发处理更新请求
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 创建更新任务
            future_to_id = {
                executor.submit(self.update_product, offer_id, update_data): offer_id 
                for offer_id in offer_ids
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_id):
                offer_id = future_to_id[future]
                try:
                    result = future.result()
                    if result["success"]:
                        results["success"].append(offer_id)
                    else:
                        results["failed"].append({
                            "id": offer_id,
                            "error": result["message"]
                        })
                except Exception as e:
                    results["failed"].append({
                        "id": offer_id,
                        "error": str(e)
                    })

        return {
            "success": len(results["success"]) > 0,
            "message": f"成功更新 {len(results['success'])} 个产品，失败 {len(results['failed'])} 个",
            "results": results
        }

def update_products(app, store_id: int, offer_ids: List[int], update_data: Dict, is_batch: bool = False):
    """
    更新产品的入口函数
    
    Args:
        app: Flask应用实例
        store_id: 店铺ID
        offer_ids: 要更新的产品ID列表
        update_data: 要更新的数据
        is_batch: 是否为批量更新
    """
    with app.app_context():
        try:
            store = Store.query.get(store_id)
            if not store:
                raise Exception("店铺不存在")
            
            editor = ProductEditor(store)
            if is_batch:
                return editor.batch_update_products(offer_ids, update_data)
            else:
                return editor.update_product(offer_ids[0], update_data)
                
        except Exception as e:
            # logger.error(f"更新任务失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新失败: {str(e)}"
            } 