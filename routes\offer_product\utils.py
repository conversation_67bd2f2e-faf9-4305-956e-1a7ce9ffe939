import logging
import time
import requests
from typing import Dict, List, Optional
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from models import db, Store

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SyncProgress:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SyncProgress, cls).__new__(cls)
                cls._instance.reset()
            return cls._instance
    
    def reset(self):
        self.progress = 0
        self.status = "准备中..."
        self.success_count = 0
        self.error_count = 0
        self.is_completed = False
        self.errors = []
        self.start_time = None
        self.is_cancelled = False
        self.current_count = 0
        self.total_count = 0
    
    def cancel(self):
        self.is_cancelled = True
        self.status = "已取消"
        self.is_completed = True

    def to_dict(self) -> Dict:
        return {
            "progress": self.progress,
            "status": self.status,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "is_completed": self.is_completed,
            "errors": self.errors[:5],  # 只返回前5个错误
            "duration": str(datetime.now() - self.start_time) if self.start_time else None,
            "is_cancelled": self.is_cancelled,
            "current_count": self.current_count,
            "total_count": self.total_count
        }

class ProductEditor:
    def __init__(self, store: Store):
        self.store = store
        self.session = requests.Session()
        # self.session.proxies = {
        #     "http": "http://127.0.0.1:10809",
        #     "https": "http://127.0.0.1:10809",
        # }
        self.max_retries = 3
        self.max_workers = 5  # 并发线程数
        
    def _get_token(self) -> Optional[str]:
        """获取API token"""
        try:
            response = self.session.post(
                "https://pmpapi.pigugroup.eu/v3/login",
                json={
                    "username": self.store.username,
                    "password": self.store.password
                }
            )
            if response.status_code == 200:
                return response.json().get('token')
        except Exception as e:
            logger.error(f"获取token失败: {str(e)}")
        return None

    def _get_headers(self) -> Optional[Dict]:
        """获取带有认证信息的请求头"""
        token = self._get_token()
        if not token:
            return None
        return {
            "Authorization": f"Pigu-mp {token}",
            "Content-Type": "application/json"
        }

    def update_product(self, offer_id: int, update_data: Dict, retry_count: int = 0) -> Dict:
        """更新单个产品信息"""
        try:
            headers = self._get_headers()
            if not headers:
                raise Exception("无法获取认证token")

            url = f"https://pmpapi.pigugroup.eu/v3/offers/{offer_id}"
            response = self.session.patch(url, headers=headers, json=update_data)
            response.raise_for_status()

            # 更新本地数据库
            ProductModel = self.store.get_products_model()
            product = ProductModel.query.get(offer_id)
            if product:
                for key, value in update_data.items():
                    setattr(product, key, value)
                product.updated_at = datetime.now()
                db.session.commit()

            return {
                "success": True,
                "message": "产品更新成功",
                "data": response.json()
            }

        except requests.exceptions.RequestException as e:
            # logger.error(f"更新产品 {offer_id} 失败: {str(e)}")
            
            # 如果是认证错误且未超过重试次数，则重试
            if (response.status_code == 401 or "token" in str(e).lower()) and retry_count < self.max_retries:
                # logger.info(f"正在重试更新产品 {offer_id}，第 {retry_count + 1} 次尝试")
                return self.update_product(offer_id, update_data, retry_count + 1)
                
            return {
                "success": False,
                "message": f"API请求失败: {str(e)}"
            }
        except Exception as e:
            # logger.error(f"更新产品 {offer_id} 失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新失败: {str(e)}"
            }

    def batch_update_products(self, offer_ids: List[int], update_data: Dict) -> Dict:
        """批量更新产品信息"""
        results = {
            "success": [],
            "failed": []
        }
        
        # 使用线程池并发处理更新请求
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 创建更新任务
            future_to_id = {
                executor.submit(self.update_product, offer_id, update_data): offer_id 
                for offer_id in offer_ids
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_id):
                offer_id = future_to_id[future]
                try:
                    result = future.result()
                    if result["success"]:
                        results["success"].append(offer_id)
                    else:
                        results["failed"].append({
                            "id": offer_id,
                            "error": result["message"]
                        })
                except Exception as e:
                    results["failed"].append({
                        "id": offer_id,
                        "error": str(e)
                    })

        return {
            "success": len(results["success"]) > 0,
            "message": f"成功更新 {len(results['success'])} 个产品，失败 {len(results['failed'])} 个",
            "results": results
        }

class ProductSynchronizer:
    def __init__(self, store: Store):
        self.store = store
        self.session = requests.Session()
        # self.session.proxies = {
        #     "http": "http://127.0.0.1:10809",
        #     "https": "http://127.0.0.1:10809",
        # }
        self.progress = SyncProgress()
        self.batch_size = 100
        self.max_retries = 3
        
    def _process_single_offer(self, offer: Dict, ProductModel) -> None:
        """处理单个商品数据并实时写入数据库"""
        try:
            modification = offer['modification']
            
            # 使用offer的id作为主键
            product_data = {
                'id': offer['id'],
                'seller_id': self.store.seller_id,
                'external_id': str(modification.get('external_id')),
                'pigu_external_id': modification.get('pigu_external_id'),
                'app_name': offer.get('app_name'),
                'title': modification.get('title'),
                'insult_price': modification.get('insult_price'),
                'buybox_price': modification.get('buybox_price'),
                'relevant_market_price': modification.get('relevant_market_price'),
                'sku': modification.get('sku'),
                'ean': modification.get('ean'),
                'delivery_hours': offer.get('delivery_hours'),
                'amount': offer.get('amount'),
                'sell_price': offer.get('sell_price'),
                'sell_price_after_discount': offer.get('sell_price_after_discount'),
                'status': offer.get('status'),
                'updated_at': datetime.now()
            }

            # 查找或创建产品记录（使用id查找）
            product = ProductModel.query.get(offer['id'])
            if product:
                # 更新现有记录，但保持 base_price 不变
                for key, value in product_data.items():
                    setattr(product, key, value)
            else:
                # 创建新记录，设置 base_price
                # 优先使用 sell_price_after_discount，如果没有则使用 sell_price
                base_price = offer.get('sell_price_after_discount') or offer.get('sell_price')
                product_data['base_price'] = base_price
                product = ProductModel(**product_data)
                db.session.add(product)

            # 立即提交更改
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise Exception(f"处理商品数据失败: {str(e)}")

    def _get_token(self) -> Optional[str]:
        """获取API token"""
        try:
            response = self.session.post(
                "https://pmpapi.pigugroup.eu/v3/login",
                json={
                    "username": self.store.username,
                    "password": self.store.password
                }
            )
            if response.status_code == 200:
                return response.json().get('token')
        except Exception as e:
            logger.error(f"获取token失败: {str(e)}")
        return None

    def _make_request_with_retry(self, url: str, headers: Dict, retry_count: int = 0) -> Optional[requests.Response]:
        """发送请求（带重试机制）"""
        try:
            response = self.session.get(url, headers=headers)
            response.raise_for_status()
            return response
        except Exception as e:
            if retry_count < self.max_retries:
                # logger.warning(f"请求失败，正在重试 ({retry_count + 1}/{self.max_retries}): {str(e)}")
                time.sleep(1)  # 重试前等待
                return self._make_request_with_retry(url, headers, retry_count + 1)
            # logger.error(f"请求失败: {str(e)}")
            return None

    def sync_products(self):
        """同步店铺产品"""
        try:
            self.progress.reset()
            self.progress.start_time = datetime.now()
            
            ProductModel = self.store.get_products_model()
            if not ProductModel:
                raise Exception("无法获取产品表模型")

            token = self._get_token()
            if not token:
                raise Exception("无法获取token")

            headers = {
                "Authorization": f"Pigu-mp {token}",
                "Content-Type": "application/json"
            }
            
            # 初始化分页参数
            base_url = f"https://pmpapi.pigugroup.eu/v3/sellers/{self.store.seller_id}/offers"
            next_url = f"{base_url}?limit={self.batch_size}"
            total_count = 0
            processed_count = 0

            while next_url and not self.progress.is_cancelled:
                response = self._make_request_with_retry(next_url, headers)
                if not response:
                    continue

                data = response.json()
                if not total_count:
                    total_count = data['meta'].get('total_count', 0)
                    self.progress.total_count = total_count

                offers = data['offers']
                
                # 实时处理每个商品数据
                for offer in offers:
                    try:
                        self._process_single_offer(offer, ProductModel)
                        self.progress.success_count += 1
                        processed_count += 1
                        self.progress.current_count = processed_count
                    except Exception as e:
                        self.progress.error_count += 1
                        error_msg = f"处理商品失败 (SKU: {offer.get('modification', {}).get('sku')}): {str(e)}"
                        # logger.error(error_msg)
                        self.progress.errors.append(error_msg)
                    
                    # 更新进度
                    if total_count > 0:
                        self.progress.progress = min(float(processed_count) / total_count, 0.99)
                    self.progress.status = f"已同步 {processed_count}/{total_count} 个产品"

                # 获取下一页URL
                next_url = data['meta'].get('next')
                
                # 添加延迟避免请求过快
                time.sleep(0.2)

            if not self.progress.is_cancelled:
                self.progress.progress = 1.0
                self.progress.status = "同步完成"
            self.progress.is_completed = True
            
        except Exception as e:
            # logger.error(f"同步过程中出错: {str(e)}")
            self.progress.status = f"同步失败: {str(e)}"
            self.progress.is_completed = True
            raise 