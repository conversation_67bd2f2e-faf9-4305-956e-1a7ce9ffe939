from flask import Blueprint, render_template, jsonify, request
from models import db, Store, TaskCenter
from .services import TaskService
from .handlers import TaskHandler
import logging

logger = logging.getLogger(__name__)
bp = Blueprint('task_center', __name__)

@bp.route('/task-center')
def task_center():
    """任务中心页面"""
    stores = Store.query.all()
    return render_template('task-center.html', 
                         active_page='task-center',
                         stores=stores)

@bp.route('/api/tasks', methods=['GET'])
def get_tasks():
    """获取任务列表"""
    try:
        # 获取查询参数
        seller_id = request.args.get('seller_id')
        status = request.args.get('status')
        task_type = request.args.get('task_type')
        
        # 构建查询
        query = TaskCenter.query
        
        if seller_id:
            query = query.filter(TaskCenter.seller_id == seller_id)
        if status:
            query = query.filter(TaskCenter.status == status)
        if task_type:
            query = query.filter(TaskCenter.task_type == task_type)
            
        # 按ID排序以保持固定顺序
        query = query.order_by(TaskCenter.id.asc())
        
        # 获取任务列表
        tasks = query.all()
        
        # 转换为字典格式，包含额外信息
        return jsonify([TaskService.to_dict_with_extra(task) for task in tasks])
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/tasks', methods=['POST'])
def create_task():
    """创建新任务"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        required_fields = ['seller_id', 'task_type', 'batch_size', 'interval', 'priority']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 创建新任务
        task = TaskCenter(
            seller_id=data['seller_id'],
            description=data.get('description', ''),
            task_type=data['task_type'],
            batch_size=int(data['batch_size']),
            interval=int(data['interval']),
            priority=int(data['priority']),
            status='stopped'  # 默认为停止状态
        )
        
        db.session.add(task)
        db.session.commit()
        
        # 返回创建的任务信息
        return jsonify({
            'success': True,
            'message': '任务创建成功',
            'task': TaskService.to_dict_with_extra(task)
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建任务失败: {str(e)}")
        return jsonify({'error': str(e)}), 400

@bp.route('/api/tasks/<task_id>', methods=['PUT'])
def update_task(task_id):
    """更新任务"""
    try:
        task = TaskCenter.query.get_or_404(task_id)
        data = request.get_json()
        
        # 更新可修改的字段
        if 'description' in data:
            task.description = data['description']
        if 'batch_size' in data:
            task.batch_size = data['batch_size']
        if 'interval' in data:
            task.interval = data['interval']
        if 'priority' in data:
            task.priority = data['priority']
        if 'execution_type' in data:
            task.execution_type = data['execution_type']
            
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '任务更新成功',
            'task': TaskService.to_dict_with_extra(task)
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新任务失败: {str(e)}")
        return jsonify({'error': str(e)}), 400

@bp.route('/api/tasks/<task_id>', methods=['DELETE'])
def delete_task(task_id):
    """删除任务"""
    try:
        task = TaskCenter.query.get_or_404(task_id)
        
        if task.status == 'running':
            return jsonify({'error': '无法删除运行中的任务'}), 400
            
        db.session.delete(task)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '任务已删除'
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除任务失败: {str(e)}")
        return jsonify({'error': str(e)}), 400

@bp.route('/api/tasks/<task_id>/start', methods=['POST'])
def start_task(task_id):
    """启动任务"""
    try:
        task = TaskCenter.query.get_or_404(task_id)
        
        if TaskService.start(task):
            return jsonify({
                'success': True,
                'message': '任务已启动',
                'task': TaskService.to_dict_with_extra(task)
            })
        else:
            return jsonify({'error': '启动任务失败'}), 400
            
    except Exception as e:
        logger.error(f"启动任务失败: {str(e)}")
        return jsonify({'error': str(e)}), 400

@bp.route('/api/tasks/<task_id>/stop', methods=['POST'])
def stop_task(task_id):
    """停止任务"""
    try:
        task = TaskCenter.query.get_or_404(task_id)
        
        TaskService.stop(task)
        return jsonify({
            'success': True,
            'message': '任务已停止',
            'task': TaskService.to_dict_with_extra(task)
        })
        
    except Exception as e:
        logger.error(f"停止任务失败: {str(e)}")
        return jsonify({'error': str(e)}), 400

@bp.route('/api/tasks/<task_id>/execute', methods=['POST'])
async def execute_task(task_id):
    """执行任务"""
    try:
        task = TaskCenter.query.get_or_404(task_id)
        
        if task.status != 'running':
            return jsonify({'error': '任务未启动'}), 400
            
        # 获取要处理的产品
        products = TaskService.get_batch_products(task)
        if not products:
            return jsonify({'error': '没有可处理的产品'}), 400
            
        # 更新最后执行时间
        TaskService.update_last_run_time(task)
        
        # 根据任务类型选择处理器
        if task.task_type == 'translation':
            result = await TaskHandler.handle_translation(task, products)
        else:
            result = TaskHandler.handle_publish(task, products)
            
        return jsonify({
            'success': True,
            'message': '任务执行完成',
            'result': result,
            'task': TaskService.to_dict_with_extra(task)
        })
        
    except Exception as e:
        logger.error(f"执行任务失败: {str(e)}")
        return jsonify({'error': str(e)}), 400

@bp.route('/api/tasks/<task_id>', methods=['GET'])
def get_task(task_id):
    """获取单个任务详情"""
    try:
        task = TaskCenter.query.get_or_404(task_id)
        return jsonify(TaskService.to_dict_with_extra(task))
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        return jsonify({'error': str(e)}), 500 