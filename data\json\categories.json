{"21523": "Table tops", "21528": "Drawers, bedding boxes", "21503": "Furniture castors", "21508": "Furniture handles", "21513": "Furniture legs and feet", "21563": "Other furniture accessories", "21518": "Soft wall panels", "5102": "Rodents, moles destruction", "5099": "Insects", "22421": "Air cleaners", "386": "Bathroom fans", "3293": "Air regulation equipment accesories", "163": "Air humidifiers, cleaners", "562": "Conditioners", "8987": "Air dehumidifiers, moisture absorber", "5654": "Meteorological stations, thermometers", "3257": "Fans", "15222": "Mountain skis", "4508": "Helmets", "15232": "Mountain ski boots", "15237": "<PERSON>ain skiing poles", "15242": "Bags for mountain skiing equipment", "2759": "Ski goggles", "15252": "Other alpine skiing goods", "751": "Ski protection", "21855": "Snowboards", "1136": "Home speakers and Soundbar Systems", "511": "Radios and alarm clocks", "5993": "Music CD's/DVD's", "162": "<PERSON><PERSON><PERSON><PERSON>", "4439": "Stereos", "10092": "Record players and turntables", "156": "Home Theater Systems", "157": "Home cinema systems", "1913": "Roof racks", "1916": "Roof rails", "1919": "Bike carriers", "12270": "Start-up and special-purpose baby mixes", "12275": "Continuous feeding mixes for babies", "12280": "Mixes for babies", "12285": "Gruel for babies", "12290": "Snacks and teas for babies", "2696": "Diapers", "14825": "Swaddle products", "8840": "Potties", "15145": "Health care products for babies", "12075": "Bathing gear", "14945": "Envelopes, sleeping bags, pillows for babies", "3305": "Educational mats", "14865": "Protections", "14950": "Higiene products for babies", "14965": "Napkins and sheets", "2684": "Mobile nanny", "14980": "For cooking baby food", "15045": "Baby bottles and accessories", "14990": "Thermoses and thermo bags", "14995": "Baby bottle warmers and sterilizers", "15005": "Bibs", "15015": "Children's dishes, containers for milk and food", "15025": "Chewing toys", "2663": "Feeding chairs", "15030": "<PERSON><PERSON><PERSON>", "12110": "Stamps for babies", "8690": "<PERSON><PERSON>", "2660": "Playards", "3185": "Baby Bouncers and swings", "14430": "Baby carriers", "14720": "Other basketball items", "14705": "Basketball balls", "14710": "Basketball stands", "14715": "Basketball boards", "1310": "Bathroom radiators", "455": "Baths", "392": "Water mixers", "456": "<PERSON><PERSON><PERSON>", "12115": "Urinals", "393": "Bathroom accessories", "458": "Wash<PERSON>ins", "7919": "Bathroom equipment for people with special needs", "459": "<PERSON><PERSON><PERSON>", "3284": "Mixers and showers accesories", "3287": "<PERSON><PERSON><PERSON>", "1334": "Accesories for baths, shower cabins", "1337": "Accesories for toilets, bidets", "8714": "Plumbing hoses, valves", "14320": "Bathroom cabinets", "14330": "Bathroom Sets", "14335": "Bathroom mirrors", "2825": "Battery chargers", "1886": "Batteries", "2774": "foot spa / foot massage bath", "2777": "massagers", "172": "Hair dryers", "20768": "Electric toothbrush tips", "559": "toothbrush", "3572": "blood pressure monitor", "177": "epilators", "20428": "Heating products", "526": "trimer", "178": "electric razor", "527": "Hair shaping and straightening devices", "18371": "Inhalers", "4727": "Termometer", "5762": "face care devices, inhaler compressor", "22153": "Oral irrigators", "2771": "manicure, pedicure devices", "3377": "<PERSON><PERSON><PERSON>", "3386": "Blankets", "3389": "Pillows", "5051": "tablecloths, kitchen towels", "3392": "Bedcovers and rugs", "3395": "Bedding for babies, children", "10061": "Sheets", "7973": "Decorative pillows", "3398": "Bedclothes", "20648": "Kitchen towels, gloves, aprons", "21298": "Furniture covers", "4121": "<PERSON>ress topper", "3113": "Wardrobes", "14210": "Night stands", "1262": "Bed", "14215": "Cosmetic tables", "423": "Matt<PERSON>", "4118": "Bed frame", "3281": "Bedroom sets", "15422": "Bicycle computers, navigation", "15472": "Bicycle seats and seat covers", "15427": "Bags, phone holders", "15477": "Bicycle carriers", "15437": "Bicycle bells, alarms", "15482": "Mudguards", "15392": "Lights and reflectors for bicycles", "15447": "Bicycle bottles and bottle holders", "15487": "Bicycle tubes and tires", "15397": "Locks for bicycles", "15452": "Cyclist clothing", "15492": "Bicycle handlebars", "15402": "Pumps for bicycles", "15457": "Cyclist gloves", "15497": "Other parts of bicycles", "15407": "Bicycle seats for children", "15462": "Cyclist backpacks", "15502": "Other bicycle accessories", "15412": "Bicycle trailers, strollers", "15467": "Tools, care products for bicycles", "15512": "Protections", "20733": "Electroroller", "1475": "Bicycles", "2702": "Scooters", "9118": "Skateboards", "16297": "Sports glasses", "3143": "Roller skates", "12715": "Electric scooters", "10334": "Tricycles", "22029": "Electric bicycles", "21867": "Electric scooter accessories", "15507": "Helmets", "10457": "Feed", "10466": "Nests, feeders, cages", "688": "Boats", "1874": "Accessories for boats and kayaks", "9032": "Life vests", "17761": "Book marks", "20448": "Calendars, notebooks", "16990": "Books for teens and young people", "20603": "Books for children", "16960": "Books for little ones", "16975": "Tales", "16980": "Educational books", "16985": "Coloring books", "17090": "Economics books", "17095": "Marketing books", "17100": "Social Science Books", "17105": "Foreign language teaching materials", "17080": "Historical books", "17085": "Encyclopedias and reference books", "115": "Camcorders", "310": "Accessories for video cameras", "3641": "Video camera chargers", "3569": "Batteries for Camcorders", "4169": "Cases for Camcorders", "8954": "Action cameras", "11930": "Car windows tinting films", "1481": "Wheel covers", "10322": "Muffler tips and mufflers", "10325": "Armrests", "21783": "Car lights", "10328": "Shift knobs", "10331": "Steering wheel covers", "22217": "Auto films for windows according to spec. order", "2282": "Wipers", "3341": "Stereos", "3344": "Speakers", "6212": "USB adapter audio music changers", "3347": "Amplifiers", "22510": "Accessories for multimedia", "4088": "Car air freshners", "7901": "Cleaning cloths for cars", "4523": "Car chemicals", "18182": "Car paint", "11312": "Windscreens washers and coolants", "21453": "Electric car charging stations", "5753": "FM modulators", "4055": "Car parking systems", "5756": "Voltage converters", "5837": "Car light bulbs", "5843": "Car 12V accesories", "5636": "Textile car mats", "5132": "Trunk mats", "4517": "Universal car mats", "4520": "Car rubber mats", "21618": "Engine parts", "21623": "Suspension parts", "21448": "Transmission parts", "17716": "Starters", "22212": "Wiper motors", "21949": "Car interior parts", "5213": "Carpets", "4190": "Door mats", "20413": "Rims", "6695": "Video Recorder", "80": "GPS receivers", "10424": "Cat litter boxes", "10427": "Cat scratching posts", "10490": "Wet/canned cat food", "10442": "Cat leashes, collars, harnesses", "10493": "Dry cat food", "10451": "Toys for cats", "10412": "Cat snacks", "10418": "Cat vitamins, supplements, anti-parasitic products", "10421": "Cat litter", "14435": "Balloons", "5849": "Gift packaging items", "14440": "Disposable dishes for celebrations", "9889": "Fest atributes", "20244": "Envelopes, postcards", "8675": "Carnival costumes", "5804": "Graves candles", "9937": "Lithuanian fans merchandise", "3374": "Flags", "4547": "Christmas lights", "4583": "Christmas atributes", "4589": "Christmas tree toys", "4544": "Christmas trees, wreaths, and christmas trees stands", "14570": "Bodysuits for babies", "14615": "Pullovers for boys", "14575": "Bodies for babies", "14620": "Shorts for babies", "14585": "Clothing sets for babies", "14625": "Shirts for babies", "14590": "Combies for babies", "14595": "Caps, mittens, scarfs for babies", "14600": "Trousers for babies", "14560": "Jackets for babies", "14605": "Swimming suits for babies", "14565": "Dresses and skirts for babies", "14610": "Socks, tights for babies", "14480": "Pullovers for boys", "14485": "Shorts for boys", "18282": "Caps, scarves for boys", "14450": "Jackets for boys", "14490": "Shirts for boys", "14455": "Sleeping clothes for boys", "14460": "Trousers for boys", "14465": "Swimming shorts for boys", "14470": "Socks for boys", "19451": "Sets for boys", "14475": "Underwear for boys", "14525": "Dresses for girls", "18277": "Caps, scarves for girls", "14530": "Trousers for girls", "14535": "Sleeping clothes for girls", "14500": "Shorts for girls", "14540": "Sweaters for girls", "14505": "Jackets for girls", "14545": "Socks, tights for girls", "14510": "Swimming suits for girls", "14550": "Underwear for girls", "19446": "Sets for girls", "14515": "Shirts for girls", "14520": "Skirts for girls", "5777": "textile cleaner", "175": "iron", "21133": "Ironing systems", "164": "sewing machine", "21138": "Steam irons", "12055": "Winter clothes for children", "12070": "Accessories for children", "21944": "Rainwear for children", "17501": "Collectible models", "21128": "Numismatics", "21964": "Postage stamps", "21733": "Trading cards", "21984": "Investment gold", "3620": "Adapters, USB Hubs", "59": "Power supplies (PSU)", "13195": "Motherboards", "13240": "Computer fans", "13200": "<PERSON> <PERSON><PERSON><PERSON>", "13245": "Thermal pastes", "13205": "Optical drives", "13250": "Controllers", "13210": "Sound cards", "4049": "Internal hard drives (HDD, SSD, Hybrid)", "13255": "TV tuners, FM, video cards", "13215": "Processor coolers", "13260": "Accessories", "56": "Video cards", "13225": "Graphic cards coolers", "13265": "Supplements for components", "13230": "Water cooling - accessories", "58": "Operational memory (RAM)", "13190": "Central Processing Unit (CPU)", "13235": "Water cooling - sets", "160": "Audio speakers", "98": "TV receivers", "67": "Mouse", "68": "Keyboards", "269": "Web & IP cameras", "3452": "Headphones", "57": "Uninterruptible power supply (UPS)", "20713": "Microphones", "22515": "Earphones accessories", "2294": "Beauty products accessories", "2303": "Accessories for coffee machines", "9922": "Household appliances accessories", "4718": "Hood filters", "2300": "Vacuum cleaner accessories", "142": "Cosmetics for children and mothers", "16047": "Soaps", "201": "Body creams, lotions", "10054": "Essential, cosmetics oils, hydrolats", "4133": "Shower gels, soaps", "3230": "Sun, solarium protection creams", "10707": "Massage oils", "4136": "Body scrubs", "21653": "Self-tanning creams", "3551": "Protections from mosquitoes, ticks", "3554": "Anti-cellulitis, firming products", "16027": "Tanning creams", "6194": "Facial cleaning products", "200": "Face creams", "15977": "Face masks, eye masks", "21663": "Facial massagers", "6188": "Facial masks, serums", "6191": "Eyes creams, serums, masks", "9166": "Massage oils", "4490": "Condoms", "4493": "Care products", "4484": "Pheromones", "4487": "Lubricants", "20364": "Glued models", "18057": "Painting by numbers", "17862": "Diamond mosaics", "15272": "Cross-country skiing boots", "15277": "Cross-country skiing poles", "15287": "Cross-country skis care products", "15292": "Other cross-country skiing goods", "15262": "Cross-country skis", "15267": "Cross-country ski bindings", "62": "External hard drive disk (HDD)", "107": "USB Storage", "9331": "External hard drive cases", "15367": "Diving sets", "15377": "Kick fins", "15382": "Other diving goods", "15357": "Diving masks", "15362": "<PERSON><PERSON><PERSON>", "9877": "Skylights", "5156": "Exterior doors", "5642": "Door handles, locks", "22326": "Plastic windows (PVC windows)", "20618": "Locks", "17140": "Roof coverings", "5159": "Interior doors", "20708": "Inspection doors, accessories", "7979": "Locks", "7853": "Door canopies", "20583": "Non - alcoholic beverages", "18017": "Juices, nectars", "17746": "Drinks", "18272": "Water", "7895": "Block planes", "845": "Millings", "17791": "Vibro panels", "854": "Saws, cutting machines", "7769": "Compressors", "857": "Powered grinders and sanders", "8942": "Concrete mixers", "860": "Electric hammers", "863": "Blowers", "9349": "Paint sprayers", "11006": "Industrial vacuum cleaners", "872": "Cordless tools and drills", "1106": "Electricity generators", "7892": "Welding machines, soldering irons", "8384": "Fences and accessories", "8717": "Gate systems", "7871": "Liquid fertilizers", "7874": "Loose fertilizers", "7877": "Plant Care", "16935": "Biographies, autobiographies, memoirs", "16940": "Prose", "21628": "Detectives", "16945": "Novels", "17852": "Comics", "16950": "Poetry", "16925": "Classic", "16930": "Fantasy, mysticism", "10496": "Aquariums accessories", "10502": "Aquariums and equipment", "10508": "Fish food", "15592": "Fishing floats, bite indicators", "15527": "Rods, rod holders, stands", "15597": "Feeders", "15532": "Clothing for fishing", "15602": "Groundbaits", "15557": "Fishing boxes, cases, backpacks", "15607": "Lines", "15612": "Wobblers", "15562": "Fishing nets", "15622": "Other fishing accessories", "15577": "Fishing weights", "15582": "Hooks, hooklenghts", "15587": "Fishing reels", "11317": "Plinths", "10681": "Tile floors", "5201": "Laminated flooring", "13675": "Connection profiles", "9871": "Terrace floor", "17686": "Artificial flowers", "17691": "Floristic equipment", "20533": "Natural flowers", "8963": "Roses", "17611": "Milk products", "17776": "Prepared food", "15767": "Bars", "15817": "Supplements for joints", "15772": "Other supplements", "15782": "Energetics", "15827": "Fat Burners", "15787": "Functional food", "15832": "Slimming supplements", "15747": "Amino acids", "15792": "Glutamine", "15837": "Testosterone promoters", "15752": "<PERSON><PERSON>", "15802": "<PERSON><PERSON><PERSON>", "15842": "Vitamins", "15757": "Nitroxes", "15807": "L-Carnitine", "15857": "Recovery supplements", "15762": "<PERSON>teins", "15812": "Mass gainers", "14730": "Football shoes", "14735": "Football balls", "14740": "Football gates and nets", "14745": "Football clothing and other goods", "14750": "Goalkeeper gloves", "10391": "Dog bowls, pet food containers", "10370": "Dry dog food", "10394": "Toys for dogs", "10373": "Wet/canned dog food", "10400": "Dog training products", "10376": "Dog snacks", "10403": "Travel accessories for dog", "10379": "Dog vitamins, supplements, anti-parasitic products", "10382": "Bearings, cushions", "10415": "Dog clothes", "21553": "Cosmetics for animals", "21813": "Leashes for dogs", "10385": "Dog transport cages, bags", "21558": "Care products for animals", "10388": "Dog leashes, collars, harnesses", "22356": "Food for exotic animals", "22351": "Goods for exotic animals", "22341": "Goods for farm animals", "22346": "Food for farm animals", "8666": "car refrigerators", "8669": "Refrigerators", "8672": "Freezers, chest freezers", "13040": "Wine refrigerator", "21333": "Furniture for beauty salons", "3038": "Mirrors", "2627": "Toys for boys", "2615": "Toys for babies", "2624": "Toys for girls", "16062": "Gaming merchandise", "21538": "Game consoles", "267": "Gaming consoles", "21543": "Virtual reality glasses", "268": "Accessories for gaming", "21548": "Game steering wheels", "5765": "Computer games", "7856": "Flower stands, pot holders", "6077": "Outdoor lighting", "7955": "Garden decorations", "7997": "Peat, compost", "7898": "Watering accessories, pump sprayer", "20698": "Chain saw", "8945": "Outdoor containers, compost bins", "3662": "Measures against mosquitoes and ticks", "8390": "Houses, roofs, woodsheds", "11302": "Decorative rubble", "5660": "Greenhouses", "9160": "Microorganisms, bacteria", "929": "Gardening tools", "11840": "Outdoor tables", "11795": "Outdoor furniture sets", "11845": "Outdoor benches", "11805": "Sun beds", "11850": "Outdoor swings", "11810": "Hammocks, stands", "11815": "Outdoor chairs, armchairs, pouffes", "11820": "Pillows, linen, protective", "12080": "Children's outdoor furniture", "11825": "Arbors", "11830": "Umbrellas, awnings, stands", "10316": "Wheelbarrows", "1229": "Leaf Blowers, garden shredders and sweeping machines", "5738": "Garden lawn tractors", "5741": "Aerators, Cultivators", "6710": "Temperature and moisture meters", "17756": "Garden technique parts", "7889": "Log Splitters", "3317": "Lawn mowers", "13937": "Robot lawn mowers", "18626": "Ground drills", "11880": "Hedge, grass scissors", "3320": "Grass trimmers", "7943": "Garden pool maintenance", "11565": "Activity gifts", "3425": "Pigu.lt gift vouchers", "21872": "Funny gifts", "22437": "Donation goods", "10057": "Zippo lighters", "10015": "Business gifts", "15877": "Grills", "15887": "Smokehouses", "15897": "BBQ accessories", "15907": "Fire places", "15922": "Charcoal, briquettes, shavings", "15872": "Barbeques", "17576": "Sweets", "17586": "Oil, vinegar", "17531": "Snacks, chips", "17591": "Groats, flakes, porridge", "2831": "coffee", "17536": "Soups", "17596": "Flour", "17541": "Breakfast cerials", "17601": "Pasta", "21143": "Tea", "17546": "Nuts, seeds, dried fruits", "17606": "Cooking additives", "17561": "<PERSON><PERSON><PERSON>", "17571": "Canned food", "14770": "Balance boards and cushions", "14820": "Resistance bands, rings", "14880": "Training gloves", "14775": "Espanders", "14885": "Other fitness products", "14780": "Gymnastic balls", "14830": "Pull up bars", "14890": "Press trainers", "14785": "Gymnastic hoops and sticks", "14835": "Jumping ropes", "14905": "Training ladders, reaction balls", "14795": "Gymnastics roller", "14850": "Whistles", "14910": "<PERSON><PERSON><PERSON>", "14800": "Wall bars", "14855": "Weight balls", "14760": "Aerobic steps", "15157": "Yoga goods", "14805": "Exercise Mats", "14765": "Push up bars", "14815": "Training cones, hurdles", "14870": "Resistance bands, training straps", "7817": "Balsams, conditioners", "7820": "Hair styling products", "15972": "Hair accessories", "7823": "Hair strengthening products", "7826": "Products for colored hair", "7829": "Brushes and hair accessories", "7811": "Shampoo", "14285": "Hallway wardrobe", "14290": "Hallway mirrors", "14310": "Hallway cabinets", "14265": "Hallway sets", "14270": "Shoe cabinets, shelves and benches", "14275": "Clothes rack", "20668": "Chimneys", "553": "Heaters", "7904": "Central heating radiators", "1088": "<PERSON><PERSON><PERSON>", "1091": "Fireplaces, inserts", "3304": "Accesories for heating equipment", "1094": "Heating boilers", "1103": "Water boilers", "7301": "Floor and mirror heating mats", "20658": "Expansion vessels", "3497": "High pressure washers", "3500": "Accesories for high pressure washers", "17040": "Architecture books", "17005": "Recipe books", "17010": "Photography books", "17015": "Travel guides, descriptions", "21638": "Taro cards", "17020": "Gardening books", "17025": "Fashion books", "17030": "Design books", "17035": "Books about art", "7865": "steam purifying devices, windows cleaner", "13660": "Vacuum cleaners batteries", "20773": "Window washers", "165": "body scale", "145": "vacuum cleaner", "20543": "Vacuum cleaners-robots", "20548": "Vacuum cleaners-brooms", "146": "Washing mashines", "147": "Dryers", "3428": "Interior stickers", "15050": "Details of interior", "7862": "Frames", "7757": "Giclee canvas, pictures", "3503": "Mobile interior walls", "7925": "Candle holders, candles", "7928": "Vases", "4742": "Watches", "10296": "Gutters", "8003": "Home mailbox, house numbers", "8240": "Stairs", "7772": "Safes", "3671": "Water filters and water cleaning equipment", "20888": "Security systems, controllers", "20893": "Intercoms", "9175": "Door bells", "20898": "Security system accessories", "20873": "Security cameras", "20878": "Alarms", "20883": "Sensors", "7814": "Smoke detectors", "20463": "Toilet paper, paper towels", "10051": "Air fresheners", "6086": "Cleaners", "6089": "Washing detergent", "6092": "Cleaning supplies and accessories", "6095": "Clothing and footwear care products", "10911": "Dishwashing detergent", "9940": "Trash Bags", "5831": "Hangers, clothing bags", "4112": "Cloth dryer rack and accessories", "4115": "Ironing board", "21293": "Trash cans", "5186": "Store bags", "271": "Infant car seats", "15172": "Carseat accessories", "12470": "Drones", "10045": "Currency detector", "12485": "Open source electronics", "20738": "Servers", "49": "Desktop computer", "14345": "Kids wardrobes", "14240": "Kids beds", "14355": "Kids chests of drawers", "14245": "Babies cribs", "14305": "Kids bean bags, armchairs, poufs", "14390": "Children's room furniture sets", "14255": "Kids cabinets", "14280": "Kids chairs and tables", "14340": "Kids shelves", "20578": "Spice jars, grinders", "12250": "Cutting boards", "12255": "Preservation crockery and accessories", "4100": "Cooking utensils, baking paper, forms", "3356": "Kitchen  tools", "3359": "Glasses, cups, jugs", "4106": "Vacuum Flasks, kettles, coffee makers", "20643": "Coffee pots, teapots", "10063": "Dishes, plates dinner set", "3362": "Pots, pressure cookers", "12240": "Cutlery", "10066": "Food storage dishes", "3365": "Fry<PERSON><PERSON>", "12245": "Knives and accessories", "4145": "Spice sets and mills", "1472": "Kitchen water mixers", "1391": "Kitchen sinks", "1469": "Kitchen sinks and mixers accesories", "14230": "Kitchen furniture accessories", "4151": "Dining room sets", "1253": "Chairs", "1259": "Tables", "14220": "Kitchen cabinets", "2168": "Kitchen furniture", "14225": "Kitchen worktops", "148": "<PERSON><PERSON>", "11340": "Garbage Disposals", "150": "Microwaves and convection ovens", "152": "Dishwashers", "11975": "Gas cookers", "153": "build in ovens", "11980": "Electric cookers", "319": "<PERSON><PERSON>", "11990": "Mini ovens", "3731": "Wall lights", "10072": "Downlights, LED panels", "4775": "Ceiling lights", "3737": "Floor lamp", "21999": "Light boards", "3740": "Table lamps", "3746": "Kids lamps", "22503": "Rail lamps and rails", "8177": "Lam<PERSON> - Fans", "3728": "Hanging lamps", "10896": "Minus products", "11092": "GET AN EXTRA PERCENTAGE BACK", "99": "Bags, backpacks, cases", "3650": "Laptop chargers", "13670": "Laptop batteries", "50": "Laptop", "315": "Cooling and other accessories for computers", "438": "Trampolines", "12085": "Outdoor games", "11068": "Inflatable furniture and toys", "17947": "Hunting supplies", "19876": "Components for solar power plants", "971": "Extenders", "4757": "Flashlight", "5192": "Chargers", "10075": "Power supplys", "6767": "Batteries", "10866": "Electrical switches, sockets", "1994": "Timers, thermostats", "17165": "Textile Cables and electrical terminals", "3407": "Electricity bulbs", "10069": "Led strips", "14190": "Corner sofas", "3104": "<PERSON><PERSON>", "14195": "Living room armchairs", "3107": "Section", "3215": "TV tables", "14200": "Sets of upholstered furniture", "3110": "Chest of drawers, cupboards", "14205": "Living room cabinets", "14391": "Console tables", "8051": "Beanbag chairs and pouffes", "6716": "Coffee tables", "1268": "Sofas, armchairs and upholstered furniture", "9023": "Showcases and dressers", "5987": "Makeup brushes and accessories", "141": "Eye shadow, eye pencils, mascara", "16007": "Eyebrow dye, pencils", "3587": "Lipsticks, glosses, balsams", "16012": "Bronzers, blushers", "7259": "Manicure, pedicure products", "3590": "Powders, foundation, bronzers, blushes", "16017": "Fake eylashes, curlers", "3593": "Nail polish, strengthening", "16022": "Cosmetic bags and cosmetic mirrors", "20503": "Fish products", "17616": "Sausages", "17781": "Hearing Aids", "21038": "COVID-19 rapid tests", "22163": "Medical footwear", "20379": "Medical clothing", "12140": "Nursing", "22208": "Diapers, pads, blankets for adults", "12145": "First aid", "20438": "Balms, ointments", "9142": "Men's scarfs, hats and gloves", "9328": "Men's sunglasses", "4955": "Men's watches", "9145": "Men's belts", "6830": "Men's bags", "9928": "Men's jewellery, accessories", "9148": "Men's umbrellas", "9151": "Men's wallets", "10827": "Ties", "5723": "Men's t-shirt", "7382": "Men's ski clothes", "5726": "Men's shirts", "9050": "Men's coats, jackets", "18012": "Men's coats", "9053": "Men's blazers, vests", "9056": "Men's jumpers", "19161": "Suits", "9059": "Men's jeans, pants", "17972": "Men's vests", "5717": "Men's sports clothes", "9068": "Men's socks", "19576": "Men's shorts", "17977": "Men's trousers", "5720": "Men's knitwear", "6749": "Men's boots", "6758": "Men's sport shoes", "5786": "Men's slippers", "21778": "Rubber boots for men", "4039": "Men's panties, boxers", "17827": "Swim shorts, tight", "4042": "Men's underwear t-shirts", "9065": "Men's pyjamas, bathrobes", "9071": "Men's thermo clothes", "85": "Smartphones", "3563": "Phone cables", "4148": "Hands free equipment", "3566": "Phone chargers", "21043": "Telephone parts and tools for their repair", "5042": "Phone cases", "4154": "Memory cards", "396": "Phone accessories", "5222": "Power Banks", "5177": "Phone holders", "5228": "Protective phone screens", "10661": "Selfie sticks", "3560": "Phone batteries", "8837": "Telescopes & microscopes", "11650": "Binoculars", "11580": "Landline phones", "11610": "Smart devices and accessories", "17867": "Thermal cameras", "463": "Digital photo frames", "74": "Monitors", "13720": "Monitor holders", "11720": "Moto boots", "11690": "Helmets", "11700": "Moto jackets", "11705": "Moto pants", "11710": "Moto protections", "12000": "Moto accessories", "22203": "Motorcycle tires, inner tubes", "11985": "Moto accumulators", "11995": "Moto oil", "159": "Voice recorders", "122": "MP3 players", "21433": "DJ remotes", "21443": "Musical instrument accessories", "21403": "Guitars", "21408": "Percussion instruments", "21413": "Keyboard musical instruments", "21418": "Wind instruments", "21423": "Violins", "21428": "Percussion", "4454": "Sex products sets", "4457": "BDSM and fetish", "4460": "Penis, vagina pumps", "4478": "Souvenirs, gifts for adults", "21378": "Appliques, decorations, stickers", "21343": "For knitting", "21388": "For the production of jewelry", "21348": "Crochet tools", "21353": "Sewing tools", "21798": "Fabrics", "21358": "Embroidery tools", "21363": "Felting accessories", "21368": "Leather processing tools", "21373": "Pins, needles, safety pins", "13855": "Routers", "13860": "Access Points", "13865": "Switch", "13870": "Range Extender", "1907": "Paper shredders", "19146": "Interactive whiteboards", "8027": "Projector screens", "69": "Scanner", "71": "Printers", "18386": "Printer accessories", "298": "Projectors", "3611": "Projectors accessories", "3161": "Computer desks", "3098": "Office chairs", "2855": "Motor oil", "2762": "Oil accesories", "812": "Other oils", "22004": "Glasses", "22014": "Contact lenses", "22498": "Accessories for glasses and lenses", "22019": "Contact lens liquid", "22024": "Eye drops", "8222": "Happy aprons", "20179": "Original socks", "11086": "Jumpers", "20249": "Original hats", "20254": "Original underwear", "10634": "Funny t-shirts", "10836": "Original money boxes", "8219": "Watches - alarm clocks", "10841": "Keychains", "22439": "Globes", "8198": "Other original gifts", "20344": "Maps", "8204": "Parties and celebrations", "20349": "Original pillows, covers", "8207": "Original mugs", "22246": "Trailers and their parts", "8030": "Seat covers, accesories", "3449": "Car accesories", "461": "Breathalysers", "15717": "Pools", "15942": "Pool filters", "15722": "Chemicals for pools", "15727": "Pool accessories", "10340": "Electric cars for kids", "12105": "Children's playhouses", "5996": "Water, sand and beach toys", "2639": "Sandboxes and sand", "15147": "Slides", "15152": "Swings", "10337": "Balance bikes", "4388": "Sealing materials", "4391": "Glue", "7844": "Primers, fillers and other", "8948": "Paints, varnishes, thinners", "7847": "Impregnants", "8543": "Varnishes, thinners", "10236": "Painting tools", "369": "Children perfumes", "3221": "Women fragrance cosmetics", "11860": "Home Scents", "352": "Women perfumes", "353": "Men perfumes", "3203": "Men fragrance cosmetics", "17050": "Self-help books", "17055": "Spiritual Books", "17060": "Books on parenting", "17065": "Relationship Books", "17070": "Books on healthy lifestyle and nutrition", "6200": "Shaving products and cosmetics", "4127": "Toothbrushes, toothpastes / Oral care", "4130": "Deodorants", "10078": "Cotton pads, wet wipes", "4139": "Depilatories", "4142": "Pads, tampons, washes / Personal Care Products", "21683": "Tampons, sanitary towels for critical days, panty liners", "309": "Accessories for cameras", "6080": "Filters for lenses", "117": "Compact digital cameras", "11575": "Lenses", "4163": "Bags, camera cases and lenses", "17857": "Photographic lighting equipment", "2609": "Memory cards for cameras", "11590": "Instant Camera", "462": "Camera batteries", "504": "Camera chargers", "3488": "Camera stands", "6044": "Garden pots", "6047": "Garden pots for sprouting", "6050": "Rectangular flowerpots", "6053": "Plates and accessories for garden pots", "312": "Cartridges for laser printers", "313": "Cartridges for ink printers", "14985": "Pencil cases", "18331": "Textbooks", "18336": "Exercise books", "3233": "School bags, pencils, sport bags", "12235": "Workbooks and paper goods", "8102": "Office products", "17215": "Writing instruments", "15702": "Touristic and travel backpacks", "17220": "Drawing, painting supplies", "3206": "Bad<PERSON>ton", "11645": "Squash", "22238": "Pa<PERSON>", "4532": "Radio Antennas", "4535": "CB Radios, Two-way radios", "18322": "Church articles", "17751": "Church candles, candlesticks", "10484": "Rodent cages, accessories", "10472": "Rodent food", "10478": "Rodent litter, hay", "5207": "Rods", "6797": "<PERSON><PERSON><PERSON>", "3653": "Mosquito", "5210": "Blinds", "3461": "roller blind", "1517": "First aid kits", "1520": "Fire extinguishers", "11625": "Saunas, sauna accessories", "11620": "Saunas, sauna stoves", "9094": "Flower bulbs", "5129": "Grass mixes and blends", "5057": "Vegetable seeds", "5060": "Flowers seeds", "5063": "Spices seeds", "21924": "Plumbing connections, valves", "21939": "Radiator valves", "11074": "Sexy clothes for women", "11080": "Sexy clothes for men", "14665": "Flats for kids", "14670": "Slippers for kids", "14680": "Sandals for kids", "14635": "Winter shoes for kids", "14685": "Boots for kids", "14640": "Water shoes for kids", "14645": "Sneakers for kids", "14655": "Rain boots for kids", "14660": "Clogs for kids", "1322": "Shower trays", "9886": "Shower channels", "11940": "Hydromassage shower cabins", "1325": "Shower sets and panels", "11945": "Shower doors and walls", "529": "Shower cabins", "168": "sandwich maker", "510": "fryer", "182": "kettle", "818": "Blenders", "170": "mixer", "550": "kitchen scale", "149": "coffee maker", "980": "electric grill", "21874": "Plant nurseries, lamps for plants", "522": "bread maker", "19151": "Vacuum sealers", "3437": "water filters", "12520": "Exclusive cooking appliances", "2723": "food dehydrator, vacuum sealer", "22311": "Carbonated water devices and accessories", "523": "coffee mill / grinder", "9011": "slicers, knife sharpeners", "166": "waffle maker, crepe maker", "179": "food processors", "2768": "food steamer, multicooker", "167": "toaster, fryer", "1289": "meat grinder", "509": "Mixers and choppers", "180": "juicer", "20718": "Water apparatus", "13730": "Smartwatch", "13735": "Fitness tracker", "13740": "Accessories for smart watches and bracelets", "108": "Antivirus software", "109": "Office software", "110": "Operating systems (OS)", "536": "Heart rate monitors, pedometers, chronometers", "15187": "Ball pumps and needles", "437": "Handball", "21743": "Disc golf", "20041": "Golf", "21759": "Horseback riding", "15822": "Massagers", "12010": "Floorball and hockey", "20598": "Ballet", "1028": "Fighting sports", "3512": "Darts", "10712": "Baseball", "3236": "Backpacks and bags", "533": "Sports medicine", "4205": "Walking sticks", "13035": "Luggages, travel bags", "2648": "Baby strollers", "2654": "Baby strollers accessories", "8843": "Breast pumps", "14405": "Nursing pillows", "14410": "Hygiene supplies for mothers", "14420": "Nursing supplies", "15332": "Swim vests and sleeves", "15337": "Swim paddles", "15342": "Other swimming goods", "15307": "Swimming goggles", "15347": "Water shoes", "15312": "Swimming caps", "15352": "Water weights", "15317": "Swimming sets", "15322": "Swimming boards, floatings", "15135": "Billiards", "15732": "Other game tables", "15125": "Balls for table tennis", "15130": "Table tennis nets", "15115": "Table tennis tables and covers", "15120": "Table tennis rackets, cases and sets", "8180": "E-book reader", "8966": "Tablet, E-reader accessories", "5564": "Tablet computers", "22188": "tablets for Drawing", "5237": "Tablet, E-reader cases", "2588": "TV receivers (set-top boxes)", "2594": "Cables and Adapters", "101": "TV mounts, holders", "2597": "TV antennas and accessories", "5651": "Multimedia Player", "4202": "Smart TV accessories", "158": "TV's", "20683": "Outdoor tennis shoes", "442": "Outdoor tennis goods", "19141": "Industrial scales", "4214": "Storage shelves", "1139": "Snow shovels, snow blowers, snow pushers", "17997": "Fasteners", "20693": "Snow shovels, pushers", "3446": "Hand Tools", "4103": "Metal detectors", "7775": "Storage shelves systems", "1463": "Household ladders, ramps", "10717": "Tool boxes, holders", "13304": "Touristic  furniture", "13490": "Alpinism", "5639": "Inflatable mattresses and mats", "5162": "Bottles and thermo mugs", "10218": "Biotoilets", "433": "Tents", "3710": "Tourist equipments, clothes and shoes care products", "434": "Sleeping bags", "13299": "Touristic mattresses and mats", "15647": "Knives, multifunctional tools", "15652": "<PERSON>mp<PERSON><PERSON>", "15657": "Gas cookers, gas cartridges", "15662": "Touristic pots, dishes, cutlery", "15677": "Waterproof bags, cases, cloaks", "15687": "Lanterns and flashlights", "15637": "Coll bags, coll boxes and ice blocks", "15697": "Other touristic inventory", "15642": "Antislip soles, gaiters", "10669": "Puzzles", "2630": "Constructors and bricks", "440": "Football, ice hockey, billiard and other gaming tables", "3299": "Excitable games, poker", "18217": "Soft (plush) toys", "2621": "Educational toys", "12100": "Table games, brain teasers", "469": "Treadmills", "452": "Bike trainers", "470": "Elliptical trainers", "471": "Rowers trainers", "473": "Dumbbells, weights, bars&barbells", "2735": "Stepper trainers", "474": "Training benches and racks", "475": "Home gyms trainers", "1859": "Trainer accessories", "13294": "Sport trampolines", "12440": "Other trainers", "11660": "Summer tires", "11665": "Winter tires", "11670": "Universal tires", "4472": "Dolls / Masturbators", "4442": "Vibrators", "4475": "Rings / lugs and others", "4445": "Anal toys", "4448": "<PERSON><PERSON><PERSON>", "4451": "Vaginal beads", "12550": "Herbs, teas", "11865": "Vitamins and food supplements for well-being", "11870": "Vitamins and food supplements for beauty", "11875": "Vitamins and food supplements for immunity", "15085": "Volleyball balls", "15090": "Volleyball nets", "15095": "Volleyball guards", "15100": "Other volleyball goods", "10684": "Wall tiles", "8186": "Wallpaper", "10687": "Decorative stone", "8189": "Photo wallpaper", "10901": "Oilcloths", "16779": "Ceiling, wall decor elements", "8192": "Kids photo wallpaper", "22223": "Adhesive films according to spec. order", "7991": "Petrol water pumps", "4502": "Well covers", "4070": "Garden water pumps", "6032": "Hydrophores", "4061": "Clean water pumps", "4064": "Dirty water pumps", "4067": "Fountain pumps", "21989": "Wakeboards and their accessories", "11116": "Water rides and skiing", "20703": "Hidrocosts", "11122": "Kites and accessories", "3434": "Sledges", "8399": "Ice hockey", "2927": "Skates", "20493": "Shopping bags", "9325": "Sunglasses", "4952": "Women's watches", "5048": "Women's scarfs", "4597": "Women's wallets", "4675": "Women's belts", "5828": "Women's umbrellas", "9163": "Women's hats", "8630": "Women's gloves", "9874": "Women's bags", "7379": "Women's ski clothes", "20359": "Women's vests", "20458": "Women's shorts", "5696": "Women's sports clothes", "5699": "Women's dresses", "8969": "Women's skirts", "17952": "Blouses, shirts for women", "5702": "Women's t-shirts", "8972": "Women's blazers", "17957": "Jeans for women", "19156": "Suits for women", "5705": "Women's tops, shirts", "8975": "Women's jumpers", "17962": "Women's jackets", "20523": "Tights", "5708": "Women's knitwear", "8708": "Women's jeans, pants", "5711": "Women's socks", "20443": "Women's overalls", "7310": "Women's coats", "4985": "Women's rings", "4961": "Women's bracelets", "4964": "Women's earings", "16910": "Brooches", "4967": "Women's jewellery sets", "4970": "Women's necklaces", "17982": "Women's shoes", "5789": "Women's sport shoes", "2018": "Women's boots", "6731": "Women's balerina type, sandals shoes", "6740": "Women's slippers", "5669": "Women's riubber boots", "9041": "Women's thermo clothes", "4036": "Women's underwear t-shirts", "16342": "Swimsuits", "4021": "Women's bras", "17967": "Women's bathrobes", "4024": "Women's panties", "4030": "Women's pyjamas, nightwear", "4033": "Women's shapewear", "20728": "Information signs", "17390": "safety covers", "7961": "Head protection", "7964": "Work shoes", "7967": "<PERSON>nee<PERSON><PERSON>", "7970": "Work gloves", "7985": "Work clothes"}