import requests
import time
import asyncio
import aiohttp
import logging
from models import SystemSettings

logger = logging.getLogger(__name__)

class Selling51Translator:
    def __init__(self):
        """初始化翻译器"""
        self.api_url = "https://www.51selling.com/Tool/GeneralTranslate"
        self.cookie = SystemSettings.get_setting('translation_api_key', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        # logger.info(f"51selling翻译器初始化，cookie长度: {len(self.cookie) if self.cookie else 0}")
        
    def refresh_settings(self):
        """刷新设置"""
        self.cookie = SystemSettings.get_setting('translation_api_key', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        # logger.info(f"51selling翻译器设置已刷新，cookie长度: {len(self.cookie) if self.cookie else 0}")
    
    def translate(self, text, source_lang="AUTO", target_lang="ZH"):
        """翻译文本"""
        if not self.cookie:
            return {
                'success': False,
                'error': '请先设置51selling的Cookie'
            }
        if not text:
            return {
                'success': False,
                'error': '翻译文本不能为空'
            }

        headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            'x-requested-with': 'XMLHttpRequest',
            'cookie': self.cookie
        }
        
        data = {
            'FormatType': 'text',
            'SourceText': text,
            'SourceTexts[]': text,
            'Scene': 'title',
            'SourceLanguage': source_lang.lower(),
            'TargetLanguage': target_lang.lower()
        }
        
        # logger.info(f"51selling翻译请求数据: {data}")
        
        for retry in range(self.retry_count):
            try:
                response = requests.post(
                    self.api_url,
                    headers=headers,
                    data=data,
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('IsSuccess') and result.get('Data'):
                        return {
                            'success': True,
                            'text': result['Data'],
                            'source_lang': source_lang,
                            'target_lang': target_lang
                        }
                
                if retry < self.retry_count - 1:
                    time.sleep(self.retry_interval)
                    
            except Exception as e:
                if retry < self.retry_count - 1:
                    time.sleep(self.retry_interval)
                    continue
                
                return {
                    'success': False,
                    'error': f'翻译请求失败: {str(e)}'
                }

        return {
            'success': False,
            'error': f'翻译服务返回错误: {response.text}'
        }
    
    async def _translate_single_async(self, session, text, source_lang, target_lang):
        """异步发送单个翻译请求"""
        headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            'x-requested-with': 'XMLHttpRequest',
            'cookie': self.cookie,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        # 构建表单数据
        form_data = {
            'FormatType': 'text',
            'SourceText': text,
            'SourceTexts[]': text,
            'Scene': 'title',
            'SourceLanguage': source_lang.lower(),
            'TargetLanguage': target_lang.lower()
        }
        
        try:
            async with session.post(self.api_url, headers=headers, data=form_data) as response:
                if response.status == 200:
                    try:
                        content_type = response.headers.get('Content-Type', '')
                        if 'text/html' in content_type:
                            # 如果返回的是HTML，先获取文本内容
                            text_content = await response.text()
                            # 尝试从HTML中提取JSON数据
                            import re
                            json_match = re.search(r'\{.*\}', text_content)
                            if json_match:
                                import json
                                result = json.loads(json_match.group())
                            else:
                                raise ValueError("无法从HTML响应中提取JSON数据")
                        else:
                            result = await response.json()

                        if result.get('IsSuccess') and result.get('Data'):
                            return {
                                'success': True,
                                'text': result['Data'],
                                'target_lang': target_lang,
                                'error': None
                            }
                        else:
                            error_msg = f"翻译失败，API返回: {result}"
                            logger.error(error_msg)
                            return {
                                'success': False,
                                'text': None,
                                'target_lang': target_lang,
                                'error': error_msg
                            }
                    except Exception as e:
                        error_msg = f"解析响应失败: {str(e)}"
                        logger.error(error_msg)
                        return {
                            'success': False,
                            'text': None,
                            'target_lang': target_lang,
                            'error': error_msg
                        }
                else:
                    error_msg = f"HTTP错误: {response.status}"
                    logger.error(error_msg)
                    return {
                        'success': False,
                        'text': None,
                        'target_lang': target_lang,
                        'error': error_msg
                    }
        except Exception as e:
            error_msg = f"请求异常: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'text': None,
                'target_lang': target_lang,
                'error': error_msg
            }

    async def translate_product_async(self, text, source_lang="AUTO"):
        """异步翻译产品的标题或描述到四个目标语言"""
        if not self.cookie:
            return {lang: None for lang in ["LT", "LV", "ET", "FI"]}
        if not text:
            return {lang: None for lang in ["LT", "LV", "ET", "FI"]}

        # 使用大写的语言代码，因为返回结果使用大写
        target_langs = ["LT", "LV", "ET", "FI"]
        translations = {lang: None for lang in target_langs}
        failed_langs = []

        try:
            async with aiohttp.ClientSession() as session:
                # 首次尝试所有语言
                tasks = [self._translate_single_async(session, text, source_lang, lang) 
                        for lang in target_langs]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                # 处理结果
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        failed_langs.append(target_langs[i])
                        continue
                        
                    if result['success']:
                        translations[target_langs[i]] = result['text']
                    else:
                        failed_langs.append(target_langs[i])

                # 重试失败的语言
                if failed_langs:
                    for lang in failed_langs:
                        for retry in range(self.retry_count):
                            try:
                                result = await self._translate_single_async(session, text, source_lang, lang)
                                if result['success']:
                                    translations[lang] = result['text']
                                    break
                                else:
                                    logger.error(f"重试语言{lang}失败: {result.get('error')}")
                            except Exception as e:
                                logger.error(f"重试语言{lang}异常: {str(e)}")
                            if retry < self.retry_count - 1:
                                await asyncio.sleep(self.retry_interval)

        except Exception as e:
            return {lang: None for lang in target_langs}

        return translations

    async def translate_product_full_async(self, title, description, source_lang="AUTO"):
        """异步翻译产品的标题和描述"""
        title_translations = await self.translate_product_async(title, source_lang)
        await asyncio.sleep(0.5)
        desc_translations = await self.translate_product_async(description, source_lang)

        return {
            'title': title_translations,
            'description': desc_translations
        } 

if __name__ == "__main__":
    translator = Selling51Translator()