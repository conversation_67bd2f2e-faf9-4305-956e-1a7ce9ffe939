from models import db, SystemSettings, beijing_now
from translate.translator_factory import TranslatorFactory
from .services import TaskService
import logging
import requests
import aiohttp
import asyncio

logger = logging.getLogger(__name__)

class TaskHandler:
    """任务处理器 - 处理具体任务类型的执行逻辑"""
    
    @staticmethod
    async def handle_translation(task, products):
        """
        处理翻译任务
        :param task: 任务对象
        :param products: 要处理的产品列表
        :return: dict 处理结果统计
        """
        success_count = 0
        failed_count = 0
        last_error = None
        
        try:
            # 从数据库获取翻译服务名称
            translation_service = db.session.query(SystemSettings).filter_by(key='translation_service').first()
            if translation_service:
                translation_function = translation_service.value
            else:
                raise Exception("未找到翻译服务设置")

            translator = TranslatorFactory.get_translator()

            if translation_function == "deepseekhuoshan":
                
                # 创建一个包含产品标题和描述的字典列表
                products_to_translate = [
                    {
                        'title': product.title_en,
                        'description': product.description_en
                    }
                    for product in products
                ]
                
                # 调用批量翻译函数
                translations = await translator.translate_batch_products_async(products_to_translate)

                for i, product in enumerate(products):
                    if translations[i]['title']:
                        product.title_lt = translations[i]['title'].get('LT', product.title_lt)
                        product.title_lv = translations[i]['title'].get('LV', product.title_lv)
                        product.title_et = translations[i]['title'].get('ET', product.title_et)
                        product.title_fi = translations[i]['title'].get('FI', product.title_fi)

                    if translations[i]['description']:
                        product.description_lt = translations[i]['description'].get('LT', product.description_lt)
                        product.description_lv = translations[i]['description'].get('LV', product.description_lv)
                        product.description_et = translations[i]['description'].get('ET', product.description_et)
                        product.description_fi = translations[i]['description'].get('FI', product.description_fi)

                # 提交所有更改
                db.session.commit()
                success_count += len(products)
                # logger.info(f"✓ 批量翻译成功，共翻译 {len(products)} 个产品")
            #由于组合翻译不同于火山翻译，所以要单独处理
            elif translation_function == "deepseekmtserver":                
                # 创建一个包含产品标题、描述和SKU的字典列表
                products_to_translate = [
                    {
                        'sku': product.sku,
                        'title': product.title_en,
                        'description': product.description_en
                    }
                    for product in products
                ]
                
                # 调用批量翻译函数
                translations = await translator.translate_batch_products_async(products_to_translate)
                
                # 处理翻译结果
                for translation in translations:
                    sku = translation.get('sku')
                    # logger.info(f"翻译的产品的SKU: {sku}")
                    # 找到对应的产品对象
                    product = next((p for p in products if p.sku == sku), None)
                    
                    if product and 'error' not in translation:
                        # 更新标题翻译
                        if translation.get('title'):
                            product.title_lt = translation['title'].get('LT', product.title_lt)
                            product.title_lv = translation['title'].get('LV', product.title_lv)
                            product.title_et = translation['title'].get('ET', product.title_et)
                            product.title_fi = translation['title'].get('FI', product.title_fi)
                        
                        # 更新描述翻译
                        if translation.get('description'):
                            product.description_lt = translation['description'].get('LT', product.description_lt)
                            product.description_lv = translation['description'].get('LV', product.description_lv)
                            product.description_et = translation['description'].get('ET', product.description_et)
                            product.description_fi = translation['description'].get('FI', product.description_fi)
                        
                        success_count += 1
                        logger.debug(f"产品 {sku} 翻译成功")
                    else:
                        failed_count += 1
                        error_msg = translation.get('error', '未知错误')
                        # last_error = f"翻译产品 {sku} 失败: {error_msg}"
                        # logger.error(f"✗ {last_error}")
                
                # 提交所有更改
                try:
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    error_msg = f"保存翻译结果失败: {str(e)}"
                    logger.error(error_msg)
                    last_error = error_msg
                    failed_count = len(products)
                    success_count = 0
            else:
                # 现有的逐个翻译逻辑
                # logger.info(f"开始翻译以下产品:")
                for product in products:
                    # logger.info(f"- SKU: {product.sku}")
                    try:
                        # 翻译产品标题和描述
                        translations = await translator.translate_product_full_async(
                            product.title_en,
                            product.description_en,
                            "EN"
                        )
                        
                        # 更新产品翻译
                        if translations['title']:
                            product.title_lt = translations['title'].get('LT', product.title_lt)
                            product.title_lv = translations['title'].get('LV', product.title_lv)
                            product.title_et = translations['title'].get('ET', product.title_et)
                            product.title_fi = translations['title'].get('FI', product.title_fi)

                        if translations['description']:
                            product.description_lt = translations['description'].get('LT', product.description_lt)
                            product.description_lv = translations['description'].get('LV', product.description_lv)
                            product.description_et = translations['description'].get('ET', product.description_et)
                            product.description_fi = translations['description'].get('FI', product.description_fi)
                        
                        # 每个产品翻译完成后立即提交
                        db.session.commit()
                        success_count += 1
                        # logger.info(f"✓ SKU: {product.sku} 翻译成功")
                        
                    except Exception as e:
                        # 翻译失败时回滚当前产品的更改
                        db.session.rollback()
                        failed_count += 1
                        last_error = f"翻译产品 {product.sku} 失败: {str(e)}"
                        logger.error(f"✗ {last_error}")
                        continue
                
            # 更新任务进度
            TaskService.update_progress(task, success_count, failed_count, last_error)
            
        except Exception as e:
            # 发生严重错误时回滚
            db.session.rollback()
            error_msg = f"翻译任务执行失败: {str(e)}"
            logger.error(error_msg)
            TaskService.update_progress(task, success_count, failed_count, error_msg)
            
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'last_error': last_error
        }

    @staticmethod
    async def handle_publish(task, products):
        """
        处理发布任务 - 异步并发方式
        :param task: 任务对象
        :param products: 要处理的产品列表
        :return: dict 处理结果统计
        """
        success_count = 0
        failed_count = 0
        last_error = None
        
        try:
            async def publish_single_product(session, product):
                """
                异步发布单个产品
                :param session: aiohttp会话
                :param product: 产品对象
                :return: (bool, str) 发布结果和错误信息
                """
                try:
                    # 检查产品是否拥有四种语言的标题和描述
                    if not (product.title_lt and product.title_lv and product.title_et and product.title_fi):
                        return False, f"产品 {product.sku} 缺少必要的标题信息"
                    if not (product.description_lt and product.description_lv and product.description_et and product.description_fi):
                        return False, f"产品 {product.sku} 缺少必要的描述信息"

                    # 调用发布API
                    # logger.info(f"开始发布产品: {product.sku}")
                    async with session.post('http://127.0.0.1:5000/api/publish/products', json={'products': [product.to_dict()]}) as response:
                        status = response.status
                        if status == 200:
                            product.status = 'upload_successful'
                            product.publish_time = beijing_now()
                            return True, None
                        else:
                            product.status = 'upload_failed'
                            product.publish_time = beijing_now()
                            error_message = ""
                            if status == 403:
                                error_message = "网络连接问题或授权失败"
                            elif status == 404:
                                error_message = "API端点未找到"
                            elif status == 500:
                                error_message = "服务器内部错误"
                            else:
                                try:
                                    error_message = await response.text()
                                except:
                                    error_message = f"未知错误，状态码: {status}"
                            return False, f"发布产品 {product.sku} 失败: {error_message}"
                            
                except Exception as e:
                    product.status = 'upload_failed'
                    product.publish_time = beijing_now()
                    return False, f"发布产品 {product.sku} 失败: {str(e)}"

            # 创建异步HTTP会话
            async with aiohttp.ClientSession() as session:
                # 并发执行所有产品的发布
                tasks = [publish_single_product(session, product) for product in products]
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理结果
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        # 处理异步执行过程中的异常
                        failed_count += 1
                        error = f"发布产品 {products[i].sku} 失败: {str(result)}"
                        last_error = error
                        # logger.error(f"✗ {error}")
                        products[i].status = 'upload_failed'
                        products[i].publish_time = beijing_now()
                    else:
                        success, error = result
                        if success:
                            success_count += 1
                            # logger.info(f"✓ 产品 {products[i].sku} 发布成功 (当前批次成功数: {success_count})")
                        else:
                            failed_count += 1
                            last_error = error
                            logger.error(f"✗ {error}")

            # 提交所有产品状态的更改
            db.session.commit()
            # logger.info(f"本批次处理完成 - 成功: {success_count}, 失败: {failed_count}")
            
        except Exception as e:
            error_msg = f"发布任务执行失败: {str(e)}"
            logger.error(error_msg)
            db.session.rollback()  # 发生错误时回滚更改
            
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'last_error': last_error
        } 