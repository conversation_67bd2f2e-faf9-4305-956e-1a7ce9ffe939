"""add publish_time to products

Revision ID: ac9057a81ea7
Revises: 
Create Date: 2025-03-20 17:15:40.710964

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ac9057a81ea7'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sellers')
    with op.batch_alter_table('stores', schema=None) as batch_op:
        batch_op.alter_column('seller_id',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
        batch_op.create_unique_constraint(None, ['seller_id'])

    with op.batch_alter_table('unified_list_products', schema=None) as batch_op:
        batch_op.add_column(sa.Column('publish_time', sa.DateTime(), nullable=True))

    with op.batch_alter_table('upload_task_details', schema=None) as batch_op:
        batch_op.drop_column('final_status')
        batch_op.drop_column('final_message')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('upload_task_details', schema=None) as batch_op:
        batch_op.add_column(sa.Column('final_message', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('final_status', sa.VARCHAR(length=20), server_default=sa.text('("pending")'), nullable=True))

    with op.batch_alter_table('unified_list_products', schema=None) as batch_op:
        batch_op.drop_column('publish_time')

    with op.batch_alter_table('stores', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('seller_id',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)

    op.create_table('sellers',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('title', sa.VARCHAR(), nullable=True),
    sa.Column('native_country', sa.VARCHAR(), nullable=True),
    sa.Column('address', sa.VARCHAR(), nullable=True),
    sa.Column('city', sa.VARCHAR(), nullable=True),
    sa.Column('email', sa.VARCHAR(), nullable=True),
    sa.Column('status', sa.VARCHAR(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
