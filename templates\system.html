{% extends "base.html" %}

{% block title %}PHH商品在线管理 - 系统设置{% endblock %}

{% block styles %}
<style>
    .dashboard-card {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 2rem);
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }

    .fixed-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1.5rem;
        background: white;
        height: 80px;
        display: flex;
        align-items: center;
    }

    .scrollable-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
        padding: 1.5rem;
    }

    /* 开关按钮样式 */
    .apple-switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
    }

    .apple-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .apple-switch-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e5e7eb;
        transition: .4s;
        border-radius: 24px;
    }

    .apple-switch-slider:before {
        position: absolute;
        content: "";
        height: 20px;
        width: 20px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .apple-switch-slider {
        background-color: #0071e3;
    }

    input:checked + .apple-switch-slider:before {
        transform: translateX(20px);
    }

    /* 设置卡片样式 */
    .settings-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    .settings-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .settings-card-header i {
        margin-right: 0.5rem;
    }

    /* 输入框样式 */
    .form-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: all 0.3s;
    }

    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* 移除输入框的上下箭头 */
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }


</style>
{% endblock %}

{% block content %}
<!-- 系统设置卡片 -->
<div class="dashboard-card">
    <!-- 固定顶部区域 -->
    <div class="fixed-header">
        <div class="flex justify-between items-center w-full px-4">
            <h2 class="text-xl font-semibold text-gray-800">系统设置</h2>
            <button onclick="saveSettings()" 
                    class="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>保存设置
            </button>
        </div>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="scrollable-content">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 代理设置卡片 -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <i class="fas fa-network-wired text-blue-500"></i>
                    <h3 class="text-base font-medium">代理设置</h3>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <label class="text-sm text-gray-600">启用代理</label>
                        <label class="apple-switch">
                            <input type="checkbox" id="proxy_enabled" 
                                   {% if settings.proxy_enabled == 'true' %}checked{% endif %}>
                            <span class="apple-switch-slider"></span>
                        </label>
                    </div>
                    <div>
                        <label class="block text-sm text-gray-600 mb-1">代理服务器地址</label>
                        <input type="text" id="proxy_host" class="form-input" 
                               value="{{ settings.proxy_host }}">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-600 mb-1">代理服务器端口</label>
                        <input type="text" id="proxy_port" class="form-input" 
                               value="{{ settings.proxy_port }}">
                    </div>
                </div>
            </div>

            <!-- 任务设置卡片 -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <i class="fas fa-tasks text-green-500"></i>
                    <h3 class="text-base font-medium">任务设置</h3>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-600 mb-1">任务过期天数</label>
                        <input type="number" id="task_expire_days" class="form-input" 
                               value="{{ settings.task_expire_days }}">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-600 mb-1">每个任务最大产品数</label>
                        <input type="number" id="max_products_per_task" class="form-input" 
                               value="{{ settings.max_products_per_task }}">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-600 mb-1">自动检查间隔(秒)</label>
                        <input type="number" id="auto_check_interval" class="form-input" 
                               value="{{ settings.auto_check_interval }}">
                    </div>
                </div>
            </div>

            <!-- 翻译API设置卡片 -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <i class="fas fa-language text-purple-500"></i>
                    <h3 class="text-base font-medium">翻译API设置</h3>
                </div>
                <div class="space-y-4">
                    <!-- 翻译开关 -->
                    <div class="flex items-center justify-between">
                        <label class="text-sm text-gray-600">启用翻译</label>
                        <label class="apple-switch">
                            <input type="checkbox" id="translation_enabled" name="translation_enabled">
                            <span class="apple-switch-slider"></span>
                        </label>
                    </div>

                    <!-- 翻译服务选择 -->
                    <div>
                        <label class="block text-sm text-gray-600 mb-1">翻译服务</label>
                        <select id="translation_service" name="translation_service" class="form-input">
                            <option value="deepllinux">Deepl Linux</option>
                            <option value="deeplpro">DeepL Pro</option>
                            <option value="deeplvercel">Deepl Vercel</option>
                            <option value="microsoft">Microsoft Translator</option>
                            <option value="deepseek">DeepSeek Translator</option>
                            <option value="51Selling">51Selling</option>
                            <option value="deepseekhuoshan">DeepSeek Huoshan </option>
                            <option value="mtranserver">Mtran Server</option>
                            <option value="deepseekmtserver">组合翻译</option>
                        </select>
                    </div>

                    <!-- API Key -->
                    <div>
                        <label class="block text-sm text-gray-600 mb-1" id="translation_api_key_label">API Key</label>
                        <input type="text" id="translation_api_key" name="translation_api_key" 
                               class="form-input" placeholder="请输入API密钥">
                    </div>

                    <!-- DeepSeek Huoshan API Key (用于组合翻译) -->
                    <div id="deepseek_api_key_container" style="display: none;">
                        <label class="block text-sm text-gray-600 mb-1">DeepSeekhuoshan API秘钥</label>
                        <input type="text" id="deepseek_api_key" name="deepseek_api_key" 
                               class="form-input" placeholder="请输入DeepSeek Huoshan API Key">
                    </div>

                    <!-- Model 选择 (仅用于 DeepSeek Huoshan或组合翻译) -->
                    <div id="translation_model_container" style="display: none;">
                        <label class="block text-sm text-gray-600 mb-1">DeepSeekhuoshan模型选择</label>
                        <input type="text" id="translation_model" name="translation_model" 
                               class="form-input" placeholder="请输入模型ID">
                    </div>
                    

                    <!-- 重试设置 -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm text-gray-600 mb-1">请求重试次数</label>
                            <input type="number" id="translation_retry_count" name="translation_retry_count" 
                                   class="form-input" min="1" max="10" value="3">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-600 mb-1">重试间隔(秒)</label>
                            <input type="number" id="translation_retry_interval" name="translation_retry_interval" 
                                   class="form-input" min="1" max="30" value="5">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Token统计卡片 -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <i class="fas fa-chart-line text-indigo-500"></i>
                    <div class="flex items-center justify-between w-full">
                        <h3 class="text-base font-medium">Token 使用统计</h3>
                        <select id="api_provider_select" class="form-input py-1 px-2 text-sm" onchange="loadTokenUsage(this.value)">

                            <option value="deepseek_huoshan">DeepSeek火山引擎</option>

                        </select>
                    </div>
                </div>
                <div class="space-y-4">
                    <!-- 费率设置 -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm text-gray-600 mb-1">推理输入费率(元/千tokens)</label>
                            <input type="number" id="token_input_rate" name="token_input_rate" 
                                   class="form-input" step="0.0001" min="0">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-600 mb-1">推理输出费率(元/千tokens)</label>
                            <input type="number" id="token_output_rate" name="token_output_rate" 
                                   class="form-input" step="0.0001" min="0">
                        </div>
                    </div>

                    <!-- 统计数据 -->
                    <div class="space-y-4 mt-4">
                        <!-- 今日统计 -->
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-blue-700 mb-2">今日统计</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-600">输入tokens: <span id="today-input-tokens">0</span></p>
                                    <p class="text-gray-600">输出tokens: <span id="today-output-tokens">0</span></p>
                                    <p class="text-gray-600">总tokens: <span id="today-total-tokens">0</span></p>
                                </div>
                                <div>
                                    <p class="text-gray-600">输入费用: ¥<span id="today-input-cost">0.00</span></p>
                                    <p class="text-gray-600">输出费用: ¥<span id="today-output-cost">0.00</span></p>
                                    <p class="font-medium text-blue-700">总费用: ¥<span id="today-total-cost">0.00</span></p>
                                </div>
                            </div>
                        </div>

                        <!-- 昨日统计 -->
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-green-700 mb-2">昨日统计</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-600">输入tokens: <span id="yesterday-input-tokens">0</span></p>
                                    <p class="text-gray-600">输出tokens: <span id="yesterday-output-tokens">0</span></p>
                                    <p class="text-gray-600">总tokens: <span id="yesterday-total-tokens">0</span></p>
                                </div>
                                <div>
                                    <p class="text-gray-600">输入费用: ¥<span id="yesterday-input-cost">0.00</span></p>
                                    <p class="text-gray-600">输出费用: ¥<span id="yesterday-output-cost">0.00</span></p>
                                    <p class="font-medium text-green-700">总费用: ¥<span id="yesterday-total-cost">0.00</span></p>
                                </div>
                            </div>
                        </div>

                        <!-- 总计统计 -->
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-purple-700 mb-2">总计统计</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-600">输入tokens: <span id="total-input-tokens">0</span></p>
                                    <p class="text-gray-600">输出tokens: <span id="total-output-tokens">0</span></p>
                                    <p class="text-gray-600">总tokens: <span id="total-total-tokens">0</span></p>
                                </div>
                                <div>
                                    <p class="text-gray-600">输入费用: ¥<span id="total-input-cost">0.00</span></p>
                                    <p class="text-gray-600">输出费用: ¥<span id="total-output-cost">0.00</span></p>
                                    <p class="font-medium text-purple-700">总费用: ¥<span id="total-total-cost">0.00</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 火山引擎多模型配置卡片 -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <i class="fas fa-layer-group text-orange-500"></i>
                    <div class="flex items-center justify-between w-full">
                        <div class="flex items-center gap-2">
                            <h3 class="text-base font-medium">多模型配置</h3>
                            <button onclick="addModelConfig()" class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors duration-200 flex items-center">
                                <i class="fas fa-plus text-xs"></i>
                            </button>
                        </div>
                        <label class="apple-switch">
                            <input type="checkbox" id="multi_model_enabled" onchange="toggleMultiModelConfig()">
                            <span class="apple-switch-slider"></span>
                        </label>
                    </div>
                </div>
                <div class="space-y-3">
                    <!-- 使用统计 -->
                    <div>
                        <h4 class="text-sm font-medium mb-1.5">模型使用情况</h4>
                        <div id="model_usage_stats" class="space-y-1.5">
                            <!-- 动态显示每个模型的使用统计 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模型配置弹出框 -->
            <div id="modelConfigModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-lg w-96">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-center" id="modalTitle">添加模型</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">模型别名</label>
                                <input type="text" id="model_name" class="form-input" placeholder="请输入模型别名">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">模型ID</label>
                                <input type="text" id="model_id" class="form-input" placeholder="请输入模型ID">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">API密钥</label>
                                <input type="text" id="model_api_key" class="form-input" placeholder="请输入API密钥">
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-center gap-4 p-4 border-t border-gray-200">
                        <button onclick="resetModelTokens()" class="px-6 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                            重置Tokens
                        </button>
                        <button onclick="closeModelModal()" class="px-6 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                            取消
                        </button>
                        <button onclick="saveModelConfig()" class="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            确认
                        </button>
                    </div>
                </div>
            </div>

            <!-- 删除确认弹窗 -->
            <div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-lg w-80">
                    <div class="p-6 text-center">
                        <h3 class="text-base mb-4">确定要删除此模型配置吗？</h3>
                        <div class="flex justify-center gap-3">
                            <button onclick="closeDeleteConfirmModal()" class="px-6 py-1.5 bg-gray-100 text-gray-600 rounded hover:bg-gray-200 text-sm">
                                取消
                            </button>
                            <button onclick="confirmDelete()" class="px-6 py-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                                确认
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- JavaScript 保持不变 -->
<script>
// 加载设置
async function loadSettings() {
    try {
        const response = await fetch('/api/settings/translation');
        const data = await response.json();
        
        // 设置表单值
        document.getElementById('translation_enabled').checked = data.translation_enabled === true;
        document.getElementById('translation_service').value = data.translation_service;
        document.getElementById('translation_api_key').value = data.translation_api_key || '';
        document.getElementById('translation_retry_count').value = data.translation_retry_count;
        document.getElementById('translation_retry_interval').value = data.translation_retry_interval;
        document.getElementById('translation_model').value = data.translation_model || '';
        
        // 为组合翻译设置的额外字段
        document.getElementById('deepseek_api_key').value = data.zh_deepseek_api_key || '';
        // 如果是组合模式，则使用专用字段，否则使用普通字段
        if (data.translation_service === 'deepseekmtserver') {
            document.getElementById('translation_api_key').value = data.zh_mtran_api_key || '';
            document.getElementById('translation_model').value = data.zh_translation_model || '';
        }
        
        // 根据选择的服务控制输入框显示
        toggleApiKeyInput(data.translation_service);
    } catch (error) {
        console.error('加载设置失败:', error);
        notification.error('加载设置失败');
    }
}

// 保存设置
async function saveSettings() {
    try {
        const translationService = document.getElementById('translation_service').value;
        const isComboMode = translationService === 'deepseekmtserver';
        
        const settings = {
            proxy_enabled: document.getElementById('proxy_enabled').checked.toString(),
            proxy_host: document.getElementById('proxy_host').value,
            proxy_port: document.getElementById('proxy_port').value,
            task_expire_days: document.getElementById('task_expire_days').value,
            max_products_per_task: document.getElementById('max_products_per_task').value,
            auto_check_interval: document.getElementById('auto_check_interval').value,
            translation_enabled: document.getElementById('translation_enabled').checked,
            translation_service: translationService,
            translation_retry_count: parseInt(document.getElementById('translation_retry_count').value),
            translation_retry_interval: parseInt(document.getElementById('translation_retry_interval').value),
            multi_model_config: {
                enabled: document.getElementById('multi_model_enabled').checked,
                models: modelConfigs
            }
        };
        
        // 根据不同的翻译模式设置不同的字段
        if (isComboMode) {
            // 组合翻译模式 - 使用zh_前缀的专用字段
            settings.zh_mtran_api_key = document.getElementById('translation_api_key').value;
            settings.zh_deepseek_api_key = document.getElementById('deepseek_api_key').value;
            settings.zh_translation_model = document.getElementById('translation_model').value;
            // 保持原字段为空或保持原值
            settings.translation_api_key = '';
            settings.translation_model = '';
        } else {
            // 正常翻译模式 - 使用常规字段
            settings.translation_api_key = document.getElementById('translation_api_key').value;
            settings.translation_model = document.getElementById('translation_model').value;
            // 可以清空组合翻译字段
            settings.zh_mtran_api_key = '';
            settings.zh_deepseek_api_key = '';
            settings.zh_translation_model = '';
        }

        console.log('Saving settings:', settings); // 添加调试日志

        const response = await fetch('/api/settings/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });

        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }
        
        notification.success('设置已保存');
        await updateModelStats();
    } catch (error) {
        console.error('保存设置失败:', error);
        notification.error(error.message || '保存设置失败');
    }
}

// 控制 API Key 输入框显示和标签
function toggleApiKeyInput(service) {
    const apiKeyInput = document.getElementById('translation_api_key');
    const apiKeyContainer = apiKeyInput.closest('div');
    const apiKeyLabel = document.getElementById('translation_api_key_label');
    const translationEnabled = document.getElementById('translation_enabled').checked;
    const serviceSelect = document.getElementById('translation_service');
    const modelContainer = document.getElementById('translation_model_container');
    const deepseekApiKeyContainer = document.getElementById('deepseek_api_key_container');
    
    // 根据启用状态控制所有翻译相关输入
    apiKeyInput.disabled = !translationEnabled;
    serviceSelect.disabled = !translationEnabled;
    document.getElementById('translation_retry_count').disabled = !translationEnabled;
    document.getElementById('translation_retry_interval').disabled = !translationEnabled;
    
    // 显示 API Key 输入框
    apiKeyContainer.style.display = 'block';
    
    // 默认隐藏额外输入框
    deepseekApiKeyContainer.style.display = 'none';
    modelContainer.style.display = 'none';
    
    // 控制额外输入框的显示
    if (service === 'deepseekhuoshan') {
        modelContainer.style.display = 'block';
        document.getElementById('translation_model').disabled = !translationEnabled;
    } else if (service === 'deepseekmtserver') {
        // 组合翻译：同时显示Mtran API和DeepSeek API以及模型ID
        deepseekApiKeyContainer.style.display = 'block';
        modelContainer.style.display = 'block';
        document.getElementById('deepseek_api_key').disabled = !translationEnabled;
        document.getElementById('translation_model').disabled = !translationEnabled;
    }
    
    // 根据不同服务设置不同的 placeholder 和标签
    switch(service) {
        case 'deepllinux':
            apiKeyLabel.textContent = 'Deepl Linux代理';
            apiKeyInput.placeholder = translationEnabled ? '请输入DeepL Linux代理' : '翻译功能已禁用';
            break;
        case 'deeplpro':
            apiKeyLabel.textContent = 'DeepL Pro API Key';
            apiKeyInput.placeholder = translationEnabled ? '请输入DeepL Pro API Key' : '翻译功能已禁用';
            break;
        case 'deeplvercel':
            apiKeyLabel.textContent = 'Deepl Vercel代理';
            apiKeyInput.placeholder = translationEnabled ? '请输入DeepL Vercel代理' : '翻译功能已禁用';
            break;
        case 'microsoft':
            apiKeyLabel.textContent = 'Microsoft API Key';
            apiKeyInput.placeholder = translationEnabled ? '请输入Microsoft Translator API Key' : '翻译功能已禁用';
            break;
        case 'deepseek':
            apiKeyLabel.textContent = 'DeepSeek API Key';
            apiKeyInput.placeholder = translationEnabled ? '请输入DeepSeek API Key' : '翻译功能已禁用';
            break;
        case 'deepseekhuoshan':
            apiKeyLabel.textContent = 'DeepSeek Huoshan API Key';
            apiKeyInput.placeholder = translationEnabled ? '请输入DeepSeek Huoshan API Key' : '翻译功能已禁用';
            if (translationEnabled) {
                document.getElementById('translation_model').placeholder = '请输入模型ID';
            }
            break;
        case '51Selling':
            apiKeyLabel.textContent = '51Selling Cookie';
            apiKeyInput.placeholder = translationEnabled ? '请输入51Selling Cookie' : '翻译功能已禁用';
            break;
        case 'mtranserver':
            apiKeyLabel.textContent = 'Mtran Server API地址';
            apiKeyInput.placeholder = translationEnabled ? '请输入Mtran Server API地址' : '翻译功能已禁用';
            break;
        case 'deepseekmtserver':
            apiKeyLabel.textContent = 'Mtran Server API地址';
            apiKeyInput.placeholder = translationEnabled ? '请输入Mtran Server API地址' : '翻译功能已禁用';
            document.getElementById('deepseek_api_key').placeholder = translationEnabled ? '请输入DeepSeek Huoshan API Key' : '翻译功能已禁用';
            document.getElementById('translation_model').placeholder = translationEnabled ? '请输入模型ID' : '翻译功能已禁用';
            break;
    }
}

// 修改 loadTokenUsage 函数
async function loadTokenUsage(apiProvider = 'deepseek_huoshan') {
    try {
        const response = await fetch(`/api/settings/token-usage?provider=${apiProvider}`);
        const data = await response.json();
        
        // 更新费率输入框
        document.getElementById('token_input_rate').value = data.rates.input;
        document.getElementById('token_output_rate').value = data.rates.output;
        
        // 更新统计数据
        ['today', 'yesterday', 'total'].forEach(period => {
            const stats = data[period];
            document.getElementById(`${period}-input-tokens`).textContent = stats.tokens.request_tokens.toLocaleString();
            document.getElementById(`${period}-output-tokens`).textContent = stats.tokens.response_tokens.toLocaleString();
            document.getElementById(`${period}-total-tokens`).textContent = stats.tokens.total_tokens.toLocaleString();
            document.getElementById(`${period}-input-cost`).textContent = stats.cost.input.toFixed(4);
            document.getElementById(`${period}-output-cost`).textContent = stats.cost.output.toFixed(4);
            document.getElementById(`${period}-total-cost`).textContent = stats.cost.total.toFixed(4);
        });
    } catch (error) {
        console.error('加载token使用统计失败:', error);
        notification.error('加载token使用统计失败');
    }
}

// 多模型配置相关函数
let modelConfigs = [];

async function loadMultiModelConfig() {
    try {
        const response = await fetch('/api/settings/multi-model');
        const data = await response.json();
        modelConfigs = data.models || [];
        document.getElementById('multi_model_enabled').checked = data.enabled;
        updateModelStats();
    } catch (error) {
        console.error('加载多模型配置失败:', error);
        notification.error('加载多模型配置失败');
    }
}

function renderModelList() {
    const modelList = document.getElementById('model_list');
    modelList.innerHTML = '';
    
    modelConfigs.forEach((config, index) => {
        const modelDiv = document.createElement('div');
        modelDiv.className = 'p-2 border border-gray-200 rounded-lg bg-gray-50';
        modelDiv.innerHTML = `
            <div class="flex justify-between items-center mb-1.5">
                <h5 class="text-sm font-medium text-gray-700">模型 ${index + 1}</h5>
                <button onclick="removeModelConfig(${index})" class="text-red-500 hover:text-red-700 p-1">
                    <i class="fas fa-trash-alt text-sm"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-1.5">
                <div class="relative">
                    <input type="text" 
                           placeholder="模型ID" 
                           value="${config.model_id || ''}"
                           onchange="updateModelConfig(${index}, 'model_id', this.value)"
                           class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                </div>
                <div class="relative">
                    <input type="text" 
                           placeholder="API密钥" 
                           value="${config.api_key || ''}"
                           onchange="updateModelConfig(${index}, 'api_key', this.value)"
                           class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                </div>
            </div>
        `;
        modelList.appendChild(modelDiv);
    });
}

async function updateModelStats() {
    try {
        const response = await fetch('/api/settings/multi-model/stats');
        const stats = await response.json();
        const statsDiv = document.getElementById('model_usage_stats');
        statsDiv.innerHTML = '';
        
        stats.forEach((stat, index) => {
            const progress = (stat.used_tokens / stat.token_limit) * 100;
            const statusColor = stat.is_active ? 'text-green-500' : 'text-red-500';
            const statusText = stat.is_active ? '可用' : '已达到限制';
            const displayName = modelConfigs[index]?.model_name || stat.model_id;
            
            statsDiv.innerHTML += `
                <div class="p-2 bg-white border border-gray-100 rounded-lg">
                    <div class="flex justify-between items-center mb-1">
                        <div class="flex items-center gap-2">
                            <span class="text-xs font-medium text-gray-700">${displayName}</span>
                            <span class="text-xs text-gray-500">${stat.model_id}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="text-xs ${statusColor} font-medium">${statusText}</span>
                            <button onclick="openModelModal(${index})" class="text-blue-500 hover:text-blue-700">
                                <i class="fas fa-edit text-xs"></i>
                            </button>
                            <button onclick="removeModelConfig(${index})" class="text-red-500 hover:text-red-700">
                                <i class="fas fa-trash-alt text-xs"></i>
                            </button>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1.5 mb-1">
                        <div class="bg-blue-500 h-1.5 rounded-full transition-all duration-300" style="width: ${progress}%"></div>
                    </div>
                    <div class="text-xs text-gray-500">
                        已使用: ${stat.used_tokens.toLocaleString()} / ${stat.token_limit.toLocaleString()} tokens
                    </div>
                </div>
            `;
        });
    } catch (error) {
        console.error('加载模型统计失败:', error);
    }
}

let currentEditIndex = -1;

function openModelModal(index = -1) {
    currentEditIndex = index;
    const modal = document.getElementById('modelConfigModal');
    const modalTitle = document.getElementById('modalTitle');
    
    if (index >= 0) {
        modalTitle.textContent = '编辑模型配置';
        document.getElementById('model_name').value = modelConfigs[index].model_name || '';
        document.getElementById('model_id').value = modelConfigs[index].model_id || '';
        document.getElementById('model_api_key').value = modelConfigs[index].api_key || '';
    } else {
        modalTitle.textContent = '添加模型配置';
        document.getElementById('model_name').value = '';
        document.getElementById('model_id').value = '';
        document.getElementById('model_api_key').value = '';
    }
    
    modal.classList.remove('hidden');
}

function closeModelModal() {
    document.getElementById('modelConfigModal').classList.add('hidden');
    currentEditIndex = -1;
}

function addModelConfig() {
    openModelModal();
}

function saveModelConfig() {
    const modelName = document.getElementById('model_name').value;
    const modelId = document.getElementById('model_id').value;
    const apiKey = document.getElementById('model_api_key').value;
    
    if (!modelId || !apiKey) {
        notification.error('请填写必要信息');
        return;
    }
    
    const config = {
        model_name: modelName,
        model_id: modelId,
        api_key: apiKey,
        token_limit: 5000000,
        used_tokens: 0,
        is_active: true
    };
    
    if (currentEditIndex >= 0) {
        modelConfigs[currentEditIndex] = { ...modelConfigs[currentEditIndex], ...config };
    } else {
        modelConfigs.push(config);
    }
    
    closeModelModal();
    saveSettings(); // 立即保存到服务器
}

let deleteModelIndex = -1;

function openDeleteConfirmModal(index) {
    deleteModelIndex = index;
    document.getElementById('deleteConfirmModal').classList.remove('hidden');
}

function closeDeleteConfirmModal() {
    document.getElementById('deleteConfirmModal').classList.add('hidden');
    deleteModelIndex = -1;
}

async function confirmDelete() {
    if (deleteModelIndex >= 0) {
        try {
            const modelId = modelConfigs[deleteModelIndex].id;
            const response = await fetch(`/api/settings/multi-model/delete/${modelId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }
            
            // 从本地配置中移除
            modelConfigs.splice(deleteModelIndex, 1);
            // 更新显示
            notification.success('模型已删除');
            updateModelStats();
            closeDeleteConfirmModal();
        } catch (error) {
            console.error('删除模型失败:', error);
            notification.error(error.message || '删除模型失败');
        }
    }
}

function removeModelConfig(index) {
    openDeleteConfirmModal(index);
}

async function toggleMultiModelConfig() {
    const enabled = document.getElementById('multi_model_enabled').checked;
    try {
        await fetch('/api/settings/multi-model/toggle', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enabled })
        });
        notification.success(enabled ? '已启用多模型配置' : '已禁用多模型配置');
    } catch (error) {
        console.error('切换多模型配置失败:', error);
        notification.error('切换多模型配置失败');
    }
}

// 在script部分添加重置函数
async function resetModelTokens() {
    if (currentEditIndex < 0) {
        notification.warning('请先选择要重置的模型');
        return;
    }

    const model = modelConfigs[currentEditIndex];
    if (!model || !model.id) {
        notification.warning('无法获取模型信息');
        return;
    }

    try {
        const response = await fetch(`/api/settings/multi-model/reset-tokens/${model.id}`, {
            method: 'POST'
        });

        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }

        notification.success('Token使用量已重置');
        await updateModelStats(); // 刷新显示
    } catch (error) {
        console.error('重置token失败:', error);
        notification.error(error.message || '重置失败，请重试');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadTokenUsage('deepseek_huoshan');
    loadMultiModelConfig();  // 加载多模型配置
    
    // 监听翻译服务变化
    document.getElementById('translation_service').addEventListener('change', function() {
        toggleApiKeyInput(this.value);
    });
    
    // 监听翻译启用状态变化
    document.getElementById('translation_enabled').addEventListener('change', function() {
        toggleApiKeyInput(document.getElementById('translation_service').value);
    });
    
    // 定期更新模型统计信息
    setInterval(updateModelStats, 60000);  // 每分钟更新一次
});
</script>
{% endblock %} 