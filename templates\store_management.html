{% extends "base.html" %}

{% block title %}PHH商品在线管理 - 店铺管理{% endblock %}

{% block content %}
<!-- 店铺管理卡片 -->
<div class="dashboard-card">
    <!-- 固定顶部区域 -->
    <div class="fixed-header">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">店铺管理</h2>
            <button onclick="openAddStoreModal()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>添加店铺
            </button>
        </div>
    </div>

    <!-- 店铺列表 -->
    <div class="scrollable-content">
        <table class="min-w-full bg-white">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        店铺名称
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Seller ID
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        账号
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        更新时间
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="stores-table-body">
                <!-- 店铺数据将通过JavaScript动态填充 -->
            </tbody>
        </table>
    </div>

    <!-- 固定底部分页 -->
    <div class="fixed-footer">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4 text-xs text-gray-700">
                <span id="pagination-info">当前: 0 - 0 行, 共 0 行</span>
                <div class="flex items-center space-x-2">
                    <span>每页</span>
                    <select class="border border-gray-300 rounded px-2 py-1 text-xs h-6" onchange="handlePageSizeChange(this.value)">
                        <option value="30">30</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                    </select>
                    <span>行</span>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToFirstPage()" disabled>
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToPrevPage()" disabled>
                    <i class="fas fa-angle-left"></i>
                </button>
                <span class="px-2 text-xs" id="current-page-info">第 0/0 页</span>
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToNextPage()" disabled>
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToLastPage()" disabled>
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑店铺的模态框 -->
<div id="store-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modal-title">添加店铺</h3>
            <form id="store-form">
                <input type="hidden" id="store-id">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">店铺名称</label>
                    <input type="text" id="store-name" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                           placeholder="输入店铺别名">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Seller ID</label>
                    <input type="text" id="seller-id" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                           placeholder="输入Pigu Seller ID">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">账号</label>
                    <input type="text" id="username" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                           placeholder="输入Pigu账号">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">密码</label>
                    <input type="password" id="password" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                           placeholder="输入Pigu密码">
                </div>
                <div class="mb-4">
                    <button type="button" onclick="testConnection()" 
                            class="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-200">
                        <i class="fas fa-link mr-2"></i>测试连接
                    </button>
                    <div id="connection-status" class="mt-2 text-sm text-center hidden">
                        <!-- 状态信息将在这里显示 -->
                    </div>
                </div>
            </form>
            <div class="mt-5 flex justify-end space-x-3">
                <button onclick="closeStoreModal()" 
                        class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200">
                    取消
                </button>
                <button onclick="saveStore()" id="save-store-btn"
                        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200">
                    保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 全局变量
    let currentStoreId = null;
    let currentPage = 1;
    let pageSize = 50;
    let totalPages = 1;

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        loadStores();
    });

    // 加载店铺列表
    function loadStores() {
        fetch('/api/phh_store/stores')
            .then(response => response.json())
            .then(stores => {
                const tbody = document.getElementById('stores-table-body');
                tbody.innerHTML = stores.map(store => `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="store-name-container" data-store-id="${store.id}">
                                <span class="store-name-display cursor-pointer hover:text-blue-600" 
                                      onclick="makeNameEditable(this)">
                                    ${store.name}
                                </span>
                                <input type="text" 
                                       class="store-name-input hidden w-full px-2 py-1 text-sm border rounded-md" 
                                       value="${store.name}"
                                       onblur="updateStoreName(this)"
                                       onkeypress="handleKeyPress(event, this)">
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${store.seller_id}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${store.username}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                ${store.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${store.status}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${store.updated_at || '-'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="editStore(${JSON.stringify(store).replace(/"/g, '&quot;')})" 
                                    class="text-blue-600 hover:text-blue-900">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteStore(${store.id})" 
                                    class="text-red-600 hover:text-red-900">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');

                // 更新分页信息
                updatePaginationInfo(data);
            });
    }

    // 更新分页信息
    function updatePaginationInfo(data) {
        const startRow = (currentPage - 1) * pageSize + 1;
        const endRow = Math.min(startRow + pageSize - 1, data.total);
        const totalCount = data.total;
        
        // 更新当前页显示信息
        document.getElementById('pagination-info').textContent = 
            `当前: ${totalCount > 0 ? startRow : 0} - ${endRow} 行, 共 ${totalCount} 行`;
        
        // 更新页码信息
        document.getElementById('current-page-info').textContent = 
            `第 ${currentPage}/${Math.max(1, totalPages)} 页`;
            
        // 更新分页按钮状态
        updatePaginationButtons();
    }

    // 更新分页按钮状态
    function updatePaginationButtons() {
        const firstPageBtn = document.querySelector('.pagination-button:nth-child(1)');
        const prevPageBtn = document.querySelector('.pagination-button:nth-child(2)');
        const nextPageBtn = document.querySelector('.pagination-button:nth-child(4)');
        const lastPageBtn = document.querySelector('.pagination-button:nth-child(5)');
        
        firstPageBtn.disabled = currentPage <= 1;
        prevPageBtn.disabled = currentPage <= 1;
        nextPageBtn.disabled = currentPage >= totalPages;
        lastPageBtn.disabled = currentPage >= totalPages;
    }

    // 页面大小改变处理
    function handlePageSizeChange(newSize) {
        pageSize = parseInt(newSize);
        currentPage = 1;
        loadStores();
    }

    // 页面跳转函数
    function goToFirstPage() {
        if (currentPage > 1) {
            currentPage = 1;
            loadStores();
        }
    }

    function goToPrevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadStores();
        }
    }

    function goToNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            loadStores();
        }
    }

    function goToLastPage() {
        if (currentPage < totalPages) {
            currentPage = totalPages;
            loadStores();
        }
    }

    // 打开添加店铺模态框
    function openAddStoreModal() {
        currentStoreId = null;
        document.getElementById('modal-title').textContent = '添加店铺';
        document.getElementById('store-form').reset();
        resetConnectionStatus();
        document.getElementById('store-modal').classList.remove('hidden');
    }

    // 打开编辑店铺模态框
    function editStore(store) {
        currentStoreId = store.id;
        document.getElementById('modal-title').textContent = '编辑店铺';
        document.getElementById('store-name').value = store.name;
        document.getElementById('seller-id').value = store.seller_id;
        document.getElementById('username').value = store.username;
        document.getElementById('password').value = ''; // 出于安全考虑，不显示原密码
        resetConnectionStatus();
        document.getElementById('store-modal').classList.remove('hidden');
    }

    // 关闭模态框
    function closeStoreModal() {
        document.getElementById('store-modal').classList.add('hidden');
    }

    // 保存店铺信息
    function saveStore() {
        const data = {
            name: document.getElementById('store-name').value,
            seller_id: document.getElementById('seller-id').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value
        };

        const url = currentStoreId ? 
            `/api/phh_store/stores/${currentStoreId}` : 
            '/api/phh_store/stores';
        
        const method = currentStoreId ? 'PUT' : 'POST';

        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                notification.error(result.error);
            } else {
                closeStoreModal();
                loadStores();
                notification.success('店铺保存成功');
            }
        })
        .catch(error => {
            notification.error('保存失败：' + error.message);
        });
    }

    // 删除店铺
    async function deleteStore(storeId) {
        const confirmed = await notification.confirm('确定要删除这个店铺吗？删除后将无法恢复。', '删除确认');
        if (!confirmed) {
            return;
        }

        const loading = notification.showLoading('正在删除...');

        fetch(`/api/phh_store/stores/${storeId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(result => {
            loading.remove();
            if (result.error) {
                notification.error(result.error);
            } else {
                notification.success('店铺删除成功');
                loadStores();
            }
        })
        .catch(error => {
            loading.remove();
            notification.error('删除失败：' + error.message);
        });
    }

    // 测试连接
    function testConnection() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const statusDiv = document.getElementById('connection-status');
        const saveButton = document.getElementById('save-store-btn');

        if (!username || !password) {
            showConnectionStatus('请填写账号和密码', 'error');
            return;
        }

        // 显示加载状态
        showConnectionStatus('正在测试连接...', 'loading');

        fetch('/api/phh_store/test-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                showConnectionStatus(result.error, 'error');
            } else {
                showConnectionStatus('连接成功！', 'success');
            }
        })
        .catch(error => {
            showConnectionStatus('连接失败：' + error.message, 'error');
        });
    }

    // 显示连接状态
    function showConnectionStatus(message, type) {
        const statusDiv = document.getElementById('connection-status');
        statusDiv.classList.remove('hidden');
        
        let color, icon;
        switch (type) {
            case 'success':
                color = 'text-green-600';
                icon = 'check-circle';
                break;
            case 'error':
                color = 'text-red-600';
                icon = 'times-circle';
                break;
            case 'loading':
                color = 'text-blue-600';
                icon = 'spinner fa-spin';
                break;
        }

        statusDiv.className = `mt-2 text-sm text-center ${color}`;
        statusDiv.innerHTML = `<i class="fas fa-${icon} mr-1"></i>${message}`;
    }

    // 重置表单时重置连接状态
    function resetConnectionStatus() {
        const statusDiv = document.getElementById('connection-status');
        statusDiv.classList.add('hidden');
    }

    // 使店铺名称可编辑
    function makeNameEditable(element) {
        const container = element.parentElement;
        const display = element;
        const input = container.querySelector('.store-name-input');
        
        // 隐藏显示元素，显示输入框
        display.classList.add('hidden');
        input.classList.remove('hidden');
        input.focus();
        input.select();
    }

    // 处理按键事件
    function handleKeyPress(event, input) {
        if (event.key === 'Enter') {
            input.blur(); // 触发失焦事件，从而保存更改
        } else if (event.key === 'Escape') {
            // 添加ESC键取消编辑的功能
            input.value = input.getAttribute('data-original-value');
            input.blur();
        }
    }

    // 更新店铺名称
    function updateStoreName(input) {
        const container = input.closest('.store-name-container');
        const storeId = container.dataset.storeId;
        const newName = input.value.trim();
        const display = container.querySelector('.store-name-display');

        if (!newName) {
            input.classList.add('hidden');
            display.classList.remove('hidden');
            return;
        }

        fetch(`/api/phh_store/stores/${storeId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: newName
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                notification.error(result.error);
                input.value = display.textContent; // 恢复原值
            } else {
                display.textContent = newName;
                notification.success('店铺名称更新成功');
            }
        })
        .catch(error => {
            notification.error('更新失败：' + error.message);
            input.value = display.textContent; // 恢复原值
        })
        .finally(() => {
            // 隐藏输入框，显示文本
            input.classList.add('hidden');
            display.classList.remove('hidden');
        });
    }
</script>

<style>
    .dashboard-card {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 2rem);
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }

    .fixed-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem;
        background: white;
    }

    .scrollable-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
        margin-bottom: 48px; /* 为固定底部分页留出空间 */
    }

    /* 分页样式 */
    .fixed-footer {
        position: fixed;
        bottom: 0;
        left: 220px;
        right: 0;
        height: 48px;
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 0.5rem 1rem;
        z-index: 40;
        box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    }

    .pagination-button {
        border: 1px solid #e5e7eb;
        border-radius: 0.25rem;
        color: #374151;
        background: white;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 24px;
    }

    .pagination-button:hover:not(:disabled) {
        background: #f3f4f6;
    }

    .pagination-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 确保表格内容不会被固定底部遮挡 */
    .table-container {
        margin-bottom: 48px;
    }

    @media (max-width: 768px) {
        .fixed-footer {
            left: 0;
        }
    }

    /* 添加新样式 */
    .disabled\:opacity-50:disabled {
        opacity: 0.5;
    }
    .disabled\:cursor-not-allowed:disabled {
        cursor: not-allowed;
    }
</style>
{% endblock %} 