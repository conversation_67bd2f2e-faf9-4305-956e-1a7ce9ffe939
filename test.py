import requests
import pandas as pd
import sys
import json
import datetime
import os

# 代理设置
proxies = {
    "http": "http://127.0.0.1:10809",
    "https": "http://127.0.0.1:10809",
}

def login_and_get_token(username, password, proxies):
    """登录并获取访问令牌"""
    try:
        login_url = "https://pmpapi.pigugroup.eu/v3/login"
        data = {
            "username": username,
            "password": password
        }
        
        response = requests.post(login_url, json=data, proxies=proxies)
        response.raise_for_status()
        token = response.json().get('token')
        
        if not token:
            print("错误：未能获取到token")
            sys.exit(1)
        
        print("成功获取token")
        return token
    except requests.exceptions.RequestException as e:
        print(f"登录失败: {str(e)}")
        sys.exit(1)

def create_import_execution(seller_id, token, proxies):
    """创建产品导入执行ID"""
    url = f"https://pmpapi.pigugroup.eu/v3/sellers/{seller_id}/product/import/execution"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Pigu-mp {token}"
    }
    
    response = requests.post(url, headers=headers, proxies=proxies)
    response.raise_for_status()
    return response.json()['id']

def upload_product(execution_id, token, product_data, proxies):
    """上传单个产品数据"""
    url = f"https://pmpapi.pigugroup.eu/v3/sellers/product/import/execution/{execution_id}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Pigu-mp {token}"
    }
    
    response = requests.post(url, headers=headers, json=product_data, proxies=proxies)
    if response.status_code == 422:
        print("验证错误:", response.json())
    response.raise_for_status()
    return response.json()

def check_import_results(seller_id, execution_id, token, proxies):
    """检查产品导入结果"""
    url = f"https://pmpapi.pigugroup.eu/v3/sellers/{seller_id}/product/import/execution/{execution_id}/results"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Pigu-mp {token}"
    }
    params = {
        "limit": 100,
        "offset": 0
    }
    
    response = requests.get(url, headers=headers, params=params, proxies=proxies)
    response.raise_for_status()
    return response.json()

def save_execution_id(execution_id, seller_id, total_products=0, success_count=0, failed_count=0, upload_details=None):
    """保存执行ID到文件中"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Z--execution_ids_{seller_id}_{timestamp}.json"
    
    data = {
        "execution_id": execution_id,
        "seller_id": seller_id,
        "timestamp": timestamp,
        "total_products": total_products,
        "success_count": success_count,
        "failed_count": failed_count,
        "upload_details": upload_details or []
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    print(f"执行ID已保存到文件: {filename}")
    return filename

def upload_products(seller_id, token, proxies, products):
    """上传多个产品"""
    try:
        # 1. 创建导入执行ID
        print("创建导入执行ID...")
        execution_id = create_import_execution(seller_id, token, proxies)
        print(f"获得执行ID: {execution_id}")

        # 2. 上传产品
        print(f"\n开始上传{len(products)}个产品...")
        results = []
        success_count = 0
        failed_count = 0
        upload_details = []

        for i, product in enumerate(products, 1):
            print(f"\n正在上传第{i}个产品...")
            try:
                result = upload_product(execution_id, token, product, proxies)
                success_count += 1
                print(f"第{i}个产品上传成功，ID: {result.get('id')}")
                
                # 记录上传成功的详细信息
                upload_detail = {
                    "index": i,
                    "status": "success",
                    "sku": product.get("modifications", [{}])[0].get("sku", ""),
                    "title": product.get("title", ""),
                    "result": result
                }
                upload_details.append(upload_detail)
                results.append({
                    "index": i,
                    "status": "success",
                    "result": result
                })
            except requests.exceptions.RequestException as e:
                failed_count += 1
                print(f"第{i}个产品上传失败: {str(e)}")
                
                # 记录上传失败的详细信息
                upload_detail = {
                    "index": i,
                    "status": "failed",
                    "sku": product.get("modifications", [{}])[0].get("sku", ""),
                    "title": product.get("title", ""),
                    "error": str(e)
                }
                upload_details.append(upload_detail)
                results.append({
                    "index": i,
                    "status": "failed",
                    "error": str(e)
                })

        # 3. 检查导入结果
        print("\n检查导入结果...")
        import_results = check_import_results(seller_id, execution_id, token, proxies)
        
        # 保存执行ID和上传统计信息
        saved_file = save_execution_id(
            execution_id=execution_id,
            seller_id=seller_id,
            total_products=len(products),
            success_count=success_count,
            failed_count=failed_count,
            upload_details=upload_details
        )
        print(f"执行ID和上传统计已保存到: {saved_file}")
        
        # 4. 保存详细上传结果
        save_results = {
            "execution_id": execution_id,
            "seller_id": seller_id,
            "timestamp": datetime.datetime.now().strftime("%Y%m%d_%H%M%S"),
            "total_products": len(products),
            "success_count": success_count,
            "failed_count": failed_count,
            "upload_details": upload_details,  # 产品上传详情
            "upload_results": results,  # 产品上传结果
            "import_results": import_results  # 产品导入结果
        }
        
        results_file = f"Z--upload_results_{execution_id}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(save_results, f, ensure_ascii=False, indent=4)
        print(f"\n详细上传结果已保存到: {results_file}")
        
        return execution_id, results

    except Exception as e:
        print(f"上传过程中发生错误: {str(e)}")
        raise

def get_seller_offers(seller_id, token, proxies, status=None, amount_from=None, amount_to=None, price_from=None, price_to=None, max_pages=None):
    """获取卖家商品列表"""
    offers_url = f"https://pmpapi.pigugroup.eu/v3/sellers/{seller_id}/offers"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Pigu-mp {token}"
    }
    params = {
        "status": status,
        "amount_from": amount_from,
        "amount_to": amount_to,
        "price_from": price_from,
        "price_to": price_to,
        "limit": 20,  # 每次请求返回的产品数量
    }

    all_offers = []
    page_count = 0
    access_count = 0  # 初始化访问次数计数器

    while True:
        # 打印当前请求的URL
        print(f"\n当前请求URL: {offers_url}")
        print("当前参数:", params)
        
        response = requests.get(offers_url, headers=headers, params=params, proxies=proxies)
        response.raise_for_status()
        offers_data = response.json()
        
        # 打印meta信息
        print("\nMeta信息:")
        print(json.dumps(offers_data['meta'], indent=2, ensure_ascii=False))
        print('获取成功')
        
        if "offers" in offers_data:
            all_offers.extend(offers_data["offers"])
        
        # 检查是否有下一页
        if offers_data['meta']['next'] and (max_pages is None or page_count < max_pages):
            offers_url = offers_data['meta']['next']
            page_count += 1
        else:
            break

        access_count += 1  # 每次成功请求后增加访问次数
        print(f"当前访问次数: {access_count}")  # 打印当前访问次数

        # 如果已经获取了3页，则退出
        if access_count >= 3:
            print("\n已达到3页限制，停止获取")
            break

    df = pd.json_normalize(all_offers)
    df.to_csv("all_offers.csv", index=False)
    print("\nAll offers data saved to all_offers.csv")

def create_excel_template(output_file="product_template.xlsx"):
    """创建Excel模板文件"""
    # 定义列名
    columns = [
        'category_id',
        'title', 'title_lv', 'title_ee', 'title_fi', 'title_ru',
        'long_description', 'long_description_lv', 'long_description_ee', 
        'long_description_fi', 'long_description_ru',
        'youtube_videos',  # 使用 | 分隔多个视频链接
        'product_features_name',  # 使用 | 分隔多个特征名称
        'product_features_value',  # 使用 | 分隔多个特征值
        'image1',  # 主图
        'image2',
        'image3',
        'image4',
        'image5',
        'modification_title',
        'sku',
        'eans',  # 使用 | 分隔多个EAN
        'package_weight',
        'package_length',
        'package_height',
        'package_width'
    ]
    
    # 创建示例数据
    example_data = {
        'category_id': 15427,
        'title': 'Example Product',
        'title_lv': 'Example Product LV',
        'title_ee': 'Example Product EE',
        'title_fi': 'Example Product FI',
        'title_ru': 'Example Product RU',
        'long_description': 'Product description',
        'long_description_lv': 'Product description LV',
        'long_description_ee': 'Product description EE',
        'long_description_fi': 'Product description FI',
        'long_description_ru': 'Product description RU',
        'youtube_videos': 'https://youtube.com/video1|https://youtube.com/video2',
        'product_features_name': 'Color|Size|Material',
        'product_features_value': 'Red|Large|Wood',
        'image1': 'https://example.com/image1.jpg',
        'image2': 'https://example.com/image2.jpg',
        'image3': '',
        'image4': '',
        'image5': '',
        'modification_title': 'Red Large',
        'sku': 'SKU-001',
        'eans': '1234567890123|1234567890124',
        'package_weight': 0.5,
        'package_length': 0.2,
        'package_height': 0.2,
        'package_width': 0.2
    }
    
    # 创建DataFrame
    df = pd.DataFrame([example_data])
    
    # 创建Excel writer对象
    writer = pd.ExcelWriter(output_file, engine='xlsxwriter')
    
    # 将数据写入Excel
    df.to_excel(writer, index=False, sheet_name='Products')
    
    # 获取workbook和worksheet对象
    workbook = writer.book
    worksheet = writer.sheets['Products']
    
    # 添加列说明
    explanation_sheet = workbook.add_worksheet('Instructions')
    explanations = {
        'category_id': '产品类别ID（必填）',
        'title': '产品标题（必填）',
        'title_lv': '拉脱维亚语标题',
        'title_ee': '爱沙尼亚语标题',
        'title_fi': '芬兰语标题',
        'title_ru': '俄语标题',
        'long_description': '产品详细描述（必填）',
        'long_description_lv': '拉脱维亚语描述',
        'long_description_ee': '爱沙尼亚语描述',
        'long_description_fi': '芬兰语描述',
        'long_description_ru': '俄语描述',
        'youtube_videos': 'YouTube视频链接，多个用|分隔',
        'product_features_name': '产品特征名称，多个用|分隔',
        'product_features_value': '产品特征值，多个用|分隔',
        'image1': '产品主图URL（必填）',
        'image2': '产品图片2 URL',
        'image3': '产品图片3 URL',
        'image4': '产品图片4 URL',
        'image5': '产品图片5 URL',
        'modification_title': '产品型号标题',
        'sku': '产品SKU（必填）',
        'eans': '产品EAN码，多个用|分隔（必填）',
        'package_weight': '包装重量，单位：kg（必填）',
        'package_length': '包装长度，单位：m（必填）',
        'package_height': '包装高度，单位：m（必填）',
        'package_width': '包装宽度，单位：m（必填）'
    }
    
    # 写入说明
    explanation_sheet.write(0, 0, '字段名')
    explanation_sheet.write(0, 1, '说明')
    for i, (field, explanation) in enumerate(explanations.items(), 1):
        explanation_sheet.write(i, 0, field)
        explanation_sheet.write(i, 1, explanation)
    
    # 设置列宽
    worksheet.set_column(0, len(columns)-1, 20)
    explanation_sheet.set_column(0, 0, 25)
    explanation_sheet.set_column(1, 1, 50)
    
    # 保存文件
    writer.close()
    print(f"Excel模板已创建: {output_file}")

def read_products_from_excel(excel_file):
    """从Excel文件读取产品数据"""
    try:
        df = pd.read_excel(excel_file)
        products = []
        
        for _, row in df.iterrows():
            # 处理product_features（将字符串转换为列表）
            features = []
            if pd.notna(row['product_features_name']) and pd.notna(row['product_features_value']):
                names = str(row['product_features_name']).split('|')
                values = str(row['product_features_value']).split('|')
                features = [{"name": n.strip(), "value": v.strip()} 
                          for n, v in zip(names, values)]

            # 处理images（从5个独立的图片列中收集）
            images = []
            for i in range(1, 6):
                image_key = f'image{i}'
                if pd.notna(row[image_key]) and str(row[image_key]).strip():
                    images.append(str(row[image_key]).strip())

            # 处理youtube_videos（将字符串转换为列表）
            youtube_videos = []
            if pd.notna(row['youtube_videos']):
                youtube_videos = [video.strip() for video in str(row['youtube_videos']).split('|')]

            # 处理modifications的eans（将字符串转换为列表）
            eans = []
            if pd.notna(row['eans']):
                eans = [ean.strip() for ean in str(row['eans']).split('|')]

            # 创建产品数据结构
            product = {
                "category_id": int(row['category_id']),
                "title": str(row['title']),
                "title_lv": str(row['title_lv']),
                "title_ee": str(row['title_ee']),
                "title_fi": str(row['title_fi']),
                "title_ru": str(row['title_ru']),
                "long_description": str(row['long_description']),
                "long_description_lv": str(row['long_description_lv']),
                "long_description_ee": str(row['long_description_ee']),
                "long_description_fi": str(row['long_description_fi']),
                "long_description_ru": str(row['long_description_ru']),
                "youtube_videos": youtube_videos,
                "product_features": features,
                "images": images,
                "modifications": [
                    {
                        "title": str(row['modification_title']),
                        "sku": str(row['sku']),
                        "eans": eans,
                        "package_weight": float(row['package_weight']),
                        "package_length": float(row['package_length']),
                        "package_height": float(row['package_height']),
                        "package_width": float(row['package_width'])
                    }
                ]
            }
            products.append(product)
        
        print(f"从Excel成功读取{len(products)}个产品数据")
        return products
    except Exception as e:
        print(f"读取Excel文件时发生错误: {str(e)}")
        raise

def check_import_status_by_id():
    """根据用户输入的导入ID检查导入状态"""
    try:
        # 获取用户输入
        execution_id = input("\n请输入要检查的导入ID: ").strip()
        seller_id = "9001265"
        
        # 获取token
        token = login_and_get_token(
            username="<EMAIL>",
            password="Chen369369",
            proxies=proxies
        )
        
        # 检查导入结果
        print(f"\n正在检查导入ID {execution_id} 的状态...")
        results = check_import_results(seller_id, execution_id, token, proxies)
        
        # 保存检查结果
        results_file = f"Z--111手动检查结果_{execution_id}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=4)
        
        print(f"导入ID检查结果: {results}")
        return results
        
    except Exception as e:
        print(f"检查导入状态时发生错误: {str(e)}")
        return None

def get_categories(token, proxies, limit=100, offset=0, category_id=None, parent_id=None, get_all=False):
    """获取类目信息
    
    Args:
        token: API访问令牌
        proxies: 代理设置
        limit: 每页返回的类目数量（默认100）
        offset: 分页偏移量（默认0）
        category_id: 特定类目ID（可选）
        parent_id: 父类目ID（可选）
        get_all: 是否获取所有类目（默认False）
    
    Returns:
        dict: 包含类目信息的字典
    """
    try:
        url = "https://pmpapi.pigugroup.eu/v3/categories"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        all_categories = []
        total_count = None
        current_offset = offset
        
        while True:
            # 构建查询参数
            params = {
                "limit": limit,
                "offset": current_offset
            }
            if category_id is not None:
                params["id"] = category_id
            if parent_id is not None:
                params["parent_id"] = parent_id
                
            print(f"\n正在获取类目信息... (offset: {current_offset})")
            response = requests.get(url, headers=headers, params=params, proxies=proxies)
            response.raise_for_status()
            
            results = response.json()
            
            # 获取总数
            if total_count is None:
                total_count = results['meta']['total_count']
                print(f"总类目数: {total_count}")
            
            # 添加当前页的类目
            if 'category_list' in results:
                all_categories.extend(results['category_list'])
            
            # 如果不需要获取所有类目，或者已经获取完所有类目，就退出循环
            if not get_all or len(all_categories) >= total_count:
                break
                
            # 更新offset继续获取下一页
            current_offset += limit
        
        # 保存结果到JSON文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Z--categories_all_{timestamp}.json"
        
        save_results = {
            "meta": {
                "total_count": len(all_categories)
            },
            "category_list": all_categories
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_results, f, ensure_ascii=False, indent=4)
            
        print(f"\n已获取 {len(all_categories)} 个类目")
        print(f"类目信息已保存到文件: {filename}")
        return save_results
        
    except Exception as e:
        print(f"获取类目信息时发生错误: {str(e)}")
        return None

def check_barcode_by_ean(ean, token, proxies):
    """
    通过EAN码查询产品信息
    """
    try:
        url = f"https://pmpapi.pigugroup.eu/v3/products/product-modifications/barcodes"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        # 构建请求参数
        params = {
            "ean": ean
        }

        print(f"\n正在查询EAN码: {ean}")
        
        # 发送GET请求
        response = requests.get(url, headers=headers, params=params)

        #如果状态码是200，打印结果
        if response.status_code == 200:
            results = response.json()
            print(results)
        
        # 如果状态码是404，说明未找到该条码
        if response.status_code == 404:
            print(f"未找到EAN码 {ean} 的相关信息")
            return None
            
        # 如果状态码是429，说明请求过于频繁
        if response.status_code == 429:
            print("请求过于频繁，请稍后再试")
            return None
            
        # 对于其他错误状态码，抛出异常
        response.raise_for_status()
        return results
        
    except requests.exceptions.RequestException as e:
        print(f"查询EAN码时发生错误: {str(e)}")
        return None
    
# GET
# ​/v3​/offers​/{offerId}
# Get information about an offer
# This endpoint retrieves detailed information about a specific offer, identified by its offerId. The response includes comprehensive data on the offer’s modification details, seller information, pricing, availability, and deactivation attributes.

# Parameters
# Name	Description
# offerId *
# integer
# (path)
# offerId

def get_offer_info(offer_id, token, proxies):
    """
    获取指定offerId的商品信息
    """
    try:
        url = f"https://pmpapi.pigugroup.eu/v3/offers/{offer_id}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }   

        response = requests.get(url, headers=headers, proxies=proxies)
        response.raise_for_status()
        return response.json()
    
    except Exception as e:
        print(f"获取商品信息时发生错误: {str(e)}")
        return None


if __name__ == "__main__":
    # 1. 创建Excel模板（如果需要）
    # create_excel_template()
    
    # 2. 登录获取token
    token = login_and_get_token(
        username="<EMAIL>",
        password="Chen369369",
        proxies=proxies
    )
    
    # # 测试EAN码查询功能
    # print("\n=== 测试EAN码查询功能 ===")
    # test_ean = "6944904695341"  # 这里替换为要查询的实际EAN码
    # barcode_info = check_barcode_by_ean(test_ean, token, proxies)

    # 测试获取商品信息
    print("\n=== 测试获取商品信息 ===")
    offer_id = 205160763  # 替换为实际的offerId
    offer_info = get_offer_info(offer_id, token, proxies)
    print(offer_info)
    
    # # 3. 设置卖家ID
    # seller_id = "9001636"
    
    # # 获取所有类目
    # print("\n开始获取所有类目:")
    # all_categories = get_categories(token, proxies, limit=100, get_all=True)

    # # 4. 从Excel读取产品数据
    # excel_file = "product_template.xlsx"  # 确保这个文件存在
    # products = read_products_from_excel(excel_file)
    
    # # 5. 上传产品
    # print("\n开始上传产品...")
    # execution_id, results = upload_products(seller_id=seller_id, token=token, proxies=proxies, products=products)

    #6. 输入导入ID检查情况并打印
    # while True:
    #     check_import_status_by_id()
        
    # # 7. 测试获取类目信息
    # print("\n=== 获取所有类目信息 ===")
    # token = login_and_get_token(
    #     username="<EMAIL>",
    #     password="Chen369369",
    #     proxies=proxies
    # )
    
    # # 获取所有类目
    # print("\n开始获取所有类目:")
    # all_categories = get_categories(token, proxies, limit=100, get_all=True)
    
    # if all_categories:
    #     total_categories = len(all_categories.get('category_list', []))
    #     print(f"\n成功获取所有类目，共 {total_categories} 个类目")
    # else:
    #     print("\n获取类目失败")

    # 8. 测试获取卖家商品列表
    # print("\n=== 测试获取卖家商品列表 ===")
    # token = login_and_get_token(
    #     username="<EMAIL>",
    #     password="Chen369369",
    #     proxies=proxies
    # )
    
    # seller_id = "9001636"
    # print("\n开始获取卖家商品列表（限制3页）:")
    # get_seller_offers(seller_id, token, proxies, max_pages=3)