{% extends "base.html" %}

{% block title %}PHH商品在线管理 - 自动改价{% endblock %}

{% block styles %}
<style>
    .dashboard-card {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 2rem);
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }

    .fixed-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem;
        background: white;
    }

    .scrollable-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
    }

    /* 规则类型标签样式 */
    .rule-type {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
    }

    .rule-type-higher {
        background-color: #fef2f2;
        color: #dc2626;
    }

    .rule-type-lower {
        background-color: #eff6ff;
        color: #2563eb;
    }

    /* 开关样式 */
    .apple-switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
    }

    .apple-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .apple-switch-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e5e7eb;
        transition: .4s;
        border-radius: 24px;
    }

    .apple-switch-slider:before {
        position: absolute;
        content: "";
        height: 20px;
        width: 20px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .apple-switch-slider {
        background-color: #0071e3;
    }

    input:checked + .apple-switch-slider:before {
        transform: translateX(20px);
    }

    /* 统计数字样式 */
    .stat-number {
        font-size: 0.875rem;
        font-weight: 500;
        color: #1f2937;
    }

    /* 规则文本样式 */
    .rule-text {
        font-size: 0.75rem;
        color: #6b7280;
    }

    /* 添加新样式 */
    .disabled\:opacity-50:disabled {
        opacity: 0.5;
    }
    .disabled\:cursor-not-allowed:disabled {
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<!-- 店铺自动改价卡片 -->
<div class="dashboard-card">
    <!-- 固定顶部区域 -->
    <div class="fixed-header">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">店铺自动改价</h2>
            <div class="flex items-center space-x-4">
                <select id="store-select" 
                        class="h-10 pl-3 pr-10 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                    <option value="">选择要添加的店铺</option>
                </select>
                <button onclick="addSelectedStore()" 
                        class="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>添加店铺
                </button>
            </div>
        </div>
    </div>

    <!-- 改价规则表格 -->
    <div class="scrollable-content">
        <table class="min-w-full bg-white">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">店铺名称</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">改价类型</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品数量</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当次改价数量</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总改价次数</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">改价规则</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                </tr>
            </thead>
            <tbody id="pricing-rules-body">
                <!-- 示例行 -->
                <tr class="hover:bg-gray-50">
                    <td rowspan="2" class="px-6 py-4 align-middle">
                        <div class="font-medium text-gray-900">测试店铺</div>
                        <div class="text-xs text-gray-500">ID: 123456</div>
                        <div class="mt-2 space-y-1">
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-box-open mr-1"></i>
                                <span>总报价: 150</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-shopping-cart mr-1"></i>
                                <span>购物车: 25</span>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <span class="rule-type rule-type-higher">高于购物车</span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="stat-number">150</span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="stat-number">25</span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="stat-number">1,234</span>
                    </td>
                    <td class="px-6 py-4">
                        <div class="rule-text">差价: €0.5</div>
                        <div class="rule-text">最低利润率: 15%</div>
                    </td>
                    <td class="px-6 py-4">
                        <label class="apple-switch">
                            <input type="checkbox" checked>
                            <span class="apple-switch-slider"></span>
                        </label>
                    </td>
                </tr>
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4">
                        <span class="rule-type rule-type-lower">低于购物车</span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="stat-number">45</span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="stat-number">8</span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="stat-number">432</span>
                    </td>
                    <td class="px-6 py-4">
                        <div class="rule-text">差价: €0.3</div>
                        <div class="rule-text">最低利润率: 8%</div>
                    </td>
                    <td class="px-6 py-4">
                        <label class="apple-switch">
                            <input type="checkbox">
                            <span class="apple-switch-slider"></span>
                        </label>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 加载可选店铺列表
    function loadAvailableStores() {
        fetch('/api/stores/available')
            .then(response => response.json())
            .then(stores => {
                const select = document.getElementById('store-select');
                select.innerHTML = '<option value="">选择要添加的店铺</option>' +
                    stores.map(store => `
                        <option value="${store.id}">${store.name}</option>
                    `).join('');
            });
    }

    // 添加选中的店铺
    function addSelectedStore() {
        const select = document.getElementById('store-select');
        const storeId = select.value;
        
        if (!storeId) {
            alert('请选择要添加的店铺');
            return;
        }

        // 默认规则设置 - 移除 equal 规则
        const data = {
            store_id: storeId,
            rules: {
                higher: {
                    enabled: true,
                    difference: 0.5,
                    profit: 15
                },
                lower: {
                    enabled: false,
                    difference: 0.3,
                    profit: 8
                }
            }
        };

        fetch('/api/auto-pricing/stores', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                loadStores();
                select.value = ''; // 重置选择
            } else {
                alert('添加店铺失败: ' + result.message);
            }
        });
    }

    // 初始化页面时加载店铺列表
    document.addEventListener('DOMContentLoaded', function() {
        loadAvailableStores();
        loadStores();
    });

    // JavaScript 代码基本保持不变，只需修改创建规则行的方法
    function createStoreRows(store) {
        const ruleTypes = [
            { type: 'higher', label: '高于购物车', class: 'rule-type-higher' },
            { type: 'lower', label: '低于购物车', class: 'rule-type-lower' }
        ];

        return ruleTypes.map((ruleType, index) => {
            const rule = store.rules[ruleType.type];
            const ruleText = `差价: €${rule.difference}<br>最低利润率: ${rule.profit}%`;

            return `
                <tr>
                    ${index === 0 ? `
                        <td rowspan="2" class="align-middle">
                            <div class="font-medium text-gray-900">${store.name}</div>
                            <div class="text-xs text-gray-500">ID: ${store.id}</div>
                            <div class="mt-2 space-y-1">
                                <div class="flex items-center text-xs text-gray-600">
                                    <i class="fas fa-box-open mr-1"></i>
                                    <span>总报价: ${store.total_products.toLocaleString()}</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-600">
                                    <i class="fas fa-shopping-cart mr-1"></i>
                                    <span>购物车: ${store.buybox_products.toLocaleString()}</span>
                                </div>
                            </div>
                        </td>
                    ` : ''}
                    <td>
                        <span class="rule-type ${ruleType.class}">${ruleType.label}</span>
                    </td>
                    <td>
                        <span class="stat-number">${rule.product_count || 0}</span>
                    </td>
                    <td>
                        <span class="stat-number">${rule.current_changes || 0}</span>
                    </td>
                    <td>
                        <span class="stat-number">${rule.total_changes || 0}</span>
                    </td>
                    <td>
                        <div class="rule-text">${ruleText}</div>
                    </td>
                    <td>
                        <label class="apple-switch">
                            <input type="checkbox" ${rule.enabled ? 'checked' : ''} 
                                   onchange="toggleRule(${store.id}, '${ruleType.type}', this.checked)">
                            <span class="apple-switch-slider"></span>
                        </label>
                    </td>
                </tr>
            `;
        }).join('');
    }

    // 加载店铺列表
    function loadStores() {
        fetch('/api/auto-pricing/stores')
            .then(response => response.json())
            .then(stores => {
                const tbody = document.getElementById('pricing-rules-body');
                tbody.innerHTML = stores.map(store => createStoreRows(store)).join('');
            });
    }

    // 其他 JavaScript 函数保持不变...
</script>
{% endblock %} 