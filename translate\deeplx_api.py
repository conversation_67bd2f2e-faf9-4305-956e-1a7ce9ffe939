import requests
import time
import asyncio
import aiohttp
import logging
from models import SystemSettings

logger = logging.getLogger(__name__)

class DeepLXTranslator:
    def __init__(self):
        """初始化翻译器"""
        self.api_url = SystemSettings.get_setting('translation_api_key', '')  # DeepLX代理地址
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
    
    def refresh_settings(self):
        """刷新设置"""
        self.api_url = SystemSettings.get_setting('translation_api_key', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
            
    def translate(self, text, source_lang="AUTO", target_lang="auto"):
        """翻译文本"""
        if SystemSettings.get_setting('translation_enabled', 'true').lower() != 'true':
            return {
                'success': False,
                'error': '翻译功能已禁用'
            }
            
        if not self.api_url:
            return {
                'success': False,
                'error': '检查Deeplx代理地址'
            }
            
        if not text:
            return {
                'success': False,
                'error': '翻译文本不能为空'
            }
            
        data = {
            "text": text,
            "source_lang": source_lang,
            "target_lang": target_lang
        }
        
        # logger.info(f"开始翻译，代理地址: {self.api_url}")
        # logger.info(f"翻译内容: {text}")
        # logger.info(f"源语言: {source_lang}, 目标语言: {target_lang}")
        
        for retry in range(self.retry_count):
            try:
                # logger.info(f"第 {retry + 1} 次尝试翻译")
                response = requests.post(self.api_url, json=data, timeout=10)
                
                # logger.info(f"响应状态码: {response.status_code}")
                # logger.info(f"响应内容: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    if 'data' in result and result['data']:
                        # logger.info(f"翻译成功: {result['data']}")
                        return {
                            'success': True,
                            'text': result['data'],
                            'source_lang': source_lang,
                            'target_lang': target_lang
                        }
                
                if retry < self.retry_count - 1:
                    # logger.warning(f"翻译失败，{self.retry_interval}秒后重试")
                    time.sleep(self.retry_interval)
                    
            except Exception as e:
                if retry < self.retry_count - 1:
                    time.sleep(self.retry_interval)
                    continue
                
                return {
                    'success': False,
                    'error': f'翻译请求失败: {str(e)}'
                }

        return {
            'success': False,
            'error': f'翻译服务返回错误: {response.text}'
        }
    
    def translate_batch(self, texts, source_lang="AUTO", target_lang="ZH"):
        """批量翻译文本"""
        results = []
        for text in texts:
            result = self.translate(text, source_lang, target_lang)
            results.append(result)
        return results

    async def _translate_single_async(self, session, text, source_lang, target_lang):
        """异步发送单个翻译请求"""
        data = {
            "text": text,
            "source_lang": source_lang,
            "target_lang": target_lang
        }
        
        try:
            async with session.post(self.api_url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if 'data' in result and result['data']:
                        return {
                            'success': True,
                            'text': result['data'],
                            'target_lang': target_lang,
                            'error': None
                        }
                return {
                    'success': False,
                    'text': None,
                    'target_lang': target_lang,
                    'error': f'翻译失败: {await response.text()}'
                }
        except Exception as e:
            return {
                'success': False,
                'text': None,
                'target_lang': target_lang,
                'error': str(e)
            }

    async def translate_product_async(self, text, source_lang="AUTO"):
        """异步翻译产品的标题或描述到四个目标语言"""
        target_langs = ["LT", "LV", "ET", "FI"]
        translations = {lang: None for lang in target_langs}
        failed_langs = []

        async with aiohttp.ClientSession() as session:
            # 首次尝试所有语言
            tasks = [self._translate_single_async(session, text, source_lang, lang) 
                    for lang in target_langs]
            results = await asyncio.gather(*tasks)

            # 处理结果
            for result in results:
                if result['success']:
                    translations[result['target_lang']] = result['text']
                else:
                    failed_langs.append(result['target_lang'])

            # 重试失败的语言
            for lang in failed_langs:
                for retry in range(self.retry_count):
                    result = await self._translate_single_async(session, text, source_lang, lang)
                    if result['success']:
                        translations[lang] = result['text']
                        break
                    await asyncio.sleep(self.retry_interval)

        return translations

    async def translate_product_full_async(self, title, description, source_lang="AUTO"):
        """异步翻译产品的标题和描述"""
        title_translations = await self.translate_product_async(title, source_lang)
        await asyncio.sleep(0.5)
        desc_translations = await self.translate_product_async(description, source_lang)

        return {
            'title': title_translations,
            'description': desc_translations
        }

if __name__ == "__main__":
    # 创建翻译器实例
    translator = DeepLXTranslator()