{% extends "base.html" %}

{% block title %}商品在线管理 - 产品刊登{% endblock %}

{% block styles %}
<style>
    .dashboard-card {
        display: flex;
        flex-direction: column;
        min-height: calc(100vh - 2rem);
        background: white;
        border-radius: 8px;
        overflow: visible; /* 修改为visible，允许下拉菜单溢出 */
    }

    .fixed-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem;
        background: white;
        position: sticky;
        top: 0;
        z-index: 1000; /* 提高层级 */
        width: 100%;
    }

    .scrollable-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
        width: 100%;
        overflow-x: auto;
        position: relative;
        z-index: 1;
    }

    /* 顶部工具栏样式 */
    .toolbar {
        display: flex;
        gap: 0.5rem;
        padding: 0.5rem 0;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
        position: relative;
        z-index: 1010; /* 确保在最上层 */
    }

    .toolbar-button {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }

    .toolbar-button i {
        margin-right: 0.5rem;
    }

    /* 筛选区域样式 */
    .filter-area {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-top: 1px solid #e5e7eb;
        min-height: 32px;
        gap: 0.5rem;
    }

    .filter-item {
        min-width: 120px;
    }

    /* 表格样式 */
    .product-table th {
        padding: 4px 8px !important;
        position: sticky;
        top: 0;
        background: #f9fafb;
        z-index: 2;
        font-size: 12px;
    }

    .product-table td {
        padding: 4px 8px !important;
        font-size: 12px;
    }

    /* 状态标签样式 */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        border-radius: 9999px;
        font-size: 12px;
        font-weight: 500;
        min-width: 60px;
        justify-content: center;
    }

    .status-badge.waiting {
        background-color: #fff7ed;
        color: #c2410c;
    }

    .status-badge.pending {
        background-color: #eff6ff;
        color: #1d4ed8;
    }

    .status-badge.success {
        background-color: #f0fdf4;
        color: #15803d;
    }

    .status-badge.failed {
        background-color: #fef2f2;
        color: #dc2626;
    }

    /* 分页样式 */
    .pagination-button {
        border: 1px solid #e5e7eb;
        border-radius: 0.25rem;
        color: #374151;
        background: white;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 24px;  /* 设置最小宽度 */
    }

    .pagination-button:hover:not(:disabled) {
        background: #f3f4f6;
    }

    .pagination-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 添加状态按钮样式 */
    .status-button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        align-items: center;
        height: auto;
    }
    
    .status-button {
        height: 32px;
        padding: 0 0.75rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        background: white;
        cursor: pointer;
        transition: all 0.2s;
        line-height: 30px;
        display: inline-flex;
        align-items: center;
    }
    
    .status-button.active {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }
    
    .status-button:hover:not(.active) {
        background: #f3f4f6;
    }
    
    .search-area {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        align-items: center;
        justify-content: flex-end; 
        flex: 1;
        min-width: 300px;
    }
    
    .search-group {
        display: flex;
        align-items: center;
        margin-left: auto;  
    }
    
    .search-select {
        min-width: 150px;
        height: 32px;
        padding: 0 0.75rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem 0 0 0.375rem;
        border-right: none;
        font-size: 0.875rem;
        background-color: #f9fafb;
        line-height: 32px;
    }
    
    .search-input {
        flex: 1;
        height: 32px;
        padding: 0 0.75rem;
        border: 1px solid #e5e7eb;
        border-radius: 0 0.375rem 0.375rem 0;
        font-size: 0.875rem;
        min-width: 200px;
        line-height: 32px;
    }
    
    .search-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .search-buttons .toolbar-button {
        height: 32px;
        padding: 0 0.75rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* 表格样式补充 */
    .product-image {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 4px;
        cursor: pointer;
    }

    .product-image-preview {
        position: fixed;
        max-width: 400px;
        max-height: 400px;
        z-index: 1000;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        display: none;
        background: white;
    }

    .truncate-1 {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
        line-height: 1.1;
        font-size: 12px;
    }

    .truncate-2 {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        overflow: hidden;
        max-width: 200px;
        line-height: 1.1;
        /* 添加 Firefox 和其他浏览器的回退支持 */
        max-height: 4.4em; /* line-height * number of lines: 1.1 * 4 = 4.4 */
        font-size: 12px;
    }

    .operation-dropdown {
        position: relative;
        display: inline-block;
    }

    /* 批量操作下拉菜单样式 */
    #batchOperationsMenu {
        position: absolute;
        right: 0;
        top: 100%;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1030; /* 确保在所有元素之上 */
        min-width: 150px;
    }

    .operation-menu {
        position: absolute;
        right: 0;
        top: 100%;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: none;
        z-index: 50;
        min-width: 120px;
    }

    .operation-menu.show {
        display: block;
    }

    .operation-menu-item {
        padding: 4px 8px;
        font-size: 12px;
        color: #374151;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .operation-menu-item:hover {
        background-color: #f3f4f6;
    }

    .country-code {
        display: flex;
        align-items: center;
        padding: 1px 4px;
        border-radius: 4px;
        font-size: 12px;
        margin-bottom: 0px;
        cursor: help;
        width: fit-content;
    }

    .country-code.has-content {
        background-color: #ecfdf5;
        color: #059669;
    }

    .country-code.no-content {
        background-color: #fef2f2;
        color: #dc2626;
    }

    .operation-dropdown button {
        font-size: 12px;
        color: #374151;
        padding: 2px 4px;
    }

    .operation-dropdown button i {
        font-size: 0.75rem;
    }

    .country-codes-container {
        display: flex;
        flex-direction: column;
        gap: 0px;
    }

    .size-weight-info {
        display: flex;
        flex-direction: column;
        gap: 0px;
        font-size: 12px;
        color: #6b7280;
    }

    .html-preview {
        position: absolute;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        padding: 1rem;
        max-width: 400px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 50;
        display: none;
    }

    .text-xs {
        font-size: 12px;
        line-height: 1.1;
    }

    .product-table {
        width: 100%;
        border-collapse: collapse;
        position: relative;
        z-index: 1;
    }

    .product-table th,
    .product-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    /* 设置固定列宽 */
    .product-table th:nth-child(1),
    .product-table td:nth-child(1) {
        width: 40px;  /* 复选框列 */
    }

    .product-table th:nth-child(2),
    .product-table td:nth-child(2) {
        width: 80px;  /* 图片列 */
    }

    .product-table th:nth-child(3),
    .product-table td:nth-child(3) {
        width: 300px;  /* 英文标题/产品ID/店铺/分类列 */
    }

    .product-table th:nth-child(4),
    .product-table td:nth-child(4) {
        width: 80px;  /* 状态列 */
    }

    .product-table th:nth-child(5),
    .product-table td:nth-child(5) {
        width: 60px;  /* 操作列 */
    }

    .product-table th:nth-child(6),
    .product-table td:nth-child(6) {
        width: 120px;  /* SKU列 */
    }

    .product-table th:nth-child(7),
    .product-table td:nth-child(7) {
        width: 200px;  /* 中文标题列 */
    }

    .product-table th:nth-child(8),
    .product-table td:nth-child(8) {
        width: 80px;  /* 站点标题列 */
    }

    .product-table th:nth-child(9),
    .product-table td:nth-child(9) {
        width: 200px;  /* 英文描述列 */
    }

    .product-table th:nth-child(10),
    .product-table td:nth-child(10) {
        width: 80px;  /* 站点描述列 */
    }

    .product-table th:nth-child(11),
    .product-table td:nth-child(11) {
        width: 100px;  /* 包装尺寸重量列 */
    }

    .product-table th:nth-child(12),
    .product-table td:nth-child(12) {
        width: 120px;  /* 创建时间列 */
    }

    /* 表格容器添加水平滚动 */
    .table-container {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        overflow: visible !important; /* 确保下拉菜单可以显示 */
        position: relative;
    }

    /* 固定表头 */
    .fixed-header {
        position: sticky;
        top: 0;
        background-color: white;
        z-index: 10;
        border-bottom: 2px solid #e5e7eb;
    }

    /* 固定列宽的表格布局 */
    .product-table {
        table-layout: fixed;
    }

    /* 文本溢出处理 */
    .truncate-1 {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    .truncate-2 {
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 2.5em;
    }

    .truncate-description {
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
        max-height: 6em;
    }

    .html-preview {
        position: fixed;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1000;
        font-size: 14px;
        line-height: 1.5;
        max-width: 500px;
        max-height: 400px;
        overflow-y: auto;
    }

    /* 修改弹出卡片的样式 */
    .modal-content {
        max-height: 90vh;
        width: 95%;
        max-width: 1000px;
        margin: auto;
    }

    /* 确保内容区域可以滚动 */
    .flex-1.overflow-y-auto {
        max-height: calc(90vh - 130px); /* 减去头部和底部的高度 */
        overflow-y: auto;
        padding-right: 20px; /* 为滚动条预留空间 */
    }

    /* 美化滚动条 */
    .flex-1.overflow-y-auto::-webkit-scrollbar {
        width: 6px;
    }

    .flex-1.overflow-y-auto::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .flex-1.overflow-y-auto::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }

    .flex-1.overflow-y-auto::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* 右侧导航样式优化 */
    .w-40.bg-gray-50 {
        position: sticky;
        top: 0;
        height: fit-content;
        max-height: calc(90vh - 40px);
        overflow-y: auto;
    }

    .w-40.bg-gray-50 a {
        transition: all 0.3s ease;
        border-radius: 6px;
        margin: 4px 0;
    }

    .w-40.bg-gray-50 a:hover {
        background-color: #f3f4f6;
        color: #2563eb;
    }

    .w-40.bg-gray-50 a.active {
        background-color: #eff6ff;
        color: #2563eb;
    }

    /* 表单区域样式优化 */
    .form-section {
        padding: 20px 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    /* 添加产品弹出卡片样式 */
    .modal-content {
        max-height: 90vh;
        display: flex;
        flex-direction: column;
    }

    /* 确保内容区域可以滚动 */
    #formContainer {
        max-height: calc(90vh - 120px);
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #888 #f1f1f1;
    }

    #formContainer::-webkit-scrollbar {
        width: 6px;
    }

    #formContainer::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    #formContainer::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }

    #formContainer::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* 右侧导航样式 */
    .nav-link {
        transition: all 0.2s ease;
    }

    .nav-link.active {
        background-color: #eff6ff;
        color: #2563eb;
    }

    /* 表单区域样式 */
    .form-section {
        padding: 1.5rem 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    /* 添加产品弹出卡片样式 */
    #formContainer {
        scrollbar-width: thin;
        scrollbar-color: #888 #f1f1f1;
    }

    #formContainer::-webkit-scrollbar {
        width: 6px;
    }

    #formContainer::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    #formContainer::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }

    #formContainer::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* 右侧导航样式 */
    .nav-link {
        transition: all 0.2s ease;
    }

    .nav-link.active {
        background-color: #eff6ff;
        color: #2563eb;
    }

    /* 表单区域样式 */
    .form-section {
        padding: 1.5rem 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .form-section:last-child {
        border-bottom: none;
        padding-bottom: 2rem;
    }

    /* 图片预览样式 */
    .image-preview-box {
        position: relative;
        width: 110px;
        height: 110px;
        border-radius: 0.375rem;
        border: 1px solid #e5e7eb;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
    }

    .image-preview-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.375rem;
    }

    .image-preview-box .placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
    }

    /* 修改图片预览容器的布局 */
    .grid.grid-cols-5.gap-4 {
        display: grid;
        grid-template-columns: repeat(5, 120px);
        gap: 1rem;
        margin-top: 0.5rem;
    }

    /* 调整预览框内的图标大小和颜色 */
    .image-preview-box .fas {
        font-size: 1.25rem;
        color: #9ca3af;
    }

    /* 更紧凑的表单布局 */
    .form-section {
        padding: 1rem 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .form-section:last-child {
        border-bottom: none;
        padding-bottom: 1rem;
    }

    /* 输入框样式优化 */
    input, select, textarea {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    /* 标签样式优化 */
    label {
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
    }

    /* 修改店铺选择器样式 */
    #store-selector {
        min-width: 200px;
        height: 32px;
        padding: 0 0.75rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        background-color: white;
        cursor: pointer;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    #store-selector:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }

    #store-selector option {
        padding: 0.5rem;
    }

    /* 分页样式 */
    .fixed-footer {
        position: fixed;
        bottom: 0;
        left: 220px; /* 侧边栏宽度 */
        right: 0;
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 0.5rem 1rem;
        z-index: 40;
        box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    }

    .pagination-button {
        border: 1px solid #e5e7eb;
        border-radius: 0.25rem;
        color: #374151;
        background: white;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 24px;
        height: 24px;
        padding: 0 0.5rem;
        font-size: 0.75rem;
    }

    .pagination-button:hover:not(:disabled) {
        background: #f3f4f6;
    }

    .pagination-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .table-container {
        margin-bottom: 48px;
    }

    @media (max-width: 768px) {
        .fixed-footer {
            left: 0;
        }
    }

    /* 修改国家代码容器样式 */
    .country-codes-container {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .country-code {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 1px 4px;
        border-radius: 4px;
        font-size: 12px;
        cursor: help;
        width: 24px;
        height: 20px;
    }

    .country-code.has-content {
        background-color: #ecfdf5;
        color: #059669;
    }

    .country-code.no-content {
        background-color: #fef2f2;
        color: #dc2626;
    }

    /* 修改预览样式 */
    .html-preview {
        position: fixed;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        padding: 1rem;
        max-width: 400px;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1000;
        display: none;
        font-size: 12px;
        line-height: 1.5;
    }

    /* 添加描述预览的样式 */
    .description-preview {
        position: fixed;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        padding: 0.75rem;
        width: 300px;
        max-height: 200px;
        overflow-y: auto;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1000;
        display: none;
        font-size: 12px;
        line-height: 1.4;
        white-space: pre-line;
        word-break: break-word;
    }

    @media (max-width: 1024px) {
        .toolbar-button {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .status-button {
            padding: 0 0.5rem;
            font-size: 0.75rem;
        }

        .search-group {
            flex: 1;
            min-width: 200px;
        }
    }

    @media (max-width: 768px) {
        .filter-area {
            flex-direction: column;
            align-items: stretch;
        }

        .search-area {
            width: 100%;
            justify-content: flex-start;  /* 修改：在移动端时靠左对齐 */
        }

        .search-group {
            margin-left: 0;  /* 修改：在移动端时移除左侧外边距 */
        }

        .status-button-group {
            width: 100%;
            justify-content: flex-start;
        }

        .fixed-footer {
            left: 0;
        }
    }

    @media (max-width: 640px) {
        .toolbar {
            flex-direction: column;
            align-items: stretch;
        }

        .toolbar-button {
            width: 100%;
            justify-content: center;
        }

        .search-group {
            flex-direction: column;
        }

        .search-select {
            width: 100%;
            border-radius: 0.375rem;
            border-right: 1px solid #e5e7eb;
            margin-bottom: 0.5rem;
        }

        .search-input {
            width: 100%;
            border-radius: 0.375rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-card">
    <div class="fixed-header">
        <div class="flex justify-between items-center mb-4">
        <div class="toolbar">
            <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-yellow-600" onclick="toggleAddProductModal(); return false;">
                <i class="fas fa-plus"></i>添加产品
            </button>
            <button class="h-8 px-3 toolbar-button bg-green-500 text-white hover:bg-yellow-600" id="excelImportBtn">
                <i class="fas fa-file-import"></i>Execl导入
            </button>
            <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-yellow-600" onclick="editSelectedProduct(); return false;">
                <i class="fas fa-edit"></i>编辑
            </button>
            <!-- 需要更改上传按钮   原来batchPublishBtn   更新之后batchCreateBtn--> 
            <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-yellow-600" id="batchCreateBtn"> 
                <i class="fas fa-copy"></i>批量发布
            </button>
            <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-yellow-600" id="batchUpdateBtn">
                <i class="fas fa-sync"></i>同步更新产品
            </button>
            <div class="relative inline-block">
                <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-blue-600" onclick="toggleBatchOperations(this)">
                    <i class="fas fa-sync"></i>批量操作
                    <i class="fas fa-chevron-down ml-1"></i>
                </button>
                <div id="batchOperationsMenu" class="absolute hidden right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="handleBatchTranslate(); return false;">
                            <i class="fas fa-language mr-2"></i>批量翻译
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="handleBatchModify(); return false;">
                            <i class="fas fa-edit mr-2"></i>批量修改
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="handleBatchInquire(); return false;">
                            <i class="fas fa-edit mr-2"></i>批量查询上架
                        </a>
                    </div>
                </div>
            </div>
            <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-yellow-600" onclick="handleBatchDelete(); return false;">
                <i class="fas fa-trash-alt"></i>删除
            </button>
            <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-yellow-600" onclick="togglePublishSuccessModal(); return false;">
                <i class="fas fa-tasks"></i>刊登任务查看
            </button>
            <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-yellow-600" onclick="toggleXmlConvertModal()">
                <i class="fas fa-tasks"></i>XML转换查看
            </button>
            </div>
            <select id="store-selector" class="h-8 px-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">所有店铺</option>
                {% for store in stores %}
                <option value="{{ store.seller_id }}">{{ store.name }}</option>
                {% endfor %}
            </select>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-area">
            <!-- 状态按钮组 -->
            <div class="status-button-group">
                <button class="status-button active">全部</button>
                <button class="status-button">待编辑</button>
                <button class="status-button">上传成功</button>
                <button class="status-button">上传失败</button>
                <button class="status-button">PHH处理创建中</button>
                <button class="status-button">PHH刊登成功</button>
                <button class="status-button">PHH刊登失败</button>
                <button class="status-button">XML转换导入</button>
                <button class="status-button">XML表成功</button>
                <button class="status-button">XML表失败</button>
            </div>
            
            <!-- 搜索区域 -->
            <div class="search-area">
                <div class="search-group">
                    <select class="search-select">
                        <option value="product_sku">产品SKU</option>
                        <option value="product_id">产品ID</option>
                        <option value="category_id">分类ID</option>
                        <option value="category_name">分类名</option>
                        <option value="en_title">英文标题</option>
                        <option value="note">备注</option>
                    </select>
                    <input type="text" class="search-input" placeholder="双击批量查询">
                </div>
                
                <div class="search-buttons">
                    <button class="h-8 px-3 toolbar-button bg-blue-500 text-white hover:bg-blue-600">
                        <i class="fas fa-search"></i>搜索
                    </button>
                    <button class="h-8 px-3 toolbar-button bg-gray-500 text-white hover:bg-gray-600">
                        <i class="fas fa-sync"></i>重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 可滚动的表格区域 -->
    <div class="scrollable-content">
        <table class="min-w-full divide-y divide-gray-200 product-table">
            <thead>
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" class="rounded border-gray-300" onclick="toggleSelectAll(this)">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">图片</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">英文标题/产品ID/店铺/分类</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU/EAN</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品发布结果</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">四站标题</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">英文描述</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">四站描述</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">包装尺寸重量</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间/发布时间</th>

                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <!-- 示例行 -->

            </tbody>
        </table>
    </div>

    <!-- 固定底部分页 -->
    <div class="fixed-footer">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4 text-xs text-gray-700">
                <span id="pagination-info">当前: 0 - 0 行, 共 0 行</span>
                <div class="flex items-center space-x-2">
                    <span>每页</span>
                    <select class="border border-gray-300 rounded px-2 py-1 text-xs h-6" onchange="handlePageSizeChange(this.value)">
                        <option value="50"selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                    <span>行</span>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToFirstPage()" disabled>
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToPrevPage()" disabled>
                    <i class="fas fa-angle-left"></i>
                </button>
                <span class="px-2 text-xs" id="current-page-info">第 0/0 页</span>
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToNextPage()" disabled>
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="pagination-button h-6 px-1 text-xs" onclick="goToLastPage()" disabled>
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 添加产品弹出卡片 -->
    <div id="addProductModal" class="fixed inset-0 z-50 hidden">
        <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
        <div class="fixed inset-0 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg shadow-xl flex w-[1000px] h-[80vh] relative">
                <!-- 左侧主要内容 -->
                <div class="flex-1 flex flex-col h-full border-r border-gray-200">
                    <!-- 头部 -->
                    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-white">
                        <h2 class="text-lg font-medium text-gray-900">添加产品</h2>
                        <button type="button" class="text-gray-400 hover:text-gray-500" onclick="toggleAddProductModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- 表单内容区域 -->
                    <div class="flex-1 overflow-y-auto px-6 py-4" id="formContainer">
                        <form id="productForm" class="space-y-8">
                            <!-- 基本信息 -->
                            <div id="basic-info" class="form-section pt-0">
                                <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <span>基本信息</span>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            选择店铺<span class="text-red-500">*</span>
                                        </label>
                                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="seller_id" required>
                                            <option value="">请选择店铺</option>
                                            {% for store in stores %}
                                            <option value="{{ store.seller_id }}">{{ store.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            SKU<span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="sku" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            EAN
                                        </label>
                                        <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="ean">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            分类ID<span class="text-red-500">*</span>
                                        </label>
                                        <input type="number" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="category_id" required>
                                    </div>
                                </div>
                            </div>

                            <!-- 标题信息 -->
                            <div id="title-info" class="form-section">
                                <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                                    <i class="fas fa-heading mr-2"></i>
                                    <span>标题信息</span>
                                </div>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            英文标题<span class="text-red-500">*</span>
                                        </label>
                                        <div class="flex items-center">
                                            <input type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="title_en" required>
                                            <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="translateModalField('title', 'en')">
                                                <i class="fas fa-language text-xl"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            中文标题
                                        </label>
                                        <div class="flex items-center">
                                            <input type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="title_cn">
                                            <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="translateModalField('title', 'zh')">
                                                <i class="fas fa-language text-xl"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="border-t border-gray-200 pt-4">
                                        <div class="flex space-x-4 mb-4">
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600 active-lang" data-lang="lt">立陶宛语</button>
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600" data-lang="lv">拉脱维亚语</button>
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600" data-lang="et">爱沙尼亚语</button>
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600" data-lang="fi">芬兰语</button>
                                        </div>
                                        <div class="lang-content" data-content="lt">
                                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="title_lt" placeholder="立陶宛语标题">
                                        </div>
                                        <div class="lang-content hidden" data-content="lv">
                                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="title_lv" placeholder="拉脱维亚语标题">
                                        </div>
                                        <div class="lang-content hidden" data-content="et">
                                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="title_et" placeholder="爱沙尼亚语标题">
                                        </div>
                                        <div class="lang-content hidden" data-content="fi">
                                            <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="title_fi" placeholder="芬兰语标题">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 图片信息 -->
                            <div id="image-info" class="form-section">
                                <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                                    <i class="fas fa-images mr-2"></i>
                                    <span>图片信息</span>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="col-span-2">
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    图片链接1<span class="text-red-500">*</span>
                                                </label>
                                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="image_url1" required onchange="previewImage(this, 'preview1')">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    图片链接2
                                                </label>
                                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="image_url2" onchange="previewImage(this, 'preview2')">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    图片链接3
                                                </label>
                                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="image_url3" onchange="previewImage(this, 'preview3')">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    图片链接4
                                                </label>
                                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="image_url4" onchange="previewImage(this, 'preview4')">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    图片链接5
                                                </label>
                                                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="image_url5" onchange="previewImage(this, 'preview5')">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-span-2 mt-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">图片预览</label>
                                        <div class="grid grid-cols-5 gap-4">
                                            <div id="preview1" class="image-preview-box">
                                                <div class="w-full h-24 bg-gray-100 rounded-md flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400 text-xl"></i>
                                                </div>
                                            </div>
                                            <div id="preview2" class="image-preview-box">
                                                <div class="w-full h-24 bg-gray-100 rounded-md flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400 text-xl"></i>
                                                </div>
                                            </div>
                                            <div id="preview3" class="image-preview-box">
                                                <div class="w-full h-24 bg-gray-100 rounded-md flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400 text-xl"></i>
                                                </div>
                                            </div>
                                            <div id="preview4" class="image-preview-box">
                                                <div class="w-full h-24 bg-gray-100 rounded-md flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400 text-xl"></i>
                                                </div>
                                            </div>
                                            <div id="preview5" class="image-preview-box">
                                                <div class="w-full h-24 bg-gray-100 rounded-md flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400 text-xl"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 描述信息 -->
                            <div id="desc-info" class="form-section">
                                <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                                    <i class="fas fa-align-left mr-2"></i>
                                    <span>描述信息</span>
                                </div>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            英文描述<span class="text-red-500">*</span>
                                        </label>
                                        <div class="flex items-center">
                                            <textarea class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="description_en" rows="4" required></textarea>
                                            <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="translateModalField('description', 'en')">
                                                <i class="fas fa-language text-xl"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="border-t border-gray-200 pt-4">
                                        <div class="flex space-x-4 mb-4">
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600" data-lang="lt">立陶宛语</button>
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600" data-lang="lv">拉脱维亚语</button>
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600" data-lang="et">爱沙尼亚语</button>
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600" data-lang="fi">芬兰语</button>
                                        </div>
                                        <div class="lang-content" data-content="lt">
                                            <textarea class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="description_lt" rows="4" placeholder="立陶宛语描述"></textarea>
                                        </div>
                                        <div class="lang-content hidden" data-content="lv">
                                            <textarea class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="description_lv" rows="4" placeholder="拉脱维亚语描述"></textarea>
                                        </div>
                                        <div class="lang-content hidden" data-content="et">
                                            <textarea class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="description_et" rows="4" placeholder="爱沙尼亚语描述"></textarea>
                                        </div>
                                        <div class="lang-content hidden" data-content="fi">
                                            <textarea class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="description_fi" rows="4" placeholder="芬兰语描述"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 包装信息 -->
                            <div id="package-info" class="form-section">
                                <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                                    <i class="fas fa-box mr-2"></i>
                                    <span>包装信息</span>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            包装长度(m)<span class="text-red-500">*</span>
                                        </label>
                                        <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="package_length" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            包装宽度(m)<span class="text-red-500">*</span>
                                        </label>
                                        <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="package_width" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            包装高度(m)<span class="text-red-500">*</span>
                                        </label>
                                        <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="package_height" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            包装重量(kg)<span class="text-red-500">*</span>
                                        </label>
                                        <input type="number" step="0.01" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="package_weight" required>
                                    </div>
                                </div>
                            </div>

                            <!-- 备注信息 -->
                            <div id="note-info" class="form-section">
                                <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                                    <i class="fas fa-sticky-note mr-2"></i>
                                    <span>备注信息</span>
                                </div>
                                <div>
                                    <textarea class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" name="notes" rows="4" placeholder="添加备注信息..."></textarea>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 底部按钮 -->
                    <div class="px-6 py-4 border-t border-gray-200 bg-white">
                        <div class="flex justify-end space-x-3">
                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" onclick="toggleAddProductModal()">取消</button>
                            <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700" onclick="submitProductForm()">保存</button>
                        </div>
                    </div>
                </div>

                <!-- 右侧导航 -->
                <div class="w-40 bg-gray-50 flex flex-col h-full">
                    <div class="p-4 flex-1 overflow-y-auto">
                        <nav class="space-y-1 sticky top-0">
                            <a href="#basic-info" class="nav-link block px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-100 active">
                                <i class="fas fa-info-circle mr-2"></i>基本信息
                            </a>
                            <a href="#title-info" class="nav-link block px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-100">
                                <i class="fas fa-heading mr-2"></i>标题信息
                            </a>
                            <a href="#image-info" class="nav-link block px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-100">
                                <i class="fas fa-images mr-2"></i>图片信息
                            </a>
                            <a href="#desc-info" class="nav-link block px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-100">
                                <i class="fas fa-align-left mr-2"></i>描述信息
                            </a>
                            <a href="#package-info" class="nav-link block px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-100">
                                <i class="fas fa-box mr-2"></i>包装信息
                            </a>
                            <a href="#note-info" class="nav-link block px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-100">
                                <i class="fas fa-sticky-note mr-2"></i>备注信息
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Excel导入导出模态框 -->
<div id="excelModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-[800px] max-h-[600px] relative">
            <!-- 头部 -->
            <div class="flex items-center justify-between px-6 py-3 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Excel导入导出</h2>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="toggleExcelModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="p-6">
                <!-- 导入导出并排布局 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 导入区域 -->
                    <div class="border border-gray-200 rounded-lg p-4 flex flex-col h-full">
                        <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                            <i class="fas fa-file-import mr-2"></i>
                            <span>导入产品</span>
                        </div>
                        <div class="flex-1 space-y-4">
                            <!-- 选择店铺 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    选择店铺<span class="text-red-500">*</span>
                                </label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" id="importStoreSelect" required>
                                    <option value="">请选择店铺</option>
                                    {% for store in stores %}
                                    <option value="{{ store.seller_id }}">{{ store.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- 文件上传区域 -->
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                <div class="text-center">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <div class="text-sm text-gray-600">
                                        <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                                            <span>选择文件</span>
                                            <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".csv,.xlsx,.xls">
                                        </label>
                                        <p class="pl-1">或将文件拖放到这里</p>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">支持 .xlsx, .xls, .csv 格式</p>
                                </div>
                                <div id="file-preview" class="hidden mt-3">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-excel text-green-500 mr-2"></i>
                                            <span class="text-sm text-gray-600" id="file-name"></span>
                                        </div>
                                        <button type="button" class="text-red-500 hover:text-red-700" onclick="removeFile()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 导入按钮 -->
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex justify-center space-x-3">
                                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="importProducts()">
                                    <i class="fas fa-file-import mr-2"></i>开始导入
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 导出区域 -->
                    <div class="border border-gray-200 rounded-lg p-4 flex flex-col h-full">
                        <div class="flex items-center text-base font-medium text-gray-900 mb-4">
                            <i class="fas fa-file-export mr-2"></i>
                            <span>导出产品</span>
                        </div>
                        <div class="flex-1 space-y-4">
                            <!-- 选择店铺 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    选择店铺<span class="text-red-500">*</span>
                                </label>
                                <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" id="exportStoreSelect">
                                    <option value="">请选择店铺</option>
                                    {% for store in stores %}
                                    <option value="{{ store.seller_id }}">{{ store.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- 导出选项区域 -->
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                <div class="text-center">
                                    <i class="fas fa-file-export text-3xl text-gray-400 mb-2"></i>
                                    <div class="text-sm text-gray-600 mb-2">
                                        <p>选择导出选项</p>
                                    </div>
                                    <!-- 导出选项 -->
                                    <div class="flex justify-center space-x-8">
                                        <div class="flex items-center">
                                            <input type="radio" name="exportType" id="exportAll" value="all" class="form-radio" checked>
                                            <label for="exportAll" class="ml-2 text-sm text-gray-700">导出所有产品</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input type="radio" name="exportType" id="exportSelected" value="selected" class="form-radio">
                                            <label for="exportSelected" class="ml-2 text-sm text-gray-700">仅导出选中的产品--暂不可用</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 导出和下载模板按钮 -->
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="flex justify-center space-x-3">
                                    <button type="button" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="downloadTemplate()">
                                        <i class="fas fa-download mr-2"></i>下载模板
                                    </button>
                                    <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="exportProducts()">
                                        <i class="fas fa-file-export mr-2"></i>开始导出
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部说明区域 -->
                <div class="grid grid-cols-3 gap-6 mt-6">
                    <!-- 导入说明 -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 mb-2">导入说明</h3>
                        <ul class="text-xs text-gray-600 space-y-1">
                            <li>• 支持 .xlsx, .xls, .csv 格式文件</li>
                            <li>• 文件大小不超过 10MB</li>
                            <li>• 请严格按照模板格式填写</li>
                            <li>• SKU为必填且唯一</li>
                            <li>• 图片URL需要是可访问的链接</li>
                        </ul>
                    </div>

                    <!-- 导出说明 -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 mb-2">导出说明</h3>
                        <ul class="text-xs text-gray-600 space-y-1">
                            <li>• 默认导出基础所有信息</li>
                            <li>• 导出格式为 .xlsx</li>
                            <li>• 支持按店铺筛选</li>
                        </ul>
                    </div>

                    <!-- 字段说明 -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 mb-2">必填字段说明</h3>
                        <ul class="text-xs text-gray-600 space-y-1">
                            <li>• SKU: 产品唯一标识</li>
                            <li>• 英文标题: 产品英文名称</li>
                            <li>• 分类ID: 产品分类编号</li>
                            <li>• 包装信息: 长宽高和重量</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 修改提示框的标题 -->
<div id="confirmDialog" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm w-full">
            <div class="text-lg font-medium mb-4">提示</div>
            <div class="mb-6" id="confirmMessage"></div>
            <div class="flex justify-end space-x-3">
                <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" onclick="closeConfirmDialog(false)">取消</button>
                <button class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700" onclick="closeConfirmDialog(true)">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 修改提示框的标题 -->
<div id="alertDialog" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm w-full">
            <div class="text-lg font-medium mb-4">提示</div>
            <div class="mb-6" id="alertMessage"></div>
            <div class="flex justify-end">
                <button class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700" onclick="closeAlertDialog()">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加进度条模态框 -->
<div id="progressModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-96">
        <h3 class="text-lg font-medium mb-4">正在翻译...</h3>
        <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div id="progressBar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
        </div>
        <div class="text-center text-sm text-gray-600">
            <span id="progressText">0/0</span> 个产品已完成
        </div>
    </div>
</div>

<!-- 批量修改模态框 -->
<div id="batchModifyModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-[800px] max-h-[600px] relative">
            <!-- 头部 -->
            <div class="flex items-center justify-between px-6 py-3 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900 mx-auto">批量修改</h2>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="toggleBatchModifyModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="p-6">
                <div class="space-y-4">
                    <!-- 分类ID修改 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="modify-category" type="checkbox" class="h-4 w-4 text-blue-600 border-gray-300 rounded">
                            </div>
                            <div class="ml-3 flex-1">
                                <label for="modify-category" class="text-sm font-medium text-gray-700">修改分类ID</label>
                                <div class="mt-2">
                                    <input type="number" id="category-id-input" class="h-8 px-3 border border-gray-300 rounded-md w-full" placeholder="输入新的分类ID">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- SKU前缀修改 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="modify-sku-prefix" type="checkbox" class="h-4 w-4 text-blue-600 border-gray-300 rounded">
                            </div>
                            <div class="ml-3 flex-1">
                                <label for="modify-sku-prefix" class="text-sm font-medium text-gray-700">修改SKU前缀</label>
                                <div class="mt-2">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-xs text-gray-500 mb-1">旧前缀</label>
                                            <input type="text" id="old-prefix-input" class="h-8 px-3 border border-gray-300 rounded-md w-full" placeholder="输入旧前缀">
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-500 mb-1">新前缀</label>
                                            <input type="text" id="new-prefix-input" class="h-8 px-3 border border-gray-300 rounded-md w-full" placeholder="输入新前缀">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 底部按钮 -->
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex justify-end space-x-3">
                    <button onclick="toggleBatchModifyModal()" class="h-8 px-4 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                    <button onclick="executeBatchModify()" class="h-8 px-4 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">确认修改</button>
                </div>
            </div>
        </div>
    </div>
</div>

{% include 'publish_success_modal.html' %}
{% include 'xml_convert_modal.html' %}
{% endblock %}

{% block scripts %}
<script>
    let currentPage = 1;
    let pageSize = 30;
    let totalPages = 1;
    let currentStatus = '';
    let currentSearch = '';
    let currentSellerId = '';

    // 页面加载完成后获取数据
    document.addEventListener('DOMContentLoaded', function() {
        // 从 sessionStorage 恢复状态
        const savedSellerId = sessionStorage.getItem('currentSellerId');
        const savedStatus = sessionStorage.getItem('currentStatus');
        const savedSearch = sessionStorage.getItem('currentSearch');
        const savedPage = sessionStorage.getItem('currentPage');
        const savedPageSize = sessionStorage.getItem('pageSize');

        // 如果不是页面刷新，则恢复保存的状态
        if (performance.navigation.type !== performance.navigation.TYPE_RELOAD) {
            if (savedSellerId) {
                currentSellerId = savedSellerId;
                document.getElementById('store-selector').value = savedSellerId;
            }
            
            if (savedStatus) {
                currentStatus = savedStatus;
                // 恢复状态按钮的激活状态
                document.querySelectorAll('.status-button').forEach(button => {
                    const statusMap = {
                        'pending_edit': '待编辑',
                        'upload_successful': '上传成功',
                        'upload_failed': '上传失败',
                        'phh_processing': 'PHH处理创建中',
                        'phh_success': 'PHH刊登成功',
                        'phh_error': 'PHH刊登失败',
                        'xml_import': 'XML转换导入',
                        'xml_success': 'XML表成功',
                        'xml_failed': 'XML表失败'
                    };
                    if (statusMap[savedStatus] === button.textContent) {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }
                });
            }
            
            if (savedSearch) {
                currentSearch = savedSearch;
                document.querySelector('.search-input').value = savedSearch;
            }
            
            if (savedPage) {
                currentPage = parseInt(savedPage);
            }
            
            if (savedPageSize) {
                pageSize = parseInt(savedPageSize);
                document.querySelector('.fixed-footer select').value = savedPageSize;
            }
        } else {
            // 如果是页面刷新，清除保存的状态
            sessionStorage.clear();
        }

        // 加载产品列表
        loadProducts();
        
        // 添加店铺选择器事件监听
        document.getElementById('store-selector').addEventListener('change', function() {
            currentSellerId = this.value;
            currentPage = 1;
            loadProducts();
        });
        
        // 添加状态按钮事件监听
        document.querySelectorAll('.status-button').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.status-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                // 修改这里的状态映射，确保与数据库枚举值完全一致
                const statusMap = {
                    '待编辑': 'pending_edit',
                    '上传成功': 'upload_successful',
                    '上传失败': 'upload_failed',
                    'PHH处理创建中': 'phh_processing',
                    'PHH刊登成功': 'phh_success',  // 确保这里的值完全正确
                    'PHH刊登失败': 'phh_error',
                    'XML转换导入': 'xml_import',
                    'XML表成功': 'xml_success',
                    'XML表失败': 'xml_failed'
                };
                currentStatus = this.textContent === '全部' ? undefined : statusMap[this.textContent];
                currentPage = 1;
                loadProducts();
            });
        });
        
        // 添加搜索按钮事件监听
        document.querySelector('.search-buttons .toolbar-button').addEventListener('click', function() {
            currentSearch = document.querySelector('.search-input').value;
            currentPage = 1;
            loadProducts();
        });
        
        // 添加重置按钮事件监听
        document.querySelector('.search-buttons .toolbar-button:last-child').addEventListener('click', function() {
            document.querySelector('.search-input').value = '';
            currentSearch = '';
            currentPage = 1;
            loadProducts();
        });
        
        // 添加分页大小选择事件监听
        document.querySelector('.fixed-footer select').addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1;
            loadProducts();
        });

        // 批量发布按钮事件监听
        const batchPublishBtn = document.getElementById('batchPublishBtn');
        if (batchPublishBtn) {
            batchPublishBtn.addEventListener('click', handleBatchPublish);
        }

        // 批量更新按钮事件监听
        const batchUpdateBtn = document.getElementById('batchUpdateBtn');
        if (batchUpdateBtn) {
            batchUpdateBtn.addEventListener('click', handleProductUpdate);
        }

        // 批量创建按钮事件监听
        const batchCreateBtn = document.getElementById('batchCreateBtn');
        if (batchCreateBtn) {
            batchCreateBtn.addEventListener('click', handleBatchCreate);
        }

        // 修改Excel导入按钮的事件监听
        const excelImportBtn = document.getElementById('excelImportBtn');
        if (excelImportBtn) {
            excelImportBtn.addEventListener('click', function() {
                toggleExcelModal();
            });
        }

        // 修改语言切换的事件监听代码
        function initLanguageSwitchers() {
            // ... 其他现有代码 ...

            // 修改语言切换按钮的事件监听
            document.querySelectorAll('.border-t [data-lang]').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.dataset.lang;
                    const parent = this.closest('.border-t');
                    
                    // 移除所有活动状态
                    parent.querySelectorAll('[data-lang]').forEach(b => {
                        b.classList.remove('active-lang', 'text-blue-600', 'border-blue-600');
                    });
                    parent.querySelectorAll('.lang-content').forEach(c => c.classList.add('hidden'));
                    
                    // 添加新的活动状态
                    this.classList.add('active-lang', 'text-blue-600', 'border-blue-600');
                    const content = parent.querySelector(`[data-content="${lang}"]`);
                    if (content) {
                        content.classList.remove('hidden');
                    }
                });
            });
        }

        // 在打开编辑模态框时初始化语言切换器
        const originalEditProduct = window.editProduct;
        window.editProduct = function(productId) {
            originalEditProduct(productId);
            // 等待模态框内容加载完成后初始化语言切换器
            setTimeout(initLanguageSwitchers, 100);
        };

        // 在打开添加产品模态框时也初始化语言切换器
        const originalToggleAddProductModal = window.toggleAddProductModal;
        window.toggleAddProductModal = function() {
            originalToggleAddProductModal();
            if (!document.getElementById('addProductModal').classList.contains('hidden')) {
                setTimeout(initLanguageSwitchers, 100);
            }
        };

        // 在页面加载时检查翻译功能是否启用
        checkTranslationEnabled();
    });

    // 批量发布处理函数
    async function handleBatchPublish() {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        if (checkboxes.length === 0) {
            notification.warning('请选择要发布的产品');
            return;
        }

        // 收集选中产品的SKU和状态
        const selectedProducts = [];
        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row && checkbox.value !== 'all') {
                const skuElement = row.querySelector('td:nth-child(6) .truncate-1');
                const statusElement = row.querySelector('.status-badge');
                if (skuElement) {
                    const sku = skuElement.textContent.trim();
                    // 检查是否是失败状态的产品
                    const isFailedProduct = statusElement && 
                        (statusElement.textContent.includes('刊登失败') || 
                         statusElement.textContent.includes('上传失败'));
                    
                    selectedProducts.push({
                        sku: sku,
                        is_retry: isFailedProduct // 标记是否是重试发布
                    });
                }
            }
        });

        if (selectedProducts.length === 0) {
            notification.warning('未找到有效的SKU');
            return;
        }

        const confirmed = await notification.confirm(
            `确定要发布选中的 ${selectedProducts.length} 个产品吗？`,
            '批量发布确认'
        );
        
        if (!confirmed) {
            return;
        }

        const totalCount = selectedProducts.length;
        let completedCount = 0;
        
        // 修改加载提示，显示进度
        const loading = notification.showLoading(`正在发布... ${completedCount}/${totalCount}`);
        this.disabled = true;

        try {
            // 发送请求时包含重试标识
            const response = await fetch('/api/publish/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    products: selectedProducts,
                    onProgress: (completed) => {
                        // 更新进度显示
                        completedCount = completed;
                        loading.updateMessage(`正在发布... ${completedCount}/${totalCount}`);
                    }
                })
            });

            const result = await response.json();
            
            if (response.ok) {
                loading.remove();
                // 先显示发布完成的基本信息
                let message = '发布完成\n';
                if (result.success_count !== undefined && result.failed_count !== undefined) {
                    message += `成功: ${result.success_count} 个\n失败: ${result.failed_count} 个`;
                } else {
                    message += `已提交 ${selectedProducts.length} 个产品发布请求`;
                }
                notification.success(message);
                loadProducts();  //重新加载产品列表

                // 如果有错误信息，单独显示失败详情
                if (result.errors && result.errors.length > 0) {
                    // 延迟一小段时间后显示错误详情，避免和成功消息重叠
                    setTimeout(() => {
                        notification.error('失败详情:\n' + result.errors.join('\n'));
                    }, 1000);
                }
                loadProducts();  //重新加载产品列表
            } else {
                throw new Error(result.error || '发布失败');
            }
        } catch (error) {
            console.error('Error:', error);
            loading.remove();
            notification.error(error.message || '发布失败，请重试');
        } finally {
            this.disabled = false;
        }
    }

    // 确保 notification 组件支持更新消息
    if (!notification.showLoading.prototype.updateMessage) {
        const originalShowLoading = notification.showLoading;
        notification.showLoading = function(message) {
            const loadingElement = originalShowLoading.call(this, message);
            loadingElement.updateMessage = function(newMessage) {
                const messageElement = loadingElement.querySelector('.message');
                if (messageElement) {
                    messageElement.textContent = newMessage;
                }
            };
            return loadingElement;
        };
    }

    // 添加一个用于显示详细错误信息的辅助函数
    function formatErrorMessage(errors) {
        if (!Array.isArray(errors) || errors.length === 0) {
            return '';
        }
        
        return errors.map((error, index) => {
            if (typeof error === 'object') {
                return `${index + 1}. ${error.sku || ''}: ${error.message || '未知错误'}`;
            }
            return `${index + 1}. ${error}`;
        }).join('\n');
    }

    // 加载产品数据
    function loadProducts() {
        // 保存当前状态到 sessionStorage
        sessionStorage.setItem('currentSellerId', currentSellerId);
        sessionStorage.setItem('currentStatus', currentStatus);
        sessionStorage.setItem('currentSearch', currentSearch);
        sessionStorage.setItem('currentPage', currentPage);
        sessionStorage.setItem('pageSize', pageSize);

        const url = new URL('/api/list-products', window.location.origin);
        url.searchParams.append('page', currentPage);
        url.searchParams.append('per_page', pageSize);
        if (currentSearch) url.searchParams.append('search', currentSearch);
        // 只有当 currentStatus 有值且不是 undefined 时才添加状态参数
        if (currentStatus !== undefined && currentStatus !== null) {
            url.searchParams.append('status', currentStatus);
        }
        if (currentSellerId) url.searchParams.append('seller_id', currentSellerId);
        
        // 先获取店铺信息
        fetch('/api/phh_store/stores')
            .then(response => response.json())
            .then(storeData => {
                // 创建店铺ID到店铺名称的映射
                const storeMap = {};
                storeData.forEach(store => {
                    storeMap[store.seller_id] = store.name;
                });

                // 获取产品列表
                return fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            console.error('Error:', data.error);
                            return;
                        }
                        
                        const tbody = document.querySelector('.product-table tbody');
                        if (!tbody) return;
                        
                        tbody.innerHTML = '';
                        
                        data.products.forEach(product => {
                            const tr = document.createElement('tr');
                            tr.className = 'hover:bg-blue-50 cursor-pointer';
                            
                            // 获取店铺名称
                            const storeName = storeMap[product.seller_id] || '未知店铺';
                            
                            // 构建表格行HTML
                            tr.innerHTML = `
                                <td class="px-6 py-4" onclick="event.stopPropagation()">
                                    <input type="checkbox" name="product_id" value="${product.id}" class="rounded border-gray-300" onclick="updateHeaderCheckbox()">
                                </td>
                                <td class="px-6 py-4">
                                    <img src="${product.image_url1 || ''}" alt="产品图片" class="product-image" onmouseover="showImagePreview(this)" onmouseout="hideImagePreview()">
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="truncate-1" title="${product.title_en || ''}">${product.title_en || ''}</div>
                                    </div>
                                    <div class="text-xs text-gray-500">产品ID: ${product.id}</div>
                                    <div class="text-xs text-gray-500">店铺: ${storeName}</div>
                                    <div class="text-xs text-gray-500">分类ID: ${product.category_id || ''}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="status-badge ${getStatusClass(product.status)}">${getStatusText(product.status)}</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="operation-dropdown">
                                        <button class="hover:text-blue-900" onclick="toggleOperationMenu(this)">
                                            <i class="fas fa-chevron-down ml-1"></i>
                                        </button>
                                        <div class="operation-menu">
                                            <div class="operation-menu-item" onclick="handleOperationClick('edit', ${product.id})">
                                                <i class="fas fa-edit mr-2"></i>编辑
                                            </div>
                                            <div class="operation-menu-item" onclick="handleOperationClick('note', ${product.id})">
                                                <i class="fas fa-edit mr-2"></i>备注
                                            </div>
                                            <div class="operation-menu-item text-red-600" onclick="handleOperationClick('delete', ${product.id})">
                                                <i class="fas fa-trash-alt mr-2"></i>删除
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="truncate-1">${product.sku || ''}</div>
                                    <div class="text-xs text-gray-500">${product.ean || '-'}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="truncate-1">${product.title_cn || ''}</div>
                                        <div class="text-xs text-red-500 mt-1">${product.platform_message || ''}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="country-codes-container">
                                        <span class="country-code ${product.title_lt ? 'has-content' : 'no-content'}" title="${product.title_lt || '暂无立陶宛标题'}">LT</span>
                                        <span class="country-code ${product.title_lv ? 'has-content' : 'no-content'}" title="${product.title_lv || '暂无拉脱维亚标题'}">LV</span>
                                        <span class="country-code ${product.title_et ? 'has-content' : 'no-content'}" title="${product.title_et || '暂无爱沙尼亚标题'}">EE</span>
                                        <span class="country-code ${product.title_fi ? 'has-content' : 'no-content'}" title="${product.title_fi || '暂无芬兰标题'}">FI</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="truncate-description" onmouseover="showFormattedHtmlPreview(this)" onmouseout="hideHtmlPreview()">
                                            ${product.description_en}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="country-codes-container">
                                        <span class="country-code ${product.description_lt ? 'has-content' : 'no-content'}" 
                                              data-description="${escapeHtml(product.description_lt || '暂无立陶宛描述')}"
                                              onmouseover="showDescriptionPreview(this)"
                                              onmouseout="hideDescriptionPreview()">LT</span>
                                        <span class="country-code ${product.description_lv ? 'has-content' : 'no-content'}" 
                                              data-description="${escapeHtml(product.description_lv || '暂无拉脱维亚描述')}"
                                              onmouseover="showDescriptionPreview(this)"
                                              onmouseout="hideDescriptionPreview()">LV</span>
                                        <span class="country-code ${product.description_et ? 'has-content' : 'no-content'}" 
                                              data-description="${escapeHtml(product.description_et || '暂无爱沙尼亚描述')}"
                                              onmouseover="showDescriptionPreview(this)"
                                              onmouseout="hideDescriptionPreview()">EE</span>
                                        <span class="country-code ${product.description_fi ? 'has-content' : 'no-content'}" 
                                              data-description="${escapeHtml(product.description_fi || '暂无芬兰描述')}"
                                              onmouseover="showDescriptionPreview(this)"
                                              onmouseout="hideDescriptionPreview()">FI</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-xs size-weight-info">
                                        <div>长度: ${product.package_length || '-'}</div>
                                        <div>宽度: ${product.package_width || '-'}</div>
                                        <div>高度: ${product.package_height || '-'}</div>
                                        <div>重量: ${product.package_weight || '-'}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm">
                                        <div class="truncate-1">${product.created_at || ''}</div>
                                        <div class="text-xs text-gray-500">${product.publish_time || '-'}</div>
                                    </div>
                                </td>
                            `;
                            
                            // 添加行点击事件
                            tr.addEventListener('click', function(e) {
                                const checkbox = this.querySelector('input[type="checkbox"]');
                                if (checkbox) {
                                    checkbox.checked = !checkbox.checked;
                                    updateHeaderCheckbox();
                                }
                            });
                            
                            tbody.appendChild(tr);
                        });
                        
                        // 更新分页信息
                        totalPages = data.pagination.total_pages;
                        updatePaginationInfo(data);
                        
                        // 重置复选框状态
                        resetCheckboxStates();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showError('加载产品列表失败');
                    });
            })
            .catch(error => console.error('Error:', error));
    }

    // 更新分页信息
    function updatePaginationInfo(data) {
        const startRow = (currentPage - 1) * pageSize + 1;
        const endRow = Math.min(startRow + data.products.length - 1, data.pagination.total_count);
        const totalCount = data.pagination.total_count;
        
        // 更新当前页显示信息
        document.getElementById('pagination-info').textContent = 
            `当前: ${totalCount > 0 ? startRow : 0} - ${endRow} 行, 共 ${totalCount} 行`;
        
        // 更新页码信息
        document.getElementById('current-page-info').textContent = 
            `第 ${currentPage}/${Math.max(1, data.pagination.total_pages)} 页`;
            
        // 更新分页按钮状态
        updatePaginationButtons(data.pagination.total_pages);
    }

    // 更新分页按钮状态
    function updatePaginationButtons(totalPages) {
        const firstPageBtn = document.querySelector('.pagination-button:nth-child(1)');
        const prevPageBtn = document.querySelector('.pagination-button:nth-child(2)');
        const nextPageBtn = document.querySelector('.pagination-button:nth-child(4)');
        const lastPageBtn = document.querySelector('.pagination-button:nth-child(5)');
        
        firstPageBtn.disabled = currentPage <= 1;
        prevPageBtn.disabled = currentPage <= 1;
        nextPageBtn.disabled = currentPage >= totalPages;
        lastPageBtn.disabled = currentPage >= totalPages;
    }

    // 页面大小改变处理
    function handlePageSizeChange(newSize) {
        pageSize = parseInt(newSize);
        currentPage = 1;
        loadProducts();
    }

    // 页面跳转函数
    function goToFirstPage() {
        if (currentPage > 1) {
            currentPage = 1;
            loadProducts();
        }
    }

    function goToPrevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadProducts();
        }
    }

    function goToNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            loadProducts();
        }
    }

    function goToLastPage() {
        if (currentPage < totalPages) {
            currentPage = totalPages;
            loadProducts();
        }
    }

    // 保留原有的图片预览和操作菜单功能
    function showImagePreview(img) {
        const preview = document.createElement('img');
        preview.src = img.src;
        preview.className = 'product-image-preview';
        preview.style.position = 'fixed';
        
        document.addEventListener('mousemove', movePreview);
        function movePreview(e) {
            preview.style.left = e.pageX + 10 + 'px';
            preview.style.top = e.pageY + 10 + 'px';
        }
        
        document.body.appendChild(preview);
        preview.style.display = 'block';
        
        img.previewElement = preview;
    }

    function hideImagePreview() {
        const previews = document.querySelectorAll('.product-image-preview');
        previews.forEach(preview => {
            preview.remove();
        });
    }

    // 修改操作菜单点击事件，阻止事件冒泡
    function toggleOperationMenu(button) {
        event.stopPropagation();  // 阻止事件冒泡
        const menu = button.nextElementSibling;
        const allMenus = document.querySelectorAll('.operation-menu');
        
        allMenus.forEach(m => {
            if (m !== menu) m.classList.remove('show');
        });
        
        menu.classList.toggle('show');
    }

    document.addEventListener('click', function(e) {
        if (!e.target.closest('.operation-dropdown')) {
            document.querySelectorAll('.operation-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    function showFormattedHtmlPreview(element) {
        const preview = document.createElement('div');
        preview.className = 'html-preview';
        
        // 获取原始内容并解析HTML
        const content = element.textContent;
        // 将<br />转换为实际的换行
        const formattedContent = content.replace(/<br\s*\/?>/gi, '<br>');
        preview.innerHTML = formattedContent;
        
        const rect = element.getBoundingClientRect();
        preview.style.left = rect.right + 10 + 'px';
        preview.style.top = rect.top + 'px';
        preview.style.display = 'block';
        
        document.body.appendChild(preview);
    }

    function hideHtmlPreview() {
        const previews = document.querySelectorAll('.html-preview');
        previews.forEach(preview => preview.remove());
    }

    function getStatusText(status) {
        switch(status) {
            case 'pending_edit': return '待编辑';
            case 'upload_successful': return '上传成功';
            case 'upload_failed': return '上传失败';
            case 'phh_processing': return '创建中';
            case 'phh_success': return '刊登成功';
            case 'phh_error': return '刊登失败';
            case 'xml_import': return 'XML表';
            case 'xml_success': return 'XML成功';
            case 'xml_failed': return 'XML失败';
            default: return '待编辑';
        }
    }

    function getStatusClass(status) {
        switch(status) {
            case 'pending_edit': return 'bg-gray-100 text-gray-800';      // 灰色底，深灰字
            case 'upload_successful': return 'bg-blue-100 text-blue-800'; // 蓝色底，深蓝字
            case 'upload_failed': return 'bg-red-100 text-red-800';       // 红色底，深红字
            case 'phh_processing': return 'bg-yellow-100 text-yellow-800'; // 黄色底，深黄字
            case 'phh_success': return 'bg-green-100 text-green-800';     // 绿色底，深绿字
            case 'phh_error': return 'bg-red-100 text-red-800';          // 红色底，深红字
            case 'xml_import': return 'bg-purple-100 text-purple-800';    // 紫色底，深紫字
            case 'xml_success': return 'bg-green-100 text-green-800';     // 绿色底，深绿字
            case 'xml_failed': return 'bg-red-100 text-red-800';          // 红色底，深红字
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    // 添加HTML转纯文本函数
    function stripHtml(html) {
        const tmp = document.createElement('div');
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || '';
    }

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .truncate-description {
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.5;
        }

        .html-preview {
            position: fixed;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            font-size: 14px;
            line-height: 1.5;
            max-width: 500px;
            max-height: 400px;
            overflow-y: auto;
        }
    `;
    document.head.appendChild(style);

    // 添加产品模态框控制
    function toggleAddProductModal() {
        const modal = document.getElementById('addProductModal');
        modal.classList.toggle('hidden');
        
        // 如果是关闭模态框，则重置表单
        if (!modal.classList.contains('hidden')) {
            resetForm();
        }
    }

    // 重置表单函数
    function resetForm() {
        const form = document.getElementById('productForm');
        form.reset();
        
        // 重置店铺选择器状态
        const sellerIdSelect = form.querySelector('[name="seller_id"]');
        sellerIdSelect.disabled = false;
        sellerIdSelect.classList.remove('bg-gray-100', 'cursor-not-allowed');
        
        // 重置图片预览
        for (let i = 1; i <= 5; i++) {
            const preview = document.getElementById(`preview${i}`);
            if (preview) {
                preview.innerHTML = `
                    <div class="placeholder">
                        <i class="fas fa-image text-gray-400 text-xl"></i>
                    </div>
                `;
            }
        }

        // 重置语言切换按钮状态
        document.querySelectorAll('[data-lang]').forEach(btn => {
            btn.classList.remove('active-lang', 'text-blue-600', 'border-blue-600');
        });
        
        // 默认显示第一个语言的内容
        const firstLangBtn = document.querySelector('[data-lang="lt"]');
        if (firstLangBtn) {
            firstLangBtn.classList.add('active-lang', 'text-blue-600', 'border-blue-600');
        }

        // 重置所有语言内容区域
        document.querySelectorAll('.lang-content').forEach(content => {
            content.classList.add('hidden');
        });
        const firstContent = document.querySelector('[data-content="lt"]');
        if (firstContent) {
            firstContent.classList.remove('hidden');
        }
    }

    // 语言切换
    document.querySelectorAll('[data-lang]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const lang = this.dataset.lang;
            const parent = this.closest('.border-t');
            
            // 移除所有活动状态
            parent.querySelectorAll('[data-lang]').forEach(b => {
                b.classList.remove('active-lang', 'text-blue-600', 'border-blue-600');
            });
            parent.querySelectorAll('.lang-content').forEach(c => c.classList.add('hidden'));
            
            // 添加新的活动状态
            this.classList.add('active-lang', 'text-blue-600', 'border-blue-600');
            const content = parent.querySelector(`[data-content="${lang}"]`);
            if (content) {
                content.classList.remove('hidden');
            }
        });
    });

    // 表单提交
    function submitProductForm() {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);
        const data = {};
        
        // 检查必填字段
        const requiredFields = [
            'seller_id',
            'sku',
            'category_id',
            'title_en',
            'title_cn',
            'description_en',
            'package_length',
            'package_width',
            'package_height',
            'package_weight'
        ];

        let hasError = false;
        requiredFields.forEach(field => {
            const value = formData.get(field);
            if (!value || value.trim() === '') {
                hasError = true;
                // 找到对应的输入框并添加错误样式
                const input = form.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('border-red-500');
                    // 添加错误提示
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'text-red-500 text-xs mt-1';
                    errorMsg.textContent = '此字段为必填项';
                    const parent = input.parentElement;
                    // 移除已存在的错误提示
                    const existingError = parent.querySelector('.text-red-500');
                    if (existingError) {
                        parent.removeChild(existingError);
                    }
                    parent.appendChild(errorMsg);
                }
            }
        });

        if (hasError) {
            alert('请填写所有必填字段');
            return;
        }

        // 处理包装尺寸和重量的数值转换
        const numberFields = ['package_length', 'package_width', 'package_height', 'package_weight'];
        numberFields.forEach(field => {
            const value = formData.get(field);
            if (value) {
                data[field] = parseFloat(value);
            }
        });
        
        // 收集所有表单数据
        formData.forEach((value, key) => {
            if (!numberFields.includes(key)) {
                data[key] = value;
            }
        });

        // 收集图片链接
        for (let i = 1; i <= 5; i++) {
            const imageUrl = formData.get(`image_url${i}`);
            if (imageUrl && imageUrl.trim() !== '') {
                data[`image_url${i}`] = imageUrl;
            }
        }

        // 发送请求
        fetch('/api/product/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => {
                    throw new Error(err.message || '保存失败');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            alert('产品添加成功');
            toggleAddProductModal();
            // 刷新产品列表
            loadProducts();
        })
        .catch(error => {
            console.error('Error:', error);
            alert(error.message || '添加失败，请重试');
        });
    }

    // 添加输入框焦点事件，移除错误样式
    document.querySelectorAll('input, select, textarea').forEach(input => {
        input.addEventListener('focus', function() {
            this.classList.remove('border-red-500');
            const errorMsg = this.parentElement.querySelector('.text-red-500');
            if (errorMsg) {
                errorMsg.remove();
            }
        });
    });

    // 添加滚动导航功能
    document.addEventListener('DOMContentLoaded', function() {
        const formContainer = document.getElementById('formContainer');
        const navLinks = document.querySelectorAll('.nav-link');
        
        // 点击导航链接处理
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                // 移除所有导航项的激活状态
                navLinks.forEach(l => l.classList.remove('active'));
                // 添加当前项的激活状态
                this.classList.add('active');
                
                if (targetElement) {
                    formContainer.scrollTo({
                        top: targetElement.offsetTop - 20,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // 滚动时更新导航状态
        formContainer.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.form-section');
            let currentSection = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (formContainer.scrollTop >= sectionTop - 100) {
                    currentSection = section.id;
                }
            });
            
            // 更新导航激活状态
            navLinks.forEach(link => {
                const href = link.getAttribute('href').substring(1);
                if (href === currentSection) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
        });
    });

    // 图片预览功能
    function previewImage(input, previewId) {
        const preview = document.getElementById(previewId);
        if (input.value) {
            // 创建新的图片预览
            const img = document.createElement('img');
            img.src = input.value;
            img.onerror = function() {
                preview.innerHTML = `
                    <div class="placeholder">
                        <i class="fas fa-exclamation-circle text-red-400 text-xl"></i>
                    </div>
                `;
            };
            img.onload = function() {
                preview.innerHTML = '';
                preview.appendChild(img);
            };
        } else {
            // 恢复默认占位符
            preview.innerHTML = `
                <div class="placeholder">
                    <i class="fas fa-image text-gray-400 text-xl"></i>
                </div>
            `;
        }
    }

    // 编辑产品功能
    function editProduct(productId) {
        // 获取产品信息
        fetch(`/api/product/edit/${productId}`)
            .then(response => response.json())
            .then(product => {
                // 打开模态框
                const modal = document.getElementById('addProductModal');
                modal.classList.remove('hidden');
                
                const form = document.getElementById('productForm');
                
                // 基本信息
                const sellerIdSelect = form.querySelector('[name="seller_id"]');
                sellerIdSelect.value = product.seller_id;
                // 设置店铺选择器为禁用状态
                sellerIdSelect.disabled = true;
                sellerIdSelect.classList.add('bg-gray-100', 'cursor-not-allowed');
                
                form.querySelector('[name="sku"]').value = product.sku;
                form.querySelector('[name="ean"]').value = product.ean || '';
                form.querySelector('[name="category_id"]').value = product.category_id;
                
                // 标题信息
                form.querySelector('[name="title_en"]').value = product.title_en;
                form.querySelector('[name="title_cn"]').value = product.title_cn;
                form.querySelector('[name="title_lt"]').value = product.title_lt || '';
                form.querySelector('[name="title_lv"]').value = product.title_lv || '';
                form.querySelector('[name="title_et"]').value = product.title_et || '';
                form.querySelector('[name="title_fi"]').value = product.title_fi || '';
                
                // 图片信息
                form.querySelector('[name="image_url1"]').value = product.image_url1 || '';
                form.querySelector('[name="image_url2"]').value = product.image_url2 || '';
                form.querySelector('[name="image_url3"]').value = product.image_url3 || '';
                form.querySelector('[name="image_url4"]').value = product.image_url4 || '';
                form.querySelector('[name="image_url5"]').value = product.image_url5 || '';
                
                // 更新图片预览
                for (let i = 1; i <= 5; i++) {
                    previewImage(form.querySelector(`[name="image_url${i}"]`), `preview${i}`);
                }
                
                // 描述信息
                form.querySelector('[name="description_en"]').value = product.description_en;
                form.querySelector('[name="description_lt"]').value = product.description_lt || '';
                form.querySelector('[name="description_lv"]').value = product.description_lv || '';
                form.querySelector('[name="description_et"]').value = product.description_et || '';
                form.querySelector('[name="description_fi"]').value = product.description_fi || '';
                
                // 包装信息
                form.querySelector('[name="package_length"]').value = product.package_length;
                form.querySelector('[name="package_width"]').value = product.package_width;
                form.querySelector('[name="package_height"]').value = product.package_height;
                form.querySelector('[name="package_weight"]').value = product.package_weight;
                
                // 备注信息
                form.querySelector('[name="notes"]').value = product.notes || '';
                
                // 修改保存按钮的点击事件
                const saveButton = modal.querySelector('button.bg-blue-600');
                saveButton.onclick = () => updateProduct(product.id);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('获取产品信息失败');
            });
    }

    // 更新产品信息
    function updateProduct(productId) {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);
        const data = {};
        
        // 检查必填字段
        const requiredFields = [
            'sku',
            'category_id',
            'title_en',
            'description_en',
            'package_length',
            'package_width',
            'package_height',
            'package_weight'
        ];

        let hasError = false;
        requiredFields.forEach(field => {
            const value = formData.get(field);
            if (!value || value.trim() === '') {
                hasError = true;
                const input = form.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('border-red-500');
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'text-red-500 text-xs mt-1';
                    errorMsg.textContent = '此字段为必填项';
                    const parent = input.parentElement;
                    const existingError = parent.querySelector('.text-red-500');
                    if (existingError) {
                        parent.removeChild(existingError);
                    }
                    parent.appendChild(errorMsg);
                }
            }
        });

        if (hasError) {
            notification.warning('请填写所有必填字段');
            return;
        }

        // 处理包装尺寸和重量的数值转换
        const numberFields = ['package_length', 'package_width', 'package_height', 'package_weight'];
        numberFields.forEach(field => {
            const value = formData.get(field);
            if (value) {
                data[field] = parseFloat(value);
            }
        });
        
        // 收集所有表单数据
        formData.forEach((value, key) => {
            if (!numberFields.includes(key)) {
                data[key] = value;
            }
        });

        // 发送更新请求
        fetch(`/api/product/edit/${productId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => {
                    throw new Error(err.message || '更新失败');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            notification.success('产品更新成功');
            toggleAddProductModal();
            // 刷新产品列表
            loadProducts();
        })
        .catch(error => {
            console.error('Error:', error);
            notification.error(error.message || '更新失败，请重试');
        });
    }

    // 删除产品
    async function deleteProduct(productId) {
        const confirmed = await notification.confirm('确定要删除这个产品吗？此操作不可恢复。', '删除确认');
        if (!confirmed) {
            return;
        }

        const loading = notification.showLoading('正在删除...');
        
        try {
            const response = await fetch(`/api/product/delete/${productId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const err = await response.json();
                throw new Error(err.message || '删除失败');
            }

            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }

            loading.remove();
            notification.success('产品删除成功');
            // 刷新产品列表
            loadProducts();
        } catch (error) {
            console.error('Error:', error);
            loading.remove();
            notification.error(error.message || '删除失败，请重试');
        }
    }

    // 修改操作菜单项点击事件，阻止事件冒泡
    function handleOperationClick(operation, productId) {
        event.stopPropagation();  // 阻止事件冒泡
        switch(operation) {
            case 'edit':
                editProduct(productId);
                break;
            case 'delete':
                deleteProduct(productId);
                break;
            case 'note':
                // 备注功能可以后续实现
                break;
        }
    }

    // 更新loadProducts函数中的操作菜单HTML
    function getOperationMenuHTML(productId) {
        return `
            <div class="operation-dropdown">
                <button class="hover:text-blue-900" onclick="toggleOperationMenu(this)">
                    <i class="fas fa-chevron-down ml-1"></i>
                </button>
                <div class="operation-menu">
                    <div class="operation-menu-item" onclick="handleOperationClick('edit', ${productId})">
                        <i class="fas fa-edit mr-2"></i>编辑
                    </div>
                    <div class="operation-menu-item" onclick="handleOperationClick('note', ${productId})">
                        <i class="fas fa-edit mr-2"></i>备注
                    </div>
                    <div class="operation-menu-item text-red-600" onclick="handleOperationClick('delete', ${productId})">
                        <i class="fas fa-trash-alt mr-2"></i>删除
                    </div>
                </div>
            </div>
        `;
    }

    // 修改loadProducts函数中的操作列渲染
    // 在loadProducts函数中找到操作列的td，替换其内容为：
    // <td class="px-6 py-4">
    //     ${getOperationMenuHTML(product.id)}
    // </td>

    // 获取选中的单个产品ID
    function getSelectedProductId() {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        if (checkboxes.length === 1) {
            const row = checkboxes[0].closest('tr');
            if (row) {
                const idText = row.querySelector('.text-xs.text-gray-500').textContent;
                const match = idText.match(/产品ID: (\d+)/);
                if (match && match[1]) {
                    return parseInt(match[1]);
                }
            }
        } else if (checkboxes.length === 0) {
            notification.warning('请选择一个产品进行编辑');
            return null;
        } else {
            notification.warning('只能选择一个产品进行编辑');
            return null;
        }
    }

    // 处理单个编辑
    function handleSingleEdit() {
        const productId = getSelectedProductId();
        if (productId) {
            editProduct(productId);
        }
    }

    // 处理单个删除
    function handleSingleDelete() {
        const productId = getSelectedProductId();
        if (productId) {
            deleteProduct(productId);
        }
    }

    // 全选/取消全选功能
    function toggleSelectAll(checkbox) {
        if (!checkbox) return;
        const tbody = document.querySelector('.product-table tbody');
        if (!tbody) return;
        
        const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(item => {
            if (item && !item.disabled) {
                item.checked = checkbox.checked;
            }
        });
    }

    // 更新表头复选框状态
    function updateHeaderCheckbox() {
        const tbody = document.querySelector('.product-table tbody');
        const headerCheckbox = document.querySelector('thead input[type="checkbox"]');
        
        if (!tbody || !headerCheckbox) return;
        
        const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
        const checkedBoxes = tbody.querySelectorAll('input[type="checkbox"]:checked');
        
        if (checkboxes.length === 0) {
            headerCheckbox.checked = false;
            headerCheckbox.indeterminate = false;
        } else if (checkedBoxes.length === 0) {
            headerCheckbox.checked = false;
            headerCheckbox.indeterminate = false;
        } else if (checkedBoxes.length === checkboxes.length) {
            headerCheckbox.checked = true;
            headerCheckbox.indeterminate = false;
        } else {
            headerCheckbox.checked = false;
            headerCheckbox.indeterminate = true;
        }
    }

    // 在加载产品后重置复选框状态
    function resetCheckboxStates() {
        const headerCheckbox = document.querySelector('thead input[type="checkbox"]');
        if (headerCheckbox) {
            headerCheckbox.checked = false;
            headerCheckbox.indeterminate = false;
        }
    }

    // 添加翻译相关的JavaScript函数
    // 翻译字段
    async function translateField(fieldType, sourceLang, productId, text) {
        if (!text) {
            alert('没有可翻译的内容');
            return;
        }

        try {
            const response = await fetch('/api/product/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: text,
                    source_lang: sourceLang,
                    target_langs: ['lt', 'lv', 'et', 'fi'],
                    content_type: fieldType
                })
            });

            if (!response.ok) {
                throw new Error('翻译请求失败');
            }

            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }

            // 准备更新数据
            const updateData = {};
            if (fieldType === 'title') {
                updateData.title_lt = data.translations.lt;
                updateData.title_lv = data.translations.lv;
                updateData.title_et = data.translations.et;
                updateData.title_fi = data.translations.fi;
            } else if (fieldType === 'description') {
                updateData.description_lt = data.translations.lt;
                updateData.description_lv = data.translations.lv;
                updateData.description_et = data.translations.et;
                updateData.description_fi = data.translations.fi;
            }

            // 更新产品信息
            const updateResponse = await fetch(`/api/product/edit/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });

            if (!updateResponse.ok) {
                throw new Error('更新产品信息失败');
            }

            const updateResult = await updateResponse.json();
            if (updateResult.error) {
                throw new Error(updateResult.error);
            }

            alert('翻译并更新成功');
            loadProducts(); // 刷新产品列表

        } catch (error) {
            console.error('Error:', error);
            alert(error.message || '翻译失败，请重试');
        }
    }

    
    // 处理模态框中的翻译  单个翻译
    async function translateModalField(fieldType, sourceLang) {
        const translationEnabled = await checkTranslationEnabled();
        if (!translationEnabled) {
            notification.warning('翻译功能已禁用');
            return;
        }
        
        const form = document.getElementById('productForm');
        let text = '';
        
        // 获取要翻译的文本
        if (fieldType === 'title') {
            text = form.querySelector(`[name="${sourceLang === 'en' ? 'title_en' : 'title_cn'}"]`).value;
        } else if (fieldType === 'description') {
            text = form.querySelector('[name="description_en"]').value;
        }

        if (!text.trim()) {
            notification.warning('请先输入需要翻译的内容');
            return;
        }

        // 显示翻译中的loading提示
        const loading = notification.showLoading('翻译中...');
        
        try {
            const response = await fetch('/api/product/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: text,
                    source_lang: sourceLang.toUpperCase(),
                    target_langs: ['lt', 'lv', 'et', 'fi'],
                    content_type: fieldType
                })
            });

            if (!response.ok) {
                throw new Error('翻译请求失败');
            }

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error || '翻译失败');
            }

            // 更新表单中的翻译字段
            if (fieldType === 'title') {
                form.querySelector('[name="title_lt"]').value = data.translations.lt || '';
                form.querySelector('[name="title_lv"]').value = data.translations.lv || '';
                form.querySelector('[name="title_et"]').value = data.translations.et || '';
                form.querySelector('[name="title_fi"]').value = data.translations.fi || '';
            } else if (fieldType === 'description') {
                form.querySelector('[name="description_lt"]').value = data.translations.lt || '';
                form.querySelector('[name="description_lv"]').value = data.translations.lv || '';
                form.querySelector('[name="description_et"]').value = data.translations.et || '';
                form.querySelector('[name="description_fi"]').value = data.translations.fi || '';
            }

            // 移除loading提示并显示成功消息
            loading.remove();
            notification.success('翻译成功');

        } catch (error) {
            console.error('Error:', error);
            // 移除loading提示并显示错误消息
            loading.remove();
            notification.error(error.message || '翻译失败，请重试');
        }
    }

    // Excel模态框控制
    function toggleExcelModal() {
        const modal = document.getElementById('excelModal');
        const isClosing = !modal.classList.contains('hidden');
        
        modal.classList.toggle('hidden');
        
        // 如果是关闭模态框，且不是因为出错导致的关闭，则刷新产品列表
        if (isClosing) {
            loadProducts();
        }
    }

    // 重置表单
    function resetExcelForm() {
        document.getElementById('importStoreSelect').value = '';
        document.getElementById('file-upload').value = '';
        const filePreview = document.getElementById('file-preview');
        if (filePreview) {
            filePreview.classList.add('hidden');
        }
    }

    // 文件上传处理
    document.getElementById('file-upload').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            if (isValidFileType(file)) {
                showFilePreview(file);
            } else {
                alert('请上传正确的文件格式（.xlsx, .xls, .csv）');
                this.value = '';
            }
        }
    });

    // 拖放处理
    const dropZone = document.querySelector('.border-dashed');
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('border-blue-500');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('border-blue-500');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('border-blue-500');
        const file = e.dataTransfer.files[0];
        if (file && isValidFileType(file)) {
            document.getElementById('file-upload').files = e.dataTransfer.files;
            showFilePreview(file);
        } else {
            alert('请上传正确的文件格式（.xlsx, .xls, .csv）');
        }
    });

    // 显示文件预览
    function showFilePreview(file) {
        const preview = document.getElementById('file-preview');
        const fileName = document.getElementById('file-name');
        preview.classList.remove('hidden');
        fileName.textContent = file.name;
    }

    // 移除文件
    function removeFile() {
        document.getElementById('file-upload').value = '';
        document.getElementById('file-preview').classList.add('hidden');
    }

    // 验证文件类型
    function isValidFileType(file) {
        const validTypes = ['.xlsx', '.xls', '.csv'];
        const fileName = file.name.toLowerCase();
        return validTypes.some(type => fileName.endsWith(type));
    }

    // 导入产品
    async function importProducts() {
        const storeId = document.getElementById('importStoreSelect').value;
        const fileInput = document.getElementById('file-upload');
        const file = fileInput.files[0];

        if (!storeId) {
            notification.warning('请选择店铺');
            return;
        }

        if (!file) {
            notification.warning('请选择要导入的文件');
            return;
        }

        // 显示加载状态
        const importButton = document.querySelector('button[onclick="importProducts()"]');
        const originalText = importButton.innerHTML;
        importButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>导入中...';
        importButton.disabled = true;

        const loading = notification.showLoading('正在导入数据...');

        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('store_id', storeId);

            const response = await fetch('/api/product/import', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            // 显示导入结果
            loading.remove();
            let message = `导入完成\n成功: ${data.success_count} 条\n失败: ${data.error_count} 条`;
            if (data.errors && data.errors.length > 0) {
                message += '\n\n错误详情:\n' + data.errors.join('\n');
            }
            notification.success(message);

            // 重置表单并关闭模态框
            resetExcelForm();
            toggleExcelModal();

        } catch (error) {
            console.error('Error:', error);
            loading.remove();
            notification.error(error.message || '导入失败，请重试');
        } finally {
            // 恢复按钮状态
            importButton.innerHTML = originalText;
            importButton.disabled = false;
        }
    }

    // 导出产品
    function exportProducts() {
        const storeId = document.getElementById('exportStoreSelect').value;
        if (!storeId) {
            notification.warning('请选择店铺');
            return;
        }

        const exportAll = document.querySelector('input[name="exportType"][value="all"]').checked;
        const selectedIds = exportAll ? [] : getSelectedProductIds();

        if (!exportAll && (!selectedIds || selectedIds.length === 0)) {
            notification.warning('请选择要导出的产品');
            return;
        }

        // 构建URL
        let url = `/api/product/export?store_id=${storeId}&export_all=${exportAll}`;
        if (!exportAll && selectedIds.length > 0) {
            url += `&selected_ids[]=${selectedIds.join('&selected_ids[]=')}`;
        }

        // 发送请求检查是否有产品可导出
        fetch(url)
            .then(response => {
                // 检查响应的Content-Type
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    // 如果是JSON响应，说明可能是错误信息
                    return response.json().then(data => {
                        if (data.error) {
                            if (data.error === '没有找到任何产品' || data.error === '没有有效的数据可以导出') {
                                notification.warning('当前没有可导出的产品');
                                return;
                            }
                            throw new Error(data.error);
                        }
                    });
                } else {
                    // 如果不是JSON，说明是文件流，直接下载
                    window.location.href = url;
                    notification.success('导出任务已开始，请稍候...');
                }
            })
            .catch(error => {
                notification.error(error.message || '导出失败，请重试');
            });
    }

    // 下载模板
    function downloadTemplate() {
        // 显示加载提示
        const templateButton = document.querySelector('button[onclick="downloadTemplate()"]');
        const originalText = templateButton.innerHTML;
        templateButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>下载中...';
        templateButton.disabled = true;

        // 下载模板文件
        window.location.href = '/api/product/template/download';

        // 延迟恢复按钮状态
        setTimeout(() => {
            templateButton.innerHTML = originalText;
            templateButton.disabled = false;
        }, 3000);
    }

    // 修改工具栏按钮事件监听
    document.addEventListener('DOMContentLoaded', function() {
        // 添加Excel导入按钮事件监听
        const excelButton = document.querySelector('button.toolbar-button:nth-child(2)');
        if (excelButton) {
            excelButton.onclick = function() {
                toggleExcelModal();
            };
        }

        // 删除按钮事件监听
        const deleteButton = document.querySelector('button.toolbar-button i.fa-trash-alt').closest('button');
        if (deleteButton) {
            deleteButton.onclick = function() {
                handleBatchDelete();
            };
        }
    });

    // 导出相关的JavaScript代码
    document.getElementById('startExportBtn').addEventListener('click', function() {
        const storeId = document.getElementById('exportStoreSelect').value;
        if (!storeId) {
            showError('请选择店铺');
            return;
        }

        const exportAll = document.querySelector('input[name="exportType"][value="all"]').checked;
        const selectedIds = exportAll ? [] : getSelectedProductIds();

        if (!exportAll && (!selectedIds || selectedIds.length === 0)) {
            showError('请选择要导出的产品');
            return;
        }

        // 构建URL
        let url = `/api/product/export?store_id=${storeId}&export_all=${exportAll}`;
        if (!exportAll && selectedIds.length > 0) {
            url += `&selected_ids[]=${selectedIds.join('&selected_ids[]=')}`;
        }

        // 下载文件
        window.location.href = url;
    });

    // 获取所有选中的产品ID
    function getSelectedProductIds() {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        const selectedIds = [];

        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row) {
                const idText = row.querySelector('.text-xs.text-gray-500').textContent;
                const match = idText.match(/产品ID: (\d+)/);
                if (match && match[1]) {
                    selectedIds.push(parseInt(match[1]));
                }
            }
        });

        return selectedIds;
    }

    // 处理批量删除
    async function handleBatchDelete() {
        const selectedIds = getSelectedProductIds();
        
        if (selectedIds.length === 0) {
            notification.warning('请至少选择一个产品');
            return;
        }

        const confirmed = await notification.confirm(
            `确定要删除选中的 ${selectedIds.length} 个产品吗？此操作不可恢复。`,
            '批量删除确认'
        );
        
        if (!confirmed) {
            return;
        }

        const loading = notification.showLoading('正在删除...');

        try {
            const response = await fetch('/api/product/batch-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_ids: selectedIds
                })
            });

            if (!response.ok) {
                throw new Error('删除请求失败');
            }

            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }

            loading.remove();
            notification.success('删除成功');
            loadProducts(); // 刷新产品列表

        } catch (error) {
            console.error('Error:', error);
            loading.remove();
            notification.error(error.message || '删除失败，请重试');
        }
    }

    // 修改页面标题显示
    document.addEventListener('DOMContentLoaded', function() {
        // 移除所有显示URL的元素
        const urlDisplays = document.querySelectorAll('.url-display');
        urlDisplays.forEach(element => {
            element.remove();
        });

        // 修改页面标题
        document.title = document.title.replace('127.0.0.1:5000', '');

        // 修改所有显示URL的地方
        const urlElements = document.querySelectorAll('.show-url');
        urlElements.forEach(element => {
            element.textContent = element.textContent.replace('127.0.0.1:5000', '');
        });
    });

    // 修改确认对话框的显示
    async function showConfirmDialog(message) {
        return await notification.confirm(message);
    }

    // 修改提示对话框的显示
    function showAlert(message) {
        notification.warning(message);
    }

    // 修改错误提示的显示
    function showError(message) {
        notification.error(message);
    }

    // 修改成功提示的显示
    function showSuccess(message) {
        notification.success(message);
    }

    // 关闭确认对话框
    function closeConfirmDialog(result) {
        const confirmDialog = document.getElementById('confirmDialog');
        confirmDialog.classList.add('hidden');
        if (result) {
            notification.success('操作成功');
        }
    }

    // 关闭提示对话框
    function closeAlertDialog() {
        const alertDialog = document.getElementById('alertDialog');
        alertDialog.classList.add('hidden');
        notification.info('操作已完成');
    }

    // 打开发布成功任务查看模态框
    function togglePublishSuccessModal() {
        const modal = document.getElementById('publishSuccessModal');
        modal.classList.toggle('hidden');
    }

    // 打开XML转换查看模态框
    function toggleXmlConvertModal() {
        const modal = document.getElementById('xmlConvertModal');
        modal.classList.toggle('hidden');

    }

    // 编辑选中的产品
    function editSelectedProduct() {
        const productId = getSelectedProductId();
        if (productId) {
            editProduct(productId);
        }
    }

    // 修改描述预览函数
    function showDescriptionPreview(element) {
        const content = element.getAttribute('data-description');
        if (!content) return;

        const preview = document.createElement('div');
        preview.className = 'description-preview';
        preview.innerHTML = content;
        
        const rect = element.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 计算最佳显示位置
        let left = rect.right + 5;
        let top = rect.top;
        
        // 如果预览框会超出右边界，则显示在左边
        if (left + 300 > viewportWidth) {
            left = rect.left - 305;
        }
        
        // 如果预览框会超出底部边界，则向上调整
        if (top + 200 > viewportHeight) {
            top = viewportHeight - 210;
        }
        
        preview.style.left = `${left}px`;
        preview.style.top = `${top}px`;
        preview.style.display = 'block';
        
        document.body.appendChild(preview);
    }

    function hideDescriptionPreview() {
        const previews = document.querySelectorAll('.description-preview');
        previews.forEach(preview => preview.remove());
    }

    // 修改HTML转义函数
    function escapeHtml(unsafe) {
        if (!unsafe) return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 修改批量操作按钮为下拉菜单
    function toggleBatchOperations(button) {
        const menu = document.getElementById('batchOperationsMenu');
        menu.classList.toggle('hidden');
        
        // 点击其他地方关闭菜单
        document.addEventListener('click', function closeMenu(e) {
            if (!button.contains(e.target) && !menu.contains(e.target)) {
                menu.classList.add('hidden');
                document.removeEventListener('click', closeMenu);
            }
        });
    }

    // 处理批量翻译  批量翻译
    async function handleBatchTranslate() {
        const translationSettings = await checkTranslationEnabled();
        if (!translationSettings.translation_enabled) {
            notification.warning('翻译功能已禁用');
            return;
        }
        
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        if (checkboxes.length === 0) {
            notification.warning('请选择要翻译的产品');
            return;
        }

        const confirmed = await notification.confirm(
            `确定要翻译选中的 ${checkboxes.length} 个产品吗？`,
            '批量翻译确认'
        );
        
        if (!confirmed) {
            return;
        }

        // 显示进度条
        const progressModal = document.getElementById('progressModal');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        progressModal.classList.remove('hidden');
        progressModal.classList.add('flex');

        // 收集选中的SKU
        const selectedProducts = [];
        checkboxes.forEach(checkbox => {
            if (checkbox.value !== 'all') {
                const row = checkbox.closest('tr');
                const skuElement = row.querySelector('td:nth-child(6) .truncate-1');
                if (skuElement) {
                    selectedProducts.push({
                        sku: skuElement.textContent.trim()
                    });
                }
            }
        });

        const totalCount = selectedProducts.length;
        let completedCount = 0;
        
        // 初始化进度显示
        progressBar.style.width = '0%';
        progressText.textContent = `0/${totalCount}`;

        try {
            
            const translationService = translationSettings.translation_service;
            const isDeepSeekHuoshan = translationService === 'deepseekhuoshan';
            const isMTranServer = translationService === 'mtranserver' || translationService === 'mtran';
            const isdeepseekmtserver = translationService === 'deepseekmtserver';
            
            // 支持批量翻译的服务: DeepSeekHuoshan、MTranServer和DeepSeekMTranServer
            if (isDeepSeekHuoshan || isMTranServer || isdeepseekmtserver) {
                // 一次性发送所有产品
                const response = await fetch('/api/product/batch-translate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        products: selectedProducts
                    })
                });

                const result = await response.json();
                if (!response.ok) {
                    throw new Error(result.error || '翻译请求失败');
                }

                completedCount = totalCount;
                progressBar.style.width = '100%';
                progressText.textContent = `${completedCount}/${totalCount}`;

                if (result.success) {
                    notification.success(`翻译完成\n成功: ${result.success_count} 个\n失败: ${result.failed_count} 个`);
                    if (result.errors && result.errors.length > 0) {
                        notification.error('失败详情:\n' + result.errors.join('\n'));
                    }
                } else {
                    throw new Error(result.error || '翻译失败');
                }
            } else {
                // 其他翻译服务: 逐个发送请求
                for (const product of selectedProducts) {
                    const response = await fetch('/api/product/batch-translate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            products: [product]
                        })
                    });

                    if (!response.ok) {
                        throw new Error('翻译请求失败');
                    }

                    // 更新进度
                    completedCount++;
                    const percentage = (completedCount / totalCount) * 100;
                    progressBar.style.width = `${percentage}%`;
                    progressText.textContent = `${completedCount}/${totalCount}`;
                }
            }
            
            // 刷新产品列表
            loadProducts();

        } catch (error) {
            console.error('Error:', error);
            notification.error(error.message || '翻译失败，请重试');
        } finally {
            // 隐藏进度条
            progressModal.classList.add('hidden');
            progressModal.classList.remove('flex');
        }
    }

    // 处理批量查询EAN上架情况
    async function handleBatchInquire() {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        if (checkboxes.length === 0) {
            notification.warning('请选择要查询的产品');
            return;
        }

        // 获取第一个选中产品的店铺ID和所有选中产品的EAN
        let seller_id = null;
        const eans = [];
        
        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row && checkbox.value !== 'all') {
                // 获取店铺ID（从第一个产品）
                if (!seller_id) {
                    const storeInfo = row.querySelector('td:nth-child(3) .text-xs.text-gray-500:nth-child(3)');
                    if (storeInfo) {
                        const storeMatch = storeInfo.textContent.match(/店铺: (.*)/);
                        if (storeMatch && storeMatch[1]) {
                            // 从店铺下拉列表中找到对应的seller_id
                            const storeSelect = document.getElementById('store-selector');
                            const storeOption = Array.from(storeSelect.options).find(option => option.text === storeMatch[1]);
                            if (storeOption) {
                                seller_id = storeOption.value;
                            }
                        }
                    }
                }
                
                // 获取EAN
                const skuCell = row.querySelector('td:nth-child(6)');
                if (skuCell) {
                    const eanText = skuCell.querySelector('.text-xs.text-gray-500').textContent;
                    const ean = eanText.replace('-', '').trim();
                    if (ean) {
                        eans.push(ean);
                    }
                }
            }
        });

        if (!seller_id) {
            notification.warning('无法获取店铺信息');
            return;
        }

        if (eans.length === 0) {
            notification.warning('选中的产品中没有有效的EAN码');
            return;
        }

        const confirmed = await notification.confirm(
            `确定要查询选中的 ${eans.length} 个产品的上架状态吗？`,
            '批量查询确认'
        );
        
        if (!confirmed) {
            return;
        }

        const loading = notification.showLoading('正在查询上架状态...');

        try {
            const response = await fetch('/api/product/batch-inquire', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    seller_id: seller_id,
                    eans: eans
                })
            });

            const result = await response.json();
            
            loading.remove();
            
            if (result.success) {
                notification.success(`查询完成\n成功上架数量: ${result.success_count} 个`);
                // 刷新产品列表以显示更新后的状态
                loadProducts();
            } else {
                throw new Error(result.error || '查询失败');
            }
        } catch (error) {
            console.error('Error:', error);
            loading.remove();
            notification.error(error.message || '查询失败，请重试');
        }
    }

        


    // 更新进度条
    function updateProgress(completed, total) {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const percentage = (completed / total) * 100;
        progressBar.style.width = `${percentage}%`;
        progressText.textContent = `${completed}/${total}`;
    }

    // 在页面加载时检查翻译状态
    document.addEventListener('DOMContentLoaded', function() {
        checkTranslationEnabled();
    });

    // 检查翻译功能是否启用并获取翻译设置
    async function checkTranslationEnabled() {
        try {
            const response = await fetch('/api/settings/translation');
            const data = await response.json();
            
            // 获取所有翻译相关按钮
            const translateButtons = document.querySelectorAll('[onclick*="translate"]');
            
            // 根据启用状态设置按钮
            translateButtons.forEach(button => {
                button.disabled = !data.translation_enabled;
                if (!data.translation_enabled) {
                    button.title = '翻译功能已禁用';
                    button.classList.add('opacity-50', 'cursor-not-allowed');
                } else {
                    button.title = '';
                    button.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            });
            
            return data;  // 返回完整的翻译设置数据
        } catch (error) {
            console.error('检查翻译设置失败:', error);
            return {
                translation_enabled: false,
                translation_service: null
            };
        }
    }

    // 处理批量修改
    function handleBatchModify() {
        toggleBatchModifyModal();
    }

    // 批量修改模态框控制
    function toggleBatchModifyModal() {
        const modal = document.getElementById('batchModifyModal');
        if (modal.classList.contains('hidden')) {
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        } else {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    }

    // 批量修改
    async function executeBatchModify() {
        const modifyCategory = document.getElementById('modify-category').checked;
        const modifySku = document.getElementById('modify-sku-prefix').checked;
        
        // 获取选中的产品IDs
        const selectedIds = getSelectedProductIds();
        if (selectedIds.length === 0) {
            notification.warning('请选择要修改的产品');
            return;
        }
        
        // 验证至少选择了一项要修改的内容
        if (!modifyCategory && !modifySku) {
            notification.warning('请至少选择一项要修改的内容');
            return;
        }
        
        // 验证输入
        let categoryId = null;
        let oldPrefix = null;
        let newPrefix = null;
        
        if (modifyCategory) {
            categoryId = document.getElementById('category-id-input').value.trim();
            if (!categoryId) {
                notification.warning('请输入有效的分类ID');
                return;
            }
        }
        
        if (modifySku) {
            oldPrefix = document.getElementById('old-prefix-input').value.trim();
            newPrefix = document.getElementById('new-prefix-input').value.trim();
            if (!oldPrefix || !newPrefix) {
                notification.warning('请输入有效的SKU前缀');
                return;
            }
        }

        // 显示确认对话框
        const confirmMessage = [];
        if (modifyCategory) {
            confirmMessage.push(`修改 ${selectedIds.length} 个产品的分类ID为: ${categoryId}`);
        }
        if (modifySku) {
            confirmMessage.push(`将 ${selectedIds.length} 个产品的SKU前缀从 "${oldPrefix}" 修改为 "${newPrefix}"`);
        }
        
        const confirmed = await notification.confirm(
            `确定要进行以下操作吗？\n${confirmMessage.join('\n')}\n\n此操作不可恢复。`,
            '批量修改确认'
        );
        
        if (!confirmed) {
            return;
        }

        const loading = notification.showLoading('正在修改...');

        try {
            const response = await fetch('/api/product/batch-modify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_ids: selectedIds,
                    category_id: modifyCategory ? categoryId : null,
                    sku_change: modifySku ? { old_prefix: oldPrefix, new_prefix: newPrefix } : null
                })
            });

            if (!response.ok) {
                throw new Error('修改请求失败');
            }

            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }

            loading.remove();
            notification.success(`成功修改了 ${data.updated_count || selectedIds.length} 个产品`);
            toggleBatchModifyModal();
            loadProducts(); // 刷新产品列表

        } catch (error) {
            console.error('Error:', error);
            loading.remove();
            notification.error(error.message || '修改失败，请重试');
        }
    }

    // 同步更新产品功能
    async function handleProductUpdate() {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        if (checkboxes.length === 0) {
            notification.warning('请选择要更新的产品');
            return;
        }

        const selectedIds = Array.from(checkboxes)
            .filter(checkbox => checkbox.value !== 'all')
            .map(checkbox => parseInt(checkbox.value));

        let confirmMessage = '';
        if (selectedIds.length === 1) {
            confirmMessage = '确定要更新选中的产品吗？';
        } else {
            confirmMessage = `确定要更新选中的 ${selectedIds.length} 个产品吗？`;
        }

        const confirmed = await notification.confirm(
            confirmMessage,
            '同步更新确认'
        );
        
        if (!confirmed) {
            return;
        }

        const loading = notification.showLoading('正在更新产品...');

        try {
            const response = await fetch('/api/product/batch-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    product_ids: selectedIds
                })
            });

            const result = await response.json();
            
            loading.remove();
            
            if (result.success) {
                notification.success(`更新完成\n成功: ${result.success_count} 个\n失败: ${result.failed_count} 个`);
                // 如果有错误信息，显示详情
                if (result.errors && result.errors.length > 0) {
                    // 限制错误信息的长度，避免弹窗太大
                    const limitedErrors = result.errors.length > 10 
                        ? result.errors.slice(0, 10).concat([`... 等共 ${result.errors.length} 条错误`]) 
                        : result.errors;
                    notification.error('失败详情:\n' + limitedErrors.join('\n'));
                }
                // 刷新产品列表
                loadProducts();
            } else {
                throw new Error(result.error || '更新失败');
            }
        } catch (error) {
            console.error('更新产品时出错:', error);
            loading.remove();
            notification.error(error.message || '更新失败，请重试');
        }
    }

    // 批量创建产品功能
    async function handleBatchCreate() {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        if (checkboxes.length === 0) {
            notification.warning('请选择要创建的产品');
            return;
        }

        const selectedIds = Array.from(checkboxes)
            .filter(checkbox => checkbox.value !== 'all')
            .map(checkbox => parseInt(checkbox.value));

        let confirmMessage = '';
        if (selectedIds.length === 1) {
            confirmMessage = '确定要创建选中的产品吗？';
        } else {
            confirmMessage = `确定要创建选中的 ${selectedIds.length} 个产品吗？`;
        }

        const confirmed = await notification.confirm(
            confirmMessage,
            '批量创建确认'
        );

        if (!confirmed) {
            return;
        }

        const loading = notification.showLoading('正在创建产品...');

        try {
            const response = await fetch('/api/product/batch-create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    product_ids: selectedIds
                })
            });

            const result = await response.json();
            
            loading.remove();
            
            if (result.success) {
                notification.success(`创建完成\n成功: ${result.success_count} 个\n失败: ${result.failed_count} 个`);
                // 如果有错误信息，显示详情
                if (result.errors && result.errors.length > 0) {
                    // 限制错误信息的长度，避免弹窗太大
                    const limitedErrors = result.errors.length > 10 
                        ? result.errors.slice(0, 10).concat([`... 等共 ${result.errors.length} 条错误`]) 
                        : result.errors;
                    notification.error('失败详情:\n' + limitedErrors.join('\n'));
                }
                // 刷新产品列表
                loadProducts();
            } else {
                throw new Error(result.error || '创建失败');
            }
        } catch (error) {
            console.error('创建产品时出错:', error);
            loading.remove();
            notification.error(error.message || '创建失败，请重试');
        }
    }
                        
</script>
{% endblock %} 