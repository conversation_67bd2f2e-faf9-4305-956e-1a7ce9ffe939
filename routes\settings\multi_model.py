from flask import Blueprint, jsonify, request
from models import db, MultiModelConfig

bp = Blueprint('multi_model', __name__)

@bp.route('/api/settings/multi-model', methods=['GET'])
def get_multi_model_config():
    """获取多模型配置"""
    try:
        models = MultiModelConfig.query.all()
        enabled = any(model.enabled for model in models)
        
        return jsonify({
            'enabled': enabled,
            'models': [{
                'id': model.id,
                'model_name': model.model_name,
                'model_id': model.model_id,
                'api_key': model.api_key,
                'token_limit': model.token_limit,
                'used_tokens': model.used_tokens,
                'is_active': model.is_active
            } for model in models]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/settings/multi-model/stats', methods=['GET'])
def get_multi_model_stats():
    """获取多模型使用统计"""
    try:
        models = MultiModelConfig.query.all()
        stats = [{
            'model_name': model.model_name,
            'model_id': model.model_id,
            'used_tokens': model.used_tokens,
            'token_limit': model.token_limit,
            'is_active': model.is_active
        } for model in models]
        
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/settings/multi-model/toggle', methods=['POST'])
def toggle_multi_model():
    """切换多模型功能"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', False)
        
        # 更新所有模型的启用状态
        models = MultiModelConfig.query.all()
        for model in models:
            model.enabled = enabled
        
        db.session.commit()
        
        return jsonify({'success': True, 'enabled': enabled})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/settings/multi-model/delete/<int:model_id>', methods=['DELETE'])
def delete_model(model_id):
    """删除指定的模型配置"""
    try:
        model = MultiModelConfig.query.get(model_id)
        if not model:
            return jsonify({'error': '模型不存在'}), 404
            
        db.session.delete(model)
        db.session.commit()
        
        return jsonify({'success': True, 'message': '模型已删除'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/api/settings/multi-model/reset-tokens/<int:model_id>', methods=['POST'])
def reset_model_tokens(model_id):
    """重置指定模型的token使用量"""
    try:
        model = MultiModelConfig.query.get(model_id)
        if not model:
            return jsonify({'error': '模型不存在'}), 404
            
        model.used_tokens = 0
        model.is_active = True
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Token使用量已重置',
            'model': {
                'id': model.id,
                'used_tokens': model.used_tokens,
                'is_active': model.is_active
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500 