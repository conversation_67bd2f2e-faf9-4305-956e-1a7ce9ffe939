{% extends "base.html" %}

{% block styles %}
<style>
    /* 基础卡片布局 */
    .dashboard-card {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 2rem);
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }

    /* 固定顶部区域 */
    .fixed-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem;
        background: white;
    }

    /* 可滚动内容区域 */
    .scrollable-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
        width: 100%;
    }

    /* 基础表格样式 */
    .base-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        table-layout: fixed;
    }

    .base-table thead {
        background-color: #f9fafb;
    }

    .base-table th {
        padding: 0.75rem 1.5rem;
        text-align: left;
        font-size: 0.75rem;
        font-weight: 500;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 1px solid #e5e7eb;
        white-space: nowrap;
    }

    .base-table td {
        padding: 1rem 1.5rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
        font-size: 0.875rem;
        color: #374151;
    }

    .base-table tr:hover {
        background-color: #f9fafb;
    }

    /* 状态标签基础样式 */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* 操作按钮基础样式 */
    .action-button {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        background: transparent;
    }

    .action-button:hover {
        background-color: #f3f4f6;
    }

    /* 顶部按钮基础样式 */
    .header-button {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }

    .header-button.primary {
        background-color: #3b82f6;
        color: white;
    }

    .header-button.primary:hover {
        background-color: #2563eb;
    }

    /* 模态框基础样式 */
    .modal {
        display: none;
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 50;
    }

    .modal.show {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
    }

    /* 表单基础样式 */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        color: #374151;
    }

    .form-select, .form-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        color: #374151;
        background-color: white;
    }

    .form-select:focus, .form-input:focus {
        outline: none;
        ring: 2px;
        ring-color: #3b82f6;
        border-color: #3b82f6;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-card">
    <!-- 固定顶部区域 -->
    <div class="fixed-header">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">{% block page_title %}{% endblock %}</h2>
            {% block header_buttons %}{% endblock %}
        </div>
        {% block header_content %}{% endblock %}
    </div>

    <!-- 可滚动的内容区域 -->
    <div class="scrollable-content">
        {% block main_content %}{% endblock %}
    </div>
</div>

<!-- 模态框区域 -->
{% block modals %}{% endblock %}
{% endblock %}

{% block scripts %}
<!-- 基础JavaScript功能可以在这里添加 -->
{% block page_scripts %}{% endblock %}
{% endblock %} 