<!-- 刊登任务查看模态框 -->
<div id="publishSuccessModal" class="fixed inset-0 z-40 hidden">
    <!-- 背景遮罩层 -->
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <!-- 容器 -->
    <div class="fixed inset-0 flex items-center justify-center" style="padding: 1rem;">
        <!-- 内容卡片 - 使用固定像素值和最小尺寸 -->
        <div class="bg-white rounded-lg shadow-xl flex flex-col overflow-hidden" 
             style="width: 1200px; height: 800px; min-width: 1200px; min-height: 800px;">
            <!-- 头部 -->
            <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
                <h2 class="text-base font-medium text-gray-900">刊登任务查看</h2>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="publishSuccessModal.toggle()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 筛选区域 -->
            <div class="p-3 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <!-- 店铺筛选 -->
                    <div class="flex-1">
                        <select class="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="storeFilter">
                            <!-- <option value="">所有店铺</option>
                            <option value="2">2号店</option>
                            <option value="4">10好点</option>
                            <option value="5">6好点</option>
                            <option value="6">测试店铺1</option>
                            <option value="7">测试店铺2</option>
                            <option value="8">测试店铺3</option> -->
                        </select>
                    </div>
                    <!-- 时间范围 -->
                    <div class="flex-1 flex space-x-2">
                        <input type="date" class="flex-1 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="dateStart">
                        <span class="text-gray-500 text-sm">至</span>
                        <input type="date" class="flex-1 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="dateEnd">
                    </div>
                    <!-- 状态筛选 -->
                    <div class="flex-1">
                        <select class="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="statusFilter">
                            <option value="">所有状态</option>
                            <option value="valid">有效</option>
                            <option value="expired">已过期</option>
                        </select>
                    </div>
                    <!-- 搜索按钮 -->
                    <button type="button" class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700" onclick="publishSuccessModal.handleSearch()">
                        <i class="fas fa-search mr-1"></i>搜索
                    </button>
                    <!-- 重置按钮 -->
                    <button type="button" class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50" onclick="publishSuccessModal.handleReset()">
                        <i class="fas fa-redo mr-1"></i>重置
                    </button>
                </div>
            </div>

            <!-- 表格区域 -->
            <div class="flex-1 overflow-y-auto overflow-x-hidden">
                <table class="w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0">
                        <tr>
                            <!-- 任务ID -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 120px;">
                                任务ID
                            </th>
                            <!-- 店铺名称 -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 140px;">
                                店铺名称
                            </th>
                            <!-- 任务类型 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 80px;">
                                类型
                            </th>
                            <!-- 创建时间 -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 160px;">
                                创建时间
                            </th>
                            <!-- 总产品数 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 100px;">
                                总产品数
                            </th>
                            <!-- 成功/失败 -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 300px;">
                                审核进度
                            </th>
                            <!-- 状态 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 100px;">
                                状态
                            </th>
                            <!-- 操作 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 100px;">
                                操作
                            </th>
                            <!-- 更新任务 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 80px;">
                                更新
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200 text-sm" id="taskTableBody">
                        <!-- 测试数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控制 -->
            <div class="px-4 py-3 border-t border-gray-200 bg-white">
                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-700" id="pageInfo">
                        显示 1 到 10 条，共 50 条
                    </div>
                    <div class="flex space-x-1" id="pagination">
                        <button id="prevPage" class="px-2 py-1 border border-gray-300 rounded-md text-xs" onclick="publishSuccessModal.changePage('prev')">
                            上一页
                        </button>
                        <!-- 页码按钮将通过JavaScript动态生成 -->
                        <button id="nextPage" class="px-2 py-1 border border-gray-300 rounded-md text-xs" onclick="publishSuccessModal.changePage('next')">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div id="taskDetailsModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <div class="fixed inset-0 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-[800px] h-[800px] flex flex-col overflow-hidden m-4">
            <!-- 头部 -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 flex-shrink-0">
                <h2 class="text-lg font-medium text-gray-900">任务详情</h2>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="publishSuccessModal.toggleTaskDetails()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 内容区域 -->
            <div class="flex-1 overflow-y-auto p-6">
                <!-- 基本信息 -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">任务ID</p>
                            <p class="text-sm font-medium text-gray-900">${detail.task_id}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">店铺名称</p>
                            <p class="text-sm font-medium text-gray-900">${this.getStoreName(detail.seller_id)}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">创建时间</p>
                            <p class="text-sm font-medium text-gray-900">${detail.upload_time}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">状态</p>
                            <p class="text-sm font-medium ${detail.status === 'valid' ? 'text-green-600' : 'text-gray-600'}">${detail.status === 'valid' ? '有效' : '过期'}</p>
                        </div>
                    </div>
                </div>

                <!-- 上传统计 -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">上传统计</h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">总产品数</p>
                            <p class="text-2xl font-semibold text-gray-900">${detail.total_products}</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">成功数量</p>
                            <p class="text-2xl font-semibold text-green-600">${detail.success_count}</p>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">失败数量</p>
                            <p class="text-2xl font-semibold text-red-600">${detail.failed_count}</p>
                        </div>
                    </div>
                </div>

                <!-- 审核统计 -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">审核统计</h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">审核处理中</p>
                            <p class="text-2xl font-semibold text-yellow-600">${detail.processing_count || 0}</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">审核成功</p>
                            <p class="text-2xl font-semibold text-green-600">${detail.success_count_final || 0}</p>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">审核失败</p>
                            <p class="text-2xl font-semibold text-red-600">${detail.error_count_final || 0}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 使用命名空间模式避免全局变量污染
const publishSuccessModal = {
    currentPage: 1,
    itemsPerPage: 30,  //每页显示数量
    
    // 获取任务列表数据
    async fetchTasks(page = 1, filters = {}) {
        try {
            const queryParams = new URLSearchParams({
                page: page.toString(),
                per_page: this.itemsPerPage.toString()
            });
            
            // 添加筛选条件
            if (filters.seller_id) queryParams.append('seller_id', filters.seller_id);
            if (filters.date_start) queryParams.append('date_start', filters.date_start);
            if (filters.date_end) queryParams.append('date_end', filters.date_end);
            if (filters.status) queryParams.append('status', filters.status);
            
            
            const response = await fetch(`/api/upload-tasks?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            if (data.code === 0) {
                return data.data;
            } else {
                throw new Error(data.message || '获取数据失败');
            }
        } catch (error) {
            console.error('获取任务列表失败:', error);
            return null;
        }
    },
    
    // 获取任务详情
    async fetchTaskDetail(taskId) {
        try {
            const response = await fetch(`/api/upload-tasks/${taskId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            if (data.code === 0) {
                return data.data;
            } else {
                throw new Error(data.message || '获取详情失败');
            }
        } catch (error) {
            console.error('获取任务详情失败:', error);
            return null;
        }
    },
    
    // 获取店铺名称的函数
    getStoreName(sellerId) {
        const storeFilter = document.getElementById('storeFilter');
        // 将 seller_id 转换为字符串并进行比较
        const option = Array.from(storeFilter.options).find(opt => opt.value === sellerId.toString());
        return option ? option.textContent : sellerId.toString();
    },
    
    // 渲染表格数据
    async renderTable() {
        const tableBody = document.getElementById('taskTableBody');
        const filters = this.getFilters();
        
        // 显示加载状态
        tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4">加载中...</td></tr>';
        
        try {
            const data = await this.fetchTasks(this.currentPage, filters);
            if (!data || !data.items) {
                throw new Error('无效的数据格式');
            }
            
            if (data.items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-gray-500">暂无数据</td></tr>';
                return;
            }

            tableBody.innerHTML = '';
            data.items.forEach(item => {

                let taskType = item.task_type || item.type || '';
                taskType = taskType.toLowerCase(); 
                const taskTypeText = 
                    taskType === 'update' ? '更新' : 
                    taskType === 'create' ? '添加' : 
                    taskType || '添加';

                const taskTypeClass = 
                    taskType === 'update' ? 'bg-purple-100 text-purple-800' : 
                    'bg-blue-100 text-blue-800';
                
                const row = `
                    <tr class="h-[50px] hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap">${item.task_id}</td>
                        <td class="px-4 py-3 whitespace-nowrap">${this.getStoreName(item.seller_id)}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${taskTypeClass}">
                                ${taskTypeText}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">${item.upload_time}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">${item.total_products}</td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden" style="min-width: 200px;">
                                <div class="flex h-3">
                                    <div class="bg-yellow-400 h-3" style="width: ${Math.min((item.processing_count/item.total_products)*100, 100)}%; border-top-left-radius: 9999px; border-bottom-left-radius: 9999px;"></div>
                                    <div class="bg-green-600 h-3" style="width: ${Math.min((item.success_count_final/item.total_products)*100, 100)}%"></div>
                                    <div class="bg-red-600 h-3" style="width: ${Math.min((item.error_count_final/item.total_products)*100, 100)}%; border-top-right-radius: 9999px; border-bottom-right-radius: 9999px;"></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.status === 'valid' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${item.status === 'valid' ? '有效' : '过期'}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">
                            <button class="text-blue-600 hover:text-blue-900 mr-2" onclick="publishSuccessModal.toggleTaskDetails('${item.task_id}')">
                                查看详情
                            </button>
                            <button class="text-red-600 hover:text-red-900" onclick="publishSuccessModal.deleteTask('${item.task_id}')">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">
                            <button class="text-green-600 hover:text-green-900 disabled:opacity-50 disabled:cursor-not-allowed" 
                                    onclick="publishSuccessModal.updateTask('${item.task_id}', '${item.seller_id}')"
                                    ${item.status === 'expired' ? 'disabled' : ''}>
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
            
            this.updatePagination(data);
        } catch (error) {
            console.error('渲染表格失败:', error);
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-red-600">加载失败: ' + error.message + '</td></tr>';
        }
    },
    
    // 获取筛选条件
    getFilters() {
        const storeFilter = document.getElementById('storeFilter').value;
        const dateStart = document.getElementById('dateStart').value;
        const dateEnd = document.getElementById('dateEnd').value;
        const statusFilter = document.getElementById('statusFilter').value;
        
        const filters = {};
        if (storeFilter) filters.seller_id = storeFilter;
        if (dateStart) filters.date_start = dateStart;
        if (dateEnd) filters.date_end = dateEnd;
        if (statusFilter) filters.status = statusFilter;
        
        return filters;
    },
    
    // 更新分页信息和按钮状态
    updatePagination(data) {
        const pageInfo = document.getElementById('pageInfo');
        const pagination = document.getElementById('pagination');
        const prevButton = document.getElementById('prevPage');
        const nextButton = document.getElementById('nextPage');
        
        const start = (data.page - 1) * data.per_page + 1;
        const end = Math.min(data.page * data.per_page, data.total);
        
        // 更新显示信息
        pageInfo.textContent = `显示 ${start} 到 ${end} 条，共 ${data.total} 条`;
        
        // 更新上一页/下一页按钮状态
        prevButton.disabled = data.page === 1;
        nextButton.disabled = data.page === data.pages;
        prevButton.classList.toggle('opacity-50', data.page === 1);
        nextButton.classList.toggle('opacity-50', data.page === data.pages);
        
        // 重新生成页码按钮
        const pageButtons = Array.from(pagination.querySelectorAll('button')).filter(
            button => !button.id.includes('prevPage') && !button.id.includes('nextPage')
        );
        pageButtons.forEach(button => button.remove());
        
        // 在上一页和下一页按钮之间插入页码按钮
        const nextPageButton = document.getElementById('nextPage');
        for (let i = 1; i <= data.pages; i++) {
            const button = document.createElement('button');
            button.className = `px-2 py-1 border border-gray-300 rounded-md text-xs ${
                i === data.page ? 'bg-blue-50 text-blue-600' : ''
            }`;
            button.textContent = i;
            button.onclick = () => this.changePage(i);
            pagination.insertBefore(button, nextPageButton);
        }
    },
    
    // 切换页面
    async changePage(page) {
        if (page === 'prev' && this.currentPage > 1) {
            this.currentPage--;
        } else if (page === 'next') {
            this.currentPage++;
        } else if (typeof page === 'number') {
            this.currentPage = page;
        }
        await this.renderTable();
    },
    
    // 切换模态框显示状态
    toggle() {
        const modal = document.getElementById('publishSuccessModal');
        if (modal) {
            modal.classList.toggle('hidden');
            console.log('Modal visibility toggled'); // 添加调试日志
        }
    },
    
    // 切换任务详情模态框
    async toggleTaskDetails(taskId) {
        const modal = document.getElementById('taskDetailsModal');
        modal.classList.toggle('hidden');
        
        if (!modal.classList.contains('hidden') && taskId) {
            const taskDetail = await this.fetchTaskDetail(taskId);
            if (taskDetail) {
                this.renderTaskDetail(taskDetail);
            }
        }
    },
    
    // 渲染任务详情
    renderTaskDetail(detail) {
        const detailContent = document.querySelector('#taskDetailsModal .p-6');
        detailContent.innerHTML = `
            <!-- 基本信息 -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500">任务ID</p>
                        <p class="text-sm font-medium text-gray-900">${detail.task_id}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">店铺名称</p>
                        <p class="text-sm font-medium text-gray-900">${this.getStoreName(detail.seller_id)}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">创建时间</p>
                        <p class="text-sm font-medium text-gray-900">${detail.upload_time}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">状态</p>
                        <p class="text-sm font-medium ${detail.status === 'valid' ? 'text-green-600' : 'text-gray-600'}">${detail.status === 'valid' ? '有效' : '过期'}</p>
                    </div>
                </div>
            </div>

            <!-- 上传统计 -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">上传统计</h3>
                <div class="grid grid-cols-3 gap-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">总产品数</p>
                        <p class="text-2xl font-semibold text-gray-900">${detail.total_products}</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">成功数量</p>
                        <p class="text-2xl font-semibold text-green-600">${detail.success_count}</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">失败数量</p>
                        <p class="text-2xl font-semibold text-red-600">${detail.failed_count}</p>
                    </div>
                </div>
            </div>

            <!-- 审核统计 -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">审核统计</h3>
                <div class="grid grid-cols-3 gap-4">
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">审核处理中</p>
                        <p class="text-2xl font-semibold text-yellow-600">${detail.processing_count || 0}</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">审核成功</p>
                        <p class="text-2xl font-semibold text-green-600">${detail.success_count_final || 0}</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">审核失败</p>
                        <p class="text-2xl font-semibold text-red-600">${detail.error_count_final || 0}</p>
                    </div>
                </div>
            </div>
        `;
    },
    
    // 处理搜索按钮点击
    handleSearch() {
        this.currentPage = 1;  // 重置到第一页
        this.renderTable();
    },
    
    // 处理重置按钮点击
    handleReset() {
        const today = new Date();
        const twoWeeksAgo = new Date(today.getTime() - (14 * 24 * 60 * 60 * 1000));
        
        document.getElementById('storeFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateStart').valueAsDate = twoWeeksAgo;
        document.getElementById('dateEnd').valueAsDate = today;
        
        this.currentPage = 1;  // 重置到第一页
        this.renderTable();
    },
    
    // 初始化
    init() {
        const today = new Date();
        const twoWeeksAgo = new Date(today.getTime() - (14 * 24 * 60 * 60 * 1000));
        
        document.getElementById('dateStart').valueAsDate = twoWeeksAgo;
        document.getElementById('dateEnd').valueAsDate = today;
        
        // 从产品发布页面获取店铺选择器
        const productPublishStoreSelect = document.querySelector('#importStoreSelect');
        if (productPublishStoreSelect) {
            const storeFilter = document.getElementById('storeFilter');
            // 清空现有选项
            storeFilter.innerHTML = '<option value="">所有店铺</option>';
            // 复制店铺选项
            Array.from(productPublishStoreSelect.options).forEach(option => {
                if (option.value) { // 跳过空值选项
                    const newOption = document.createElement('option');
                    newOption.value = option.value;
                    newOption.textContent = option.textContent;
                    storeFilter.appendChild(newOption);
                }
            });
        }
        
        this.renderTable();
    },

    // 添加更新任务方法
    async updateTask(executionId, sellerId) {
        try {
            const button = event.target.closest('button');
            // 禁用按钮并显示加载状态
            button.disabled = true;
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';


            const response = await fetch(`/api/upload-tasks/${executionId}/check`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    seller_id: sellerId
                })
            });

            const data = await response.json();
            
            if (data.code === 0) {
                // 更新成功，刷新表格
                await this.renderTable();
                // 显示成功提示
                notification.success(`更新成功\n处理中: ${data.data.processing_count}\n成功: ${data.data.success_count}\n失败: ${data.data.error_count}`);
            } else {
                throw new Error(data.message || '更新失败');
            }
        } catch (error) {
            console.error('更新任务失败:', error);
            notification.error('更新失败: ' + error.message);
        } finally {
            // 恢复按钮状态
            const button = event.target.closest('button');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-sync-alt"></i>';
        }
    },

    // 添加删除任务方法
    async deleteTask(executionId) {
        try {
            // 显示确认对话框
            const confirmed = await notification.confirm(
                '确定要删除该任务吗？\n删除后将无法恢复！',
                '删除确认'
            );
            
            if (!confirmed) {
                return;
            }

            const response = await fetch(`/api/upload-tasks/${executionId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            
            if (data.code === 0) {
                // 删除成功，刷新表格
                notification.success('删除成功');
                await this.renderTable();
            } else {
                throw new Error(data.message || '删除失败');
            }
        } catch (error) {
            console.error('删除任务失败:', error);
            notification.error('删除失败: ' + error.message);
        }
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    publishSuccessModal.init();
});

// 修改原有的togglePublishSuccessModal函数，使用新的命名空间方法
function togglePublishSuccessModal() {
    publishSuccessModal.toggle();
}
</script> 