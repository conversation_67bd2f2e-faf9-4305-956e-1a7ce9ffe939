from flask import Blueprint, request, jsonify
from models import db, Store, UnifiedListProducts, UploadTask, UploadTaskDetail, beijing_now
from datetime import datetime, timedelta
import requests
import logging
import json
from sqlalchemy import desc
from translate.deeplx_api import DeepLXTranslator
from translate.translator_factory import TranslatorFactory
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
from itertools import islice
import os
from flask import send_file
from models import SystemSettings
bp = Blueprint('product_publish', __name__)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 代理设置
PROXIES = {
    "http": "http://127.0.0.1:10809",
    "https": "http://127.0.0.1:10809",
}

REQUIRED_FIELDS = {
    'title_lt': '立陶宛语标题',
    'title_lv': '拉脱维亚语标题',
    'title_et': '爱沙尼亚语标题',
    'title_fi': '芬兰语标题',
    'description_lt': '立陶宛语描述',
    'description_lv': '拉脱维亚语描述',
    'description_et': '爱沙尼亚语描述',
    'description_fi': '芬兰语描述',
    'category_id': '分类ID',
    'package_length': '包装长度',
    'package_width': '包装宽度',
    'package_height': '包装高度',
    'package_weight': '包装重量',
    'image_url1': '主图'
}

@bp.route('/api/publish/products', methods=['POST'])
def publish_products():
    try:

        request_data = request.get_json()
        products_data = request_data.get('products', [])
        
        if not products_data:
            return jsonify({"error": "没有接收到数据"}), 400
            
        # 获取第一个产品的SKU来查找店铺信息
        first_sku = products_data[0]['sku']
        first_product = UnifiedListProducts.query.filter_by(sku=first_sku).first()
        
        if not first_product:
            return jsonify({"error": "未找到任何有效的产品"}), 400
            
        store = Store.query.filter_by(seller_id=first_product.seller_id).first()
        if not store:
            return jsonify({"error": "未找到店铺信息"}), 400
            
        # 获取token
        token = store.get_valid_token()
        if not token:
            return jsonify({"error": "获取token失败"}), 400
            
        # 查找该店铺最近的有效任务
        now = beijing_now()
        latest_task = UploadTask.query.filter(
            UploadTask.seller_id == store.seller_id,
            UploadTask.expires_at > now,
            UploadTask.task_type == 'create'
        ).order_by(desc(UploadTask.created_at)).first()
        
        # 创建新任务时不设置 total_products   每个任务最多500产品
        if not latest_task or (latest_task.total_products + len(products_data)) > 1900:
            execution_id = create_import_execution(store.seller_id, token)
            if not execution_id:
                return jsonify({"error": "创建import execution失败"}), 400
                
            latest_task = UploadTask(
                execution_id=execution_id,
                seller_id=store.seller_id,
                total_products=0,  # 初始化为0
                expires_at=now + timedelta(days=14),  # 过期时间也用北京时间
                task_type='create'
            )
            db.session.add(latest_task)
            db.session.commit()
        
        success_count = 0
        failed_count = 0
        errors = []
        
        # 处理所有产品
        for product_data in products_data:
            sku = product_data['sku']
            is_retry = product_data.get('is_retry', False)
            
            try:
                product = UnifiedListProducts.query.filter_by(sku=sku).first()
                if not product:
                    raise Exception(f"未找到SKU为{sku}的产品")
                
                # 准备上传数据
                product_upload_data = prepare_upload_data(product)
                
                # 上传产品
                response = upload_product(latest_task.execution_id, token, product_upload_data)
                
                if response and response.get('id'):
                    # 上传成功
                    success_count += 1
                    if not is_retry:
                        # 只有非重试的成功产品才增加总数
                        latest_task.total_products += 1
                    product.status = 'upload_successful'
                    product.publish_time = beijing_now()  # 更新发布时间
                else:
                    # 上传失败
                    if not is_retry:
                        failed_count += 1
                    product.status = 'upload_failed'
                    product.publish_time = beijing_now()  # 更新发布时间
                    error_message = response.get('error', '上传失败')
                    errors.append(f"{sku}: {error_message}")
                
                db.session.commit()
                
            except Exception as e:
                # 处理异常
                if not is_retry:
                    failed_count += 1
                errors.append(f"{sku}: {str(e)}")
                
                if product:
                    product.status = 'upload_failed'
                    product.publish_time = beijing_now()  # 更新发布时间
                    
                db.session.commit()
        
        # 更新任务统计信息
        latest_task.success_count = success_count
        latest_task.failed_count = failed_count
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "发布完成",
            "success_count": success_count,
            "failed_count": failed_count,
            "task_id": latest_task.id,
            "errors": errors
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def prepare_upload_data(product):
    """新路由-产品上传数据准备"""
    # 首先检查四站标题和描述是否完整
    required_fields = {
        'title_lt': '立陶宛语标题',
        'title_lv': '拉脱维亚语标题',
        'title_et': '爱沙尼亚语标题',
        'title_fi': '芬兰语标题',
        'description_lt': '立陶宛语描述',
        'description_lv': '拉脱维亚语描述',
        'description_et': '爱沙尼亚语描述',
        'description_fi': '芬兰语描述'
    }

    # 检查所有必填字段
    for field, field_name in required_fields.items():
        if not getattr(product, field):
            return {
                'error': f'请检查产品四站标题或者描述，SKU: {product.sku} 缺少{field_name}',
                'success': False,
                'sku': product.sku
            }
    # 准备图片URL列表
    images = []
    for url in [product.image_url1, product.image_url2, product.image_url3, product.image_url4, product.image_url5]:
        if url:
            images.append(url)

    # 准备修改信息
    modification = {
        "title":"",  # 使用产品标题作为修改标题  product.title_lt or 
        "sku": product.sku,
        "eans": [product.ean] if product.ean else [],
        "package_weight": float(product.package_weight or 0),
        "package_length": float(product.package_length or 0),
        "package_width": float(product.package_width or 0),
        "package_height": float(product.package_height or 0)
    }

    # 构造基本数据结构
    data = {
        "category_id": int(product.category_id),
        "title": product.title_lt or '',
        "title_lv": product.title_lv or '',
        "title_ee": product.title_et or '',
        "title_fi": product.title_fi or '',
        "title_ru": "",
        "long_description": product.description_lt or '',
        "long_description_lv": product.description_lv or '',
        "long_description_ee": product.description_et or '',
        "long_description_fi": product.description_fi or '',
        "long_description_ru": "",
        "product_features": [
        # 如果想提交空的，可以用空数组  #这里暂时留空后期会添加其他数据
        # {
        #     "name": "Feature name",
        #     "value": "Feature value"
        # }
    ],
        "images": images,
        "youtube_videos": [],
        "modifications": [modification],
        "auto_generate_ean": False # 是否自动生成EAN码，如果为true，则系统会自动生成EAN码，如果为false，则需要手动填写EAN码
    }

    # 记录产品数据，用于调试 添加断点，用于检查数据
    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # filename = f"logs/Z--产品数据_{product.sku}_{timestamp}.json"
    # try:
    #     # 确保logs目录存在
    #     os.makedirs("logs", exist_ok=True)
    #     # 保存数据到JSON文件
    #     with open(filename, 'w', encoding='utf-8') as f:
    #         json.dump(data, f, ensure_ascii=False, indent=2)
    # except Exception as e:
    #     logger.error(f"保存产品数据日志失败: {str(e)}")
    
    return data

def create_import_execution(seller_id, token):
    """创建产品导入执行ID"""
    try:
        url = f"https://pmpapi.pigugroup.eu/v3/sellers/{seller_id}/product/import/execution"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        response = requests.post(url, headers=headers)
        response.raise_for_status()
        return response.json().get('id')
    except Exception as e:
        raise Exception(f"创建导入执行ID失败: {str(e)}")
def create_update_task(seller_id, token):
    """创建更新产品执行ID"""
    try:
        url = f"https://pmpapi.pigugroup.eu/v3/sellers/{seller_id}/product/import/execution"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        response = requests.post(url, headers=headers)
        response.raise_for_status()
        return response.json().get('id')
    except Exception as e:
        logger.error(f"创建产品执行ID失败，响应信息: {response.json()}")
        raise Exception(f"创建更新产品执行ID失败，响应信息: {response.json()}")

def upload_product(execution_id, token, product_data):
    """上传单个产品主函数--暂时弃置不用"""
    try:
        url = f"https://pmpapi.pigugroup.eu/v3/sellers/product/import/execution/{execution_id}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        # 记录请求数据
        # logger.info("=== API请求详情 ===")
        # logger.info(f"请求URL: {url}")
        # logger.info(f"请求Headers: {headers}")
        # logger.info(f"请求数据: {product_data}")
        
        # 添加断点，用于检查数据
        # print("\n=== 断点：检查上传数据 ===")
        # print("URL:", url)
        # print("Headers:", headers)
        # print("产品数据:", filename)
        # print("按回车继续...")
        # input()
        

        
        response = requests.post(url, headers=headers, json=product_data)
        
        # 记录响应数据
        # logger.info("=== API响应详情 ===")
        # logger.info(f"响应状态码: {response.status_code}")
        # logger.info(f"响应Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            # logger.info(f"响应数据: {response_data}")
            
            # 检查响应状态码
            if response.status_code == 200:
                # logger.info("\n=== 产品上传成功 ===")
                # logger.info(f"执行ID: {response_data.get('id')}")
                # logger.info(f"请求总数: {response_data.get('total_request_count')}")
                return response_data
            elif response.status_code == 422:
                # 处理验证错误
                if 'Title and long description must be provided' in str(response_data):
                    error_message = '产品需要添加四站标题和描述'
                else:
                    error_message = response_data.get('errors', [{}])[0].get('message', '验证失败')
                return {'error': error_message}
            else:
                error_message = response_data.get('error', {}).get('message', '未知错误')
                return {'error': error_message}
                
        except ValueError as e:
            return {'error': '解析响应失败'}
            
    except Exception as e:
        logger.error(f"上传产品失败: {str(e)}")
        logger.error(f"错误详情: {getattr(e, 'response', {}).get('text', '无响应内容')}")
        return {'error': str(e)}

@bp.route('/api/publish/tasks/<int:task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取上传任务状态"""
    try:
        task = UploadTask.query.get(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        # 获取任务详情
        details = UploadTaskDetail.query.filter_by(task_id=task_id).all()
        
        # 准备详细信息
        detail_list = []
        for detail in details:
            detail_list.append({
                'sku': detail.sku,
                'status': detail.status,
                'response_id': detail.response_id,
                'error_message': detail.error_message,
                'created_at': detail.created_at.strftime('%Y-%m-%d %H:%M:%S') if detail.created_at else None
            })

        return jsonify({
            'task': {
                'id': task.id,
                'execution_id': task.execution_id,
                'seller_id': task.seller_id,
                'total_products': task.total_products,
                'success_count': task.success_count,
                'failed_count': task.failed_count,
                'status': task.status_text,
                'created_at': task.created_at.strftime('%Y-%m-%d %H:%M:%S') if task.created_at else None,
                'expires_at': task.expires_at.strftime('%Y-%m-%d %H:%M:%S') if task.expires_at else None
            },
            'details': detail_list
        })

    except Exception as e:
        # logger.error(f"获取任务状态失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

async def create_single_product(session, execution_id, token, product_data):    
    """异步创建单个产品"""
    PROXY_HOST = SystemSettings.get_setting('proxy_host')
    PROXY_PORT = SystemSettings.get_setting('proxy_port')
    PROXY_URL = f"http://{PROXY_HOST}:{PROXY_PORT}"
    try:
        url = f"https://pmpapi.pigugroup.eu/v3/sellers/product/import/execution/{execution_id}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
                # 记录请求数据到日志，便于调试
        # logger.info(f"更新产品请求URL: {url}")
        # logger.info(f"更新产品请求头: {headers}")
        sku = product_data['modifications'][0]['sku']


        try:
            async with session.post(url, headers=headers, json=product_data, proxy=PROXY_URL) as response:
                PROXY_HOST = SystemSettings.get_setting('proxy_host')
                PROXY_PORT = SystemSettings.get_setting('proxy_port')
                PROXY_URL = f"http://{PROXY_HOST}:{PROXY_PORT}"
                # 检查响应状态码和内容类型
                content_type = response.headers.get('Content-Type', '')
                # logger.info(f"响应状态码: {response.status}, Content-Type: {content_type}")
                
                # 如果响应不是JSON格式
                if 'application/json' not in content_type.lower():
                    text = await response.text()
                    return {
                        'success': False,
                        'error': "返回异常，请检查网络确保可用",
                        'sku': sku
                    }
                
                # 尝试解析JSON响应
                response_data = await response.json()
                
                if response.status == 200:
                    return {
                        'success': True,
                        'data': response_data,
                        'sku': sku
                    }
                elif response.status == 422:
                    # 处理验证错误
                    if 'errors' in response_data and len(response_data['errors']) > 0:
                        error_message = response_data['errors'][0].get('message', '验证失败')
                        property_path = response_data['errors'][0].get('property_path', '')
                        if property_path:
                            error_message = f"{property_path}: {error_message}"
                    else:
                        error_message = "验证失败，但未提供详细错误信息"
                        return {
                            'success': False,
                            'error': error_message,
                            'sku': sku
                        }
                else:
                    error_message = response_data.get('error', {}).get('message', f'未知错误，状态码: {response.status}')
                    return {
                        'success': False,
                        'error': error_message,
                        'sku': sku
                    }
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON解析错误: {str(json_err)}")
            return {
                'success': False,
                'error': f"无法解析服务器响应的JSON: {str(json_err)}",
                'sku': sku
            }
            
    except Exception as e:
        logger.error(f"创建产品异常: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'sku': sku
        }
    
async def update_single_product(session, execution_id, token, product_data):
    """异步更新单个产品"""
    PROXY_HOST = SystemSettings.get_setting('proxy_host')
    PROXY_PORT = SystemSettings.get_setting('proxy_port')
    PROXY_URL = f"http://{PROXY_HOST}:{PROXY_PORT}"
    try:
        url = f"https://pmpapi.pigugroup.eu/v3/sellers/product/import/execution/{execution_id}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        # 记录请求数据到日志，便于调试
        # logger.info(f"更新产品请求URL: {url}")
        # logger.info(f"更新产品请求头: {headers}")
        sku = product_data['modifications'][0]['sku']
        product = UnifiedListProducts.query.filter_by(sku=sku).first()

        # 检查产品状态 phh成功 只有成功的产品才可以更新
        if product.status != 'phh_success':
            return {
                'success': False,
                'error': "产品状态不为上传成功，请先上传产品",
                'sku': sku
            }
        
        try:
            # 使用正确格式的代理URL
            async with session.patch(url, headers=headers, json=product_data, proxy=PROXY_URL) as response:
                # 检查响应状态码和内容类型
                content_type = response.headers.get('Content-Type', '')
                # logger.info(f"响应状态码: {response.status}, Content-Type: {content_type}")
                
                # 如果响应不是JSON格式
                if 'application/json' not in content_type.lower():
                    text = await response.text()
                    return {
                        'success': False,
                        'error': "返回异常，请检查网络确保可用",
                        'sku': sku
                    }
                
                # 尝试解析JSON响应
                response_data = await response.json()
                
                if response.status == 200:
                    return {
                        'success': True,
                        'data': response_data,
                        'sku': sku
                    }
                elif response.status == 422:
                    # 处理验证错误
                    if 'errors' in response_data and len(response_data['errors']) > 0:
                        error_message = response_data['errors'][0].get('message', '验证失败')
                        property_path = response_data['errors'][0].get('property_path', '')
                        if property_path:
                            error_message = f"{property_path}: {error_message}"
                    else:
                        error_message = "验证失败，但未提供详细错误信息"
                    
                    return {
                        'success': False,
                        'error': error_message,
                        'sku': sku
                    }
                else:
                    # 处理其他错误
                    error_message = response_data.get('error', {}).get('message', f'未知错误，状态码: {response.status}')
                    return {
                        'success': False,
                        'error': error_message,
                        'sku': sku
                    }
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON解析错误: {str(json_err)}")
            return {
                'success': False,
                'error': f"无法解析服务器响应的JSON: {str(json_err)}",
                'sku': sku
            }
            
    except Exception as e:
        logger.error(f"更新产品异常: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'sku': product_data['modifications'][0]['sku']
        }

async def process_batch(products_batch, execution_id, token):
    """处理一批更新产品"""
    async with aiohttp.ClientSession() as session:
        tasks = []
        for product in products_batch:
            product_data = prepare_upload_data(product)
            task = update_single_product(session, execution_id, token, product_data)
            tasks.append(task)
        return await asyncio.gather(*tasks)

async def process_batch_create(products_batch, execution_id, token):
    """处理一批创建产品"""
    async with aiohttp.ClientSession() as session:
        tasks = []
        for product in products_batch:
            product_data = prepare_upload_data(product)
            task = create_single_product(session, execution_id, token, product_data)
            tasks.append(task)
        return await asyncio.gather(*tasks)

def chunks(lst, n):
    """将列表分成大小为n的块"""
    for i in range(0, len(lst), n):
        yield lst[i:i + n]

@bp.route('/api/product/batch-update', methods=['POST'])
def batch_update_products():
    """批量更新产品信息"""
    max_products_per_task = int(SystemSettings.get_setting('max_products_per_task'))
    try:
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        
        # logger.info(f"开始批量更新产品，IDs: {product_ids}")
        
        if not product_ids:
            return jsonify({"error": "没有选择要更新的产品"}), 400
            
        # 获取第一个产品的SKU来查找店铺信息
        first_product = UnifiedListProducts.query.filter(UnifiedListProducts.id == product_ids[0]).first()
        if not first_product:
            return jsonify({"error": "未找到任何有效的产品"}), 400
            
        store = Store.query.filter_by(seller_id=first_product.seller_id).first()
        if not store:
            return jsonify({"error": "未找到店铺信息"}), 400
            
        # 获取token
        token = store.get_valid_token()
        if not token:
            return jsonify({"error": "获取token失败"}), 400
        1
        # 查找该店铺最近的有效任务
        now = beijing_now()
        latest_task = UploadTask.query.filter(
            UploadTask.seller_id == store.seller_id,
            UploadTask.expires_at > now,
            UploadTask.task_type == 'update'
        ).order_by(desc(UploadTask.created_at)).first()
        
        # 判断是否需要创建新的execution_id
        if not latest_task or (latest_task.total_products + len(product_ids)) > max_products_per_task:
            execution_id = create_update_task(store.seller_id, token)
            if not execution_id:
                return jsonify({"error": "创建import execution失败"}), 400
                
            latest_task = UploadTask(
                execution_id=execution_id,
                seller_id=store.seller_id,
                total_products=0,  # 初始化为0
                expires_at=now + timedelta(days=14),  # 过期时间也用北京时间
                task_type='update'
            )
            db.session.add(latest_task)
            db.session.commit()
        else:
            pass
        
        execution_id = latest_task.execution_id
            
        # 获取所有产品
        products = UnifiedListProducts.query.filter(UnifiedListProducts.id.in_(product_ids)).all()
        if not products:
            return jsonify({"error": "未找到要更新的产品"}), 404
            

         # 预检查产品状态 只有一种状态方可更新产品
        for product in products:
            if product.status != 'phh_success':
                return jsonify({
                    "success": False,
                    "error": f"产品状态异常不可更新产品数据，请检查产品状态，SKU: {product.sku}",
                    "errors": [f"产品状态异常不可更新产品数据，请检查产品状态，SKU: {product.sku}"]
                }), 400
        
        # 将产品分成每批40个
        batches = list(chunks(products, 50))        
        success_count = 0
        failed_count = 0
        errors = []
        
        # 使用ThreadPoolExecutor来运行异步代码
        with ThreadPoolExecutor() as executor:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 处理每一批产品
                for batch_index, batch in enumerate(batches):
                    results = loop.run_until_complete(process_batch(batch, execution_id, token))
                    
                    # 处理结果
                    batch_success = 0
                    batch_failed = 0
                    
                    for result in results:
                        if result['success']:
                            success_count += 1
                            batch_success += 1
                            # 更新产品状态
                            product = next((p for p in batch if p.sku == result['sku']), None)
                            if product:
                                product.status = 'upload_successful'
                                product.publish_time = beijing_now()
                        else:
                            failed_count += 1
                            batch_failed += 1
                            error_msg = f"{result['sku']}: {result['error']}"
                            errors.append(error_msg)
                            # # 更新产品状态  
                            # product = next((p for p in batch if p.sku == result['sku']), None)
                            # if product:
                            #     product.status = 'upload_failed'
                            #     product.publish_time = beijing_now()

                    db.session.commit()
                    
            finally:
                loop.close()
        
        # 更新任务统计信息
        latest_task.total_products += len(products)
        latest_task.success_count += success_count
        latest_task.failed_count += failed_count
        db.session.commit()
        
        logger.info(f"批量更新完成: 总计 {len(products)} 个产品, 成功 {success_count}, 失败 {failed_count}")
        
        return jsonify({
            "success": True,
            "message": "更新完成",
            "success_count": success_count,
            "failed_count": failed_count,
            "task_id": latest_task.id,
            "errors": errors
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    
@bp.route('/api/product/batch-create', methods=['POST'])
def batch_create_products():
    """批量创建产品"""
    max_products_per_task = int(SystemSettings.get_setting('max_products_per_task'))
    try:
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        
        # logger.info(f"开始批量创建发布产品，IDs: {product_ids}")
        
        if not product_ids:
            return jsonify({"error": "没有选择要创建的产品"}), 400
            
        # 获取第一个产品的SKU来查找店铺信息
        first_product = UnifiedListProducts.query.filter(UnifiedListProducts.id == product_ids[0]).first()
        if not first_product:
            return jsonify({"error": "未找到任何有效的产品"}), 400
            
        store = Store.query.filter_by(seller_id=first_product.seller_id).first()
        if not store:
            return jsonify({"error": "未找到店铺信息"}), 400
            
        # 获取token
        token = store.get_valid_token()
        if not token:
            return jsonify({"error": "获取token失败"}), 400
        1
        # 查找该店铺最近的有效任务
        now = beijing_now()
        latest_task = UploadTask.query.filter(
            UploadTask.seller_id == store.seller_id,
            UploadTask.expires_at > now,
            UploadTask.task_type == 'create'
        ).order_by(desc(UploadTask.created_at)).first()
        
        # 判断是否需要创建新的execution_id
        if not latest_task or (latest_task.total_products + len(product_ids)) > max_products_per_task:
            execution_id = create_update_task(store.seller_id, token)
            if not execution_id:
                return jsonify({"error": "创建import execution失败"}), 400
                
            latest_task = UploadTask(
                execution_id=execution_id,
                seller_id=store.seller_id,
                total_products=0,  # 初始化为0
                expires_at=now + timedelta(days=14),  # 过期时间也用北京时间
                task_type='create'
            )
            db.session.add(latest_task)
            db.session.commit()
        else:
            pass
        
        execution_id = latest_task.execution_id
            
        # 获取所有产品
        products = UnifiedListProducts.query.filter(UnifiedListProducts.id.in_(product_ids)).all()
        if not products:
            return jsonify({"error": "未找到要创建的产品"}), 404

        # 先检查所有产品状态 允许的三种状态
        for product in products:
            if product.status != 'pending_edit' and product.status != 'upload_failed' and product.status != 'phh_error':
                return jsonify({
                    "success": False,
                    "error": f"产品状态异常不可发布产品，请检查产品状态，SKU: {product.sku}",
                    "errors": [f"产品状态异常不可发布产品，请检查产品状态，SKU: {product.sku}"]
                }), 400

        # 预检查所有产品数据
        for product in products:
            prepared_data = prepare_upload_data(product)
            if isinstance(prepared_data, dict) and 'error' in prepared_data:
                return jsonify({
                    "success": False,
                    "error": prepared_data['error'],
                    "errors": [prepared_data['error']]
                }), 400
            
        # 将产品分成每批100个
        batches = list(chunks(products, 100))        
        success_count = 0
        failed_count = 0
        errors = []
        
        # 使用ThreadPoolExecutor来运行异步代码
        with ThreadPoolExecutor() as executor:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 处理每一批产品
                for batch_index, batch in enumerate(batches):
                    results = loop.run_until_complete(process_batch_create(batch, execution_id, token))
                    
                    # 处理结果
                    batch_success = 0
                    batch_failed = 0
                    
                    for result in results:
                        if result['success']:
                            success_count += 1
                            batch_success += 1
                            # 更新产品状态
                            product = next((p for p in batch if p.sku == result['sku']), None)
                            if product:
                                product.status = 'upload_successful'
                                product.publish_time = beijing_now()
                        else:
                            failed_count += 1
                            batch_failed += 1
                            error_msg = f"{result['sku']}: {result['error']}"
                            errors.append(error_msg)
                            # 更新产品状态  
                            product = next((p for p in batch if p.sku == result['sku']), None)
                            if product:
                                product.status = 'upload_failed'
                                product.publish_time = beijing_now()
                    db.session.commit()
                    
            finally:
                loop.close()
            
        # 更新任务统计信息
        latest_task.total_products += len(products)
        latest_task.success_count += success_count
        latest_task.failed_count += failed_count
        db.session.commit()
        
        logger.info(f"批量创建发布完成: 总计 {len(products)} 个产品, 成功 {success_count}, 失败 {failed_count}")
        
        return jsonify({
            "success": True,
            "message": "创建完成",
            "success_count": success_count,
            "failed_count": failed_count,
            "task_id": latest_task.id,
            "errors": errors
        })
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500
                
            