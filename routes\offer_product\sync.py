from flask import jsonify, request, current_app
from models import Store
from . import bp
from .utils import SyncProgress, ProductSynchronizer
import threading

@bp.route('/sync', methods=['POST'])
def sync_products():
    """同步产品的路由处理函数"""
    data = request.get_json()
    store_id = data.get('store_id')
    
    try:
        store = Store.query.filter_by(seller_id=store_id).first()
        if not store:
            raise Exception(f"店铺不存在: seller_id={store_id}")
        
        # 创建同步器实例
        synchronizer = ProductSynchronizer(store)
        
        # 在新线程中运行同步任务，并传递应用上下文
        app = current_app._get_current_object()  # 获取实际的应用对象
        thread = threading.Thread(target=lambda: run_sync_with_context(app, synchronizer))
        thread.start()
        
        return jsonify({
            "success": True,
            "message": "同步任务已启动"
        })
            
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"同步失败: {str(e)}"
        })

def run_sync_with_context(app, synchronizer):
    """在应用上下文中运行同步任务"""
    with app.app_context():
        try:
            synchronizer.sync_products()
        except Exception as e:
            synchronizer.progress.status = f"同步失败: {str(e)}"
            synchronizer.progress.is_completed = True

@bp.route('/sync/progress', methods=['GET'])
def get_sync_progress():
    """获取同步进度的路由处理函数"""
    progress = SyncProgress()
    return jsonify(progress.to_dict())

@bp.route('/sync/cancel', methods=['POST'])
def cancel_sync():
    """取消同步的路由处理函数"""
    progress = SyncProgress()
    progress.cancel()
    return jsonify({
        "success": True,
        "message": "同步已取消"
    }) 