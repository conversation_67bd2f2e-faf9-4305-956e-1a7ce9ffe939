// 创建样式
const notificationStyle = document.createElement('style');
notificationStyle.textContent = `
/* 通知容器样式 */
#notificationContainer {
    position: fixed;
    top: 8%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

/* 确认对话框容器 */
#confirmContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

/* 确认对话框 */
.confirm-dialog {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 300px;
    animation: slideIn 0.3s ease-out;
}

.confirm-dialog .title {
    font-size: 18px;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 12px;
}

.confirm-dialog .message {
    font-size: 14px;
    color: #4b5563;
    margin-bottom: 20px;
}

.confirm-dialog .buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.confirm-dialog button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.confirm-dialog .cancel {
    background: #f3f4f6;
    color: #4b5563;
    border: 1px solid #e5e7eb;
}

.confirm-dialog .cancel:hover {
    background: #e5e7eb;
}

.confirm-dialog .confirm {
    background: #2563eb;
    color: white;
    border: none;
}

.confirm-dialog .confirm:hover {
    background: #1d4ed8;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 通知基础样式 */
.notification {
    display: flex;
    align-items: flex-start;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 8px 12px;
    margin-bottom: 8px;
    width: 240px;
    animation: fadeIn 0.3s ease-in-out;
}

/* 错误类型 */
.notification.error {
    background-color: #fff4f4;
    border: 1px solid #f8d7da;
}
.notification.error i {
    color: #dc2626;
}
.notification.error .message {
    color: #991b1b;
}

/* 成功类型 */
.notification.success {
    background-color: #f0fdf4;
    border: 1px solid #86efac;
}
.notification.success i {
    color: #16a34a;
}
.notification.success .message {
    color: #166534;
}

/* 警告类型 */
.notification.warning {
    background-color: #fffbeb;
    border: 1px solid #fef3c7;
}
.notification.warning i {
    color: #d97706;
}
.notification.warning .message {
    color: #92400e;
}

/* 信息类型 */
.notification.info {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
}
.notification.info i {
    color: #2563eb;
}
.notification.info .message {
    color: #1e40af;
}

/* 加载类型 */
.notification.loading {
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
}
.notification.loading .message {
    color: #374151;
}

.notification i {
    font-size: 14px;
    margin-right: 8px;
    margin-top: 2px;
}

.notification .message {
    flex-grow: 1;
    font-size: 14px;
    white-space: pre-line;

}

.notification .loader {
    width: 20px;
    height: 20px;
    border: 2px solid #6b7280;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
`;

// 添加样式到文档头部
document.head.appendChild(notificationStyle);

// 通知类
class Notification {
    constructor() {
        this.container = null;
        this.confirmContainer = null;
        // 如果DOM已经加载完成，立即初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        // 初始化通知容器
        const existingContainer = document.getElementById('notificationContainer');
        if (existingContainer) {
            existingContainer.remove();
        }
        this.container = document.createElement('div');
        this.container.id = 'notificationContainer';
        document.body.appendChild(this.container);

        // 初始化确认对话框容器
        const existingConfirmContainer = document.getElementById('confirmContainer');
        if (existingConfirmContainer) {
            existingConfirmContainer.remove();
        }
        this.confirmContainer = document.createElement('div');
        this.confirmContainer.id = 'confirmContainer';
        document.body.appendChild(this.confirmContainer);
    }

    // 显示确认对话框
    async confirm(message, title = '批量删除确认') {
        return new Promise((resolve) => {
            this.confirmContainer.innerHTML = `
                <div class="confirm-dialog">
                    <div class="title">${title}</div>
                    <div class="message">${message}</div>
                    <div class="buttons">
                        <button class="cancel">取消</button>
                        <button class="confirm">确认</button>
                    </div>
                </div>
            `;

            this.confirmContainer.style.display = 'flex';

            const confirmButton = this.confirmContainer.querySelector('.confirm');
            const cancelButton = this.confirmContainer.querySelector('.cancel');

            const cleanup = () => {
                this.confirmContainer.style.display = 'none';
                this.confirmContainer.innerHTML = '';
            };

            confirmButton.onclick = () => {
                cleanup();
                resolve(true);
            };

            cancelButton.onclick = () => {
                cleanup();
                resolve(false);
            };

            // 点击背景关闭
            this.confirmContainer.onclick = (e) => {
                if (e.target === this.confirmContainer) {
                    cleanup();
                    resolve(false);
                }
            };
        });
    }

    // 显示通知
    show(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // 创建图标
        const icon = document.createElement('i');
        switch (type) {
            case 'error':
                icon.className = 'fas fa-exclamation-circle';
                break;
            case 'success':
                icon.className = 'fas fa-check-circle';
                break;
            case 'warning':
                icon.className = 'fas fa-exclamation-triangle';
                break;
            case 'info':
                icon.className = 'fas fa-info-circle';
                break;
            default:
                icon.className = 'fas fa-info-circle';
        }
        
        // 创建消息文本
        const messageElement = document.createElement('div');
        messageElement.className = 'message';
        messageElement.innerHTML = message;

        // 组装通知
        notification.appendChild(icon);
        notification.appendChild(messageElement);
        this.container.appendChild(notification);

        // 设置自动移除
        setTimeout(() => {
            notification.style.animation = 'fadeOut 0.5s ease-out forwards';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, duration);

        return notification;
    }

    // 显示成功通知
    success(message, duration = 3000) {
        return this.show(message, 'success', duration);
    }

    // 显示错误通知
    error(message, duration = 3000) {
        return this.show(message, 'error', duration);
    }

    // 显示警告通知
    warning(message, duration = 3000) {
        return this.show(message, 'warning', duration);
    }

    // 显示信息通知
    info(message, duration = 3000) {
        return this.show(message, 'info', duration);
    }

    // 显示加载中通知
    showLoading(message = '加载中...') {
        const notification = document.createElement('div');
        notification.className = 'notification loading';

        const loader = document.createElement('div');
        loader.className = 'loader';

        const messageElement = document.createElement('div');
        messageElement.className = 'message';
        messageElement.textContent = message;

        notification.appendChild(loader);
        notification.appendChild(messageElement);
        this.container.appendChild(notification);

        return {
            remove: () => {
                notification.style.animation = 'fadeOut 0.5s ease-out forwards';
                setTimeout(() => {
                    notification.remove();
                }, 500);
            },
            updateMessage: (newMessage) => {
                messageElement.textContent = newMessage;
            }
        };
    }
}

// 确保只创建一个实例
if (!window.notification) {
    window.notification = new Notification();
}