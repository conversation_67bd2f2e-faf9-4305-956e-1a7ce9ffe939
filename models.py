from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
from sqlalchemy import MetaData, Table, Column, Integer, String, Float, DateTime, create_engine, inspect, or_
from sqlalchemy.ext.declarative import declarative_base
import requests
import time
from threading import Thread
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

db = SQLAlchemy()

def beijing_now():
    """返回北京时间"""
    return datetime.utcnow() + timedelta(hours=8)

class UnifiedListProducts(db.Model):
    __tablename__ = 'unified_list_products'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    seller_id = db.Column(db.String(100), db.ForeignKey('stores.seller_id', ondelete='CASCADE'), nullable=False)
    store = db.relationship('Store', backref=db.backref('products', lazy=True, cascade='all, delete-orphan'))
    
    sku = db.Column(db.String(100), nullable=False)
    ean = db.Column(db.String(100))
    category_id = db.Column(db.Integer)
    title_en = db.Column(db.String(500))
    title_cn = db.Column(db.String(500))
    title_lt = db.Column(db.String(500))
    title_lv = db.Column(db.String(500))
    title_et = db.Column(db.String(500))
    title_fi = db.Column(db.String(500))
    description_en = db.Column(db.Text(length=4294967295))
    description_lt = db.Column(db.Text(length=4294967295))
    description_lv = db.Column(db.Text(length=4294967295))
    description_et = db.Column(db.Text(length=4294967295))
    description_fi = db.Column(db.Text(length=4294967295))
    image_url1 = db.Column(db.String(500))
    image_url2 = db.Column(db.String(500))
    image_url3 = db.Column(db.String(500))
    image_url4 = db.Column(db.String(500))
    image_url5 = db.Column(db.String(500))
    video_url = db.Column(db.String(500))
    status = db.Column(
        db.Enum(
            'pending_edit',
            'upload_successful',
            'upload_failed',
            'phh_processing',
            'phh_success',
            'phh_error',
            'xml_import',
            'xml_success',
            'xml_failed',
            name='product_status_enum'
        ),
        default='pending_edit',
        nullable=False
    )
    package_length = db.Column(db.Float)
    package_width = db.Column(db.Float)
    package_height = db.Column(db.Float)
    package_weight = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=beijing_now)
    publish_time = db.Column(db.DateTime)  # 添加发布时间字段
    platform_message = db.Column(db.Text)
    notes = db.Column(db.Text)

    __table_args__ = (
        db.Index('idx_seller_id_sku', 'seller_id', 'sku', unique=True),
        db.Index('idx_status', 'status'),
        db.Index('idx_created_at', 'created_at'),
        db.Index('idx_publish_time', 'publish_time'),  # 添加发布时间索引
    )

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'seller_id': self.seller_id,
            'sku': self.sku,
            'ean': self.ean,
            'category_id': self.category_id,
            'title_en': self.title_en,
            'title_cn': self.title_cn,
            'title_lt': self.title_lt,
            'title_lv': self.title_lv,
            'title_et': self.title_et,
            'title_fi': self.title_fi,
            'description_en': self.description_en,
            'description_lt': self.description_lt,
            'description_lv': self.description_lv,
            'description_et': self.description_et,
            'description_fi': self.description_fi,
            'image_url1': self.image_url1,
            'image_url2': self.image_url2,
            'image_url3': self.image_url3,
            'image_url4': self.image_url4,
            'image_url5': self.image_url5,
            'video_url': self.video_url,
            'status': self.status,
            'package_length': self.package_length,
            'package_width': self.package_width,
            'package_height': self.package_height,
            'package_weight': self.package_weight,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'publish_time': self.publish_time.strftime('%Y-%m-%d %H:%M:%S') if self.publish_time else None,  # 添加发布时间
            'platform_message': self.platform_message,
            'notes': self.notes
        }

class Store(db.Model):
    __tablename__ = 'stores'
    
    id = db.Column(db.Integer, primary_key=True)
    seller_id = db.Column(db.String(100), nullable=False, unique=True)
    name = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(100), nullable=False)
    password = db.Column(db.String(100), nullable=False)
    table_name = db.Column(db.String(100))
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=beijing_now)
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now)
    token = db.Column(db.String(500))
    token_expires_at = db.Column(db.DateTime)
    token_refresh_attempts = db.Column(db.Integer, default=0)  # 记录刷新尝试次数
    last_refresh_error = db.Column(db.String(500))  # 记录最后一次刷新错误
    
    # 添加反向关系属性，这样可以通过store.list_products访问关联的产品
    @property
    def list_products(self):
        """获取该店铺的所有列表产品"""
        ListProductsModel = self.get_list_products_model()
        if ListProductsModel:
            return ListProductsModel.query.filter_by(seller_id=self.seller_id).all()
        return []
    
    def get_valid_token(self, force_refresh=False):
        """
        获取有效的token
        :param force_refresh: 是否强制刷新token
        :return: 有效的token
        """
        now = datetime.now()
        
        # 如果token存在且未过期，且不是强制刷新，则直接返回
        if not force_refresh and self.token and self.token_expires_at and self.token_expires_at > now:
            # 如果距离过期时间不到1天，后台异步刷新token
            if self.token_expires_at - now < timedelta(days=1):
                self._async_refresh_token()
            return self.token
            
        # 需要刷新token
        return self.refresh_token()
        
    def refresh_token(self, max_retries=3, retry_delay=5):
        """
        刷新token
        :param max_retries: 最大重试次数
        :param retry_delay: 重试延迟（秒）
        :return: 新的token
        """
        last_error = None
        
        for attempt in range(max_retries):
            try:
                login_url = "https://pmpapi.pigugroup.eu/v3/login"
                response = requests.post(
                    login_url,
                    json={
                        "username": self.username,
                        "password": self.password
                    },
                    # proxies={
                    #     "http": "http://127.0.0.1:10809",
                    #     "https": "http://127.0.0.1:10809",
                    # },
                    timeout=30  # 设置超时时间
                )
                
                if response.status_code != 200:
                    raise Exception(f"服务器返回错误: {response.status_code}")
                    
                data = response.json()
                token = data.get('token')
                if not token:
                    raise Exception("响应中无token")
                    
                # 更新token信息
                self.token = token
                self.token_expires_at = datetime.now() + timedelta(days=29)  # 29天后过期
                self.token_refresh_attempts = 0  # 重置尝试次数
                self.last_refresh_error = None  # 清除错误记录
                db.session.commit()
                
                #logger.info(f"成功刷新token: {self.name}")
                return token
                
            except Exception as e:
                last_error = str(e)
                self.token_refresh_attempts += 1
                self.last_refresh_error = last_error
                db.session.commit()
                #logger.error(f"刷新token失败 (尝试 {attempt + 1}/{max_retries}): {last_error}")
                
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    
        # 所有重试都失败
        raise Exception(f"刷新token失败（尝试{max_retries}次）: {last_error}")
        
    def _async_refresh_token(self):
        """异步刷新token"""
        def refresh_task():
            try:
                self.refresh_token()
            except Exception as e:
                # 记录错误但不抛出异常
                self.last_refresh_error = str(e)
                db.session.commit()
                
        # 创建新线程执行刷新
        Thread(target=refresh_task, daemon=True).start()

    def create_products_table(self):
        """创建店铺产品表 - 现在使用统一表"""
        try:
            # 确保统一表存在
            inspector = inspect(db.engine)
            if not inspector.has_table('unified_storeoffer_products'):
                db.create_all()
            
            # 设置表名（保留用于兼容性）
            self.table_name = f'store_products_{self.seller_id}'
            db.session.commit()
            
            return True
        except Exception as e:
            db.session.rollback()
            raise Exception(f'创建产品表失败: {str(e)}')

    def get_products_model(self):
        """获取店铺产品表模型 - 现在返回统一表模型"""
        return UnifiedStoreofferProducts

    def get_list_products_model(self):
        """获取产品列表模型（兼容旧代码）"""
        return UnifiedListProducts
    
    def create_list_products_table(self):
        """创建产品列表表（兼容旧代码）"""
        return True  # 不再需要创建表

class UploadTask(db.Model):
    __tablename__ = 'upload_tasks'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    execution_id = db.Column(db.String(50), unique=True, nullable=False)
    seller_id = db.Column(db.Integer, nullable=False)
    task_type = db.Column(db.Text, nullable=False, comment='任务类型：create/update')
    total_products = db.Column(db.Integer, default=0)
    success_count = db.Column(db.Integer, default=0)
    failed_count = db.Column(db.Integer, default=0)
    expires_at = db.Column(db.DateTime, nullable=False)
    created_at = db.Column(db.DateTime, default=beijing_now)
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now)
    check_status = db.Column(db.String(20), default='pending', comment='检查状态：pending/checking/checked')
    last_check_time = db.Column(db.DateTime, comment='最后检查时间')
    processing_count = db.Column(db.Integer, default=0, comment='处理中的产品数量')
    success_count_final = db.Column(db.Integer, default=0, comment='最终成功的产品数量')
    error_count_final = db.Column(db.Integer, default=0, comment='最终失败的产品数量')
    
    @property
    def remaining_days(self):
        """计算剩余有效天数"""
        if self.expires_at:
            delta = self.expires_at - datetime.utcnow()
            return max(0, delta.days)
        return 0
    
    @property
    def is_valid(self):
        """判断任务是否有效"""
        return self.expires_at > datetime.utcnow()
    
    @property
    def status_text(self):
        """获取状态的中文描述"""
        if not self.is_valid:
            return 'expired'
        return 'valid'

class UploadTaskDetail(db.Model):
    __tablename__ = 'upload_task_details'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    task_id = db.Column(db.Integer, db.ForeignKey('upload_tasks.id'), nullable=False)
    sku = db.Column(db.String(100), nullable=False, index=True)
    
    # 上传状态相关字段（保持原有）
    status = db.Column(db.String(20), default='pending', comment='上传状态：pending/success/failed')
    response_id = db.Column(db.Integer, comment='上传时API返回的ID')
    error_message = db.Column(db.Text, comment='上传时的错误信息')
    
    # 新增平台检查状态相关字段
    platform_status = db.Column(db.String(20), default='pending', comment='平台状态：pending/processing/success/error')
    platform_message = db.Column(db.Text, comment='平台返回的处理消息')
    platform_id = db.Column(db.String(100), comment='平台返回的产品ID')
    check_time = db.Column(db.DateTime, comment='最后检查时间')
    
    created_at = db.Column(db.DateTime, default=beijing_now)
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now)

    # 建立与主表的关系
    task = db.relationship('UploadTask', backref=db.backref('details', lazy=True))

    @property
    def status_text(self):
        """获取状态的中文描述"""
        status_map = {
            'pending': '待处理',
            'phh_processing': '审核处理中',
            'phh_success': '审核成功',
            'phh_error': '审核失败'
        }
        return status_map.get(self.platform_status, self.platform_status)

    def __repr__(self):
        return f'<UploadTaskDetail {self.sku} ({self.platform_status})>'

def get_product_table(store_id):
    """获取店铺对应的产品表模型"""
    if not store_id:
        return None
        
    store = Store.query.get(store_id)
    if not store:
        return None
        
    return store.get_products_model()

class UnifiedStoreofferProducts(db.Model):
    """统一的店铺产品表"""
    __tablename__ = 'unified_storeoffer_products'

    id = db.Column(db.BigInteger, primary_key=True)  # 修改为 BigInteger 以匹配原表的 ID
    seller_id = db.Column(db.String(50), db.ForeignKey('stores.seller_id', ondelete='CASCADE'))  # 店铺ID，添加外键关联
    external_id = db.Column(db.String(100))  # 外部ID
    pigu_external_id = db.Column(db.Integer)  # PIGU外部ID
    app_name = db.Column(db.String(50))      # 应用/国家名称
    title = db.Column(db.String(500))        # 标题
    insult_price = db.Column(db.Float)       # 最低价格
    buybox_price = db.Column(db.Float)       # 购物车价格
    relevant_market_price = db.Column(db.Float)  # 相关市场价格
    sku = db.Column(db.String(100), index=True)  # SKU
    ean = db.Column(db.String(100))         # EAN码
    delivery_hours = db.Column(db.Integer)   # 发货时间
    amount = db.Column(db.Integer)          # 库存数量
    sell_price = db.Column(db.Float)        # 销售价格
    sell_price_after_discount = db.Column(db.Float)  # 折扣后价格
    status = db.Column(db.String(20), default='active')  # 状态
    created_at = db.Column(db.DateTime, default=beijing_now)  # 创建时间
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now)  # 更新时间
    base_price = db.Column(db.Float, nullable=True, comment='基准价格')  # 基础价格

    # 修改关系定义，使用不同的 backref 名称
    store = db.relationship('Store', backref=db.backref('store_offer_products', lazy=True, cascade='all, delete-orphan'))

    def __repr__(self):
        return f'<UnifiedStoreofferProducts {self.sku}>'

# 在models.py中添加SystemSettings模型
class SystemSettings(db.Model):
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=beijing_now)
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now)

    # 默认设置
    DEFAULT_SETTINGS = {
        # 代理设置
        'proxy_enabled': {'value': 'true', 'description': '是否启用代理'},
        'proxy_host': {'value': '127.0.0.1', 'description': '代理服务器地址'},
        'proxy_port': {'value': '10809', 'description': '代理服务器端口'},
        
        # 任务设置
        'task_expire_days': {'value': '14', 'description': '任务过期天数'},
        'max_products_per_task': {'value': '500', 'description': '每个任务最大产品数'},
        'auto_check_interval': {'value': '300', 'description': '自动检查任务状态间隔(秒)'},
        
        # 翻译设置
        'translation_enabled': {'value': 'true', 'description': '是否启用翻译'},
        'translation_service': {'value': 'deeplx', 'description': '翻译服务类型'},
        'translation_api_key': {'value': '', 'description': '常规模式翻译服务API密钥'},
        'translation_retry_count': {'value': '3', 'description': '翻译请求重试次数'},
        'translation_retry_interval': {'value': '5', 'description': '翻译重试间隔(秒)'},
        'translation_model': {'value': 'deepseek_official', 'description': 'AI翻译模型ID'},
        'zh_deepseek_api_key': {'value': '', 'description': '组合模式DeepSeekAPI秘钥'},
        'zh_mtran_api_key': {'value': '', 'description': '组合模式MtranAPI地址'},
        'zh_translation_model': {'value': '', 'description': '组合模式AI翻译模型ID'},
    
        # token 费率设置
        'token_input_rate': {'value': '0.0020', 'description': 'DeepSeek推理输入费率(元/千tokens)'},
        'token_output_rate': {'value': '0.0080', 'description': 'DeepSeek推理输出费率(元/千tokens)'},
        'token_input_rate_huoshan': {'value': '0.0020', 'description': '火山引擎推理输入费率(元/千tokens)'},
        'token_output_rate_huoshan': {'value': '0.0080', 'description': '火山引擎推理输出费率(元/千tokens)'}
    }

    @classmethod
    def get_setting(cls, key, default=None):
        """获取设置值"""
        setting = cls.query.filter_by(key=key).first()
        if setting is None:
            return default
        return setting.value

    @classmethod
    def set_setting(cls, key, value):
        """设置值"""
        setting = cls.query.filter_by(key=key).first()
        if setting is None:
            setting = cls(key=key, value=value)
            db.session.add(setting)
        else:
            setting.value = value
        db.session.commit()

    @classmethod
    def init_settings(cls):
        """初始化系统设置"""
        for key, data in cls.DEFAULT_SETTINGS.items():
            if not cls.query.filter_by(key=key).first():
                setting = cls(
                    key=key,
                    value=data['value'],
                    description=data['description']
                )
                db.session.add(setting)
        db.session.commit()

    def __repr__(self):
        return f'<SystemSettings {self.key}={self.value}>'

class MultiModelConfig(db.Model):
    """临时多模型配置表"""
    __tablename__ = 'multi_model_config'
    
    id = db.Column(db.Integer, primary_key=True)
    enabled = db.Column(db.Boolean, default=False)  # 是否启用多模型
    model_name = db.Column(db.String(100), nullable=False)  # 模型名称
    model_id = db.Column(db.String(100), nullable=False)  # 模型ID
    api_key = db.Column(db.String(500))  # API密钥
    token_limit = db.Column(db.Integer, default=4600000)  # token限制（460万）
    used_tokens = db.Column(db.Integer, default=0)  # 已使用的token数
    is_active = db.Column(db.Boolean, default=True)  # 是否可用（未超出限制）
    created_at = db.Column(db.DateTime, default=beijing_now)
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now)
    
    @classmethod
    def get_available_model(cls):
        """获取可用的模型配置，按ID顺序获取第一个可用的模型"""
        return cls.query.filter_by(
            enabled=True, 
            is_active=True
        ).order_by(cls.id.asc()).first()  # 按ID升序排序，确保从最小ID开始使用
    
    def update_token_usage(self, tokens):
        """更新token使用量"""
        self.used_tokens += tokens
        if self.used_tokens >= self.token_limit:
            self.is_active = False
        db.session.commit()

class TaskCenter(db.Model):
    """任务中心模型"""
    __tablename__ = 'task_center'
    
    # 基本信息
    id = db.Column(db.String(150), primary_key=True)  # 使用 seller_id + task_type 作为主键
    seller_id = db.Column(db.String(100), db.ForeignKey('stores.seller_id', ondelete='CASCADE'), nullable=False)
    store = db.relationship('Store', backref=db.backref('tasks', lazy=True, cascade='all, delete-orphan'))
    description = db.Column(db.String(200), comment='任务描述')
    
    # 任务配置
    task_type = db.Column(db.Enum('translation', 'publish', name='task_type_enum'), nullable=False)
    batch_size = db.Column(db.Integer, nullable=False, default=200, comment='每次处理的产品数量')
    interval = db.Column(db.Integer, nullable=False, default=30, comment='执行间隔（分钟）')
    priority = db.Column(db.Integer, nullable=False, default=3, comment='优先级：1最高-5最低')
    
    # 任务状态
    status = db.Column(
        db.Enum('running', 'stopped', 'completed', 'executing', name='task_status_enum'),
        nullable=False,
        default='stopped'
    )
    
    # 当前执行的统计
    total_count = db.Column(db.Integer, default=0, comment='总产品数量')
    processing_count = db.Column(db.Integer, default=0, comment='处理中的数量')
    processed_count = db.Column(db.Integer, default=0, comment='已处理数量')
    failed_count = db.Column(db.Integer, default=0, comment='失败数量')
    progress = db.Column(db.Float, default=0, comment='进度百分比')
    
    # 累计统计
    execution_count = db.Column(db.Integer, default=0, comment='总执行次数')
    total_success_count = db.Column(db.Integer, default=0, comment='累计处理成功数量')
    total_failed_count = db.Column(db.Integer, default=0, comment='累计处理失败数量')
    
    # 时间相关
    created_at = db.Column(db.DateTime, default=beijing_now)
    last_run_at = db.Column(db.DateTime, comment='最后执行时间')
    next_run_at = db.Column(db.DateTime, comment='下次执行时间')
    
    # 错误信息
    last_error = db.Column(db.Text, comment='最后一次执行的错误信息')
    
    # 索引和约束
    __table_args__ = (
        db.UniqueConstraint('seller_id', 'task_type', name='uq_seller_task_type'),  # 确保每个店铺的每种任务类型只能有一个
        db.Index('idx_seller_id_status', 'seller_id', 'status'),
        db.Index('idx_priority_next_run', 'priority', 'next_run_at'),
    )

    def __init__(self, **kwargs):
        # 在初始化时自动生成 ID
        if 'seller_id' in kwargs and 'task_type' in kwargs:
            kwargs['id'] = f"{kwargs['seller_id']}{kwargs['task_type']}"
        super(TaskCenter, self).__init__(**kwargs)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'seller_id': self.seller_id,
            'store_name': self.store.name if self.store else None,
            'description': self.description,
            'task_type': self.task_type,
            'batch_size': self.batch_size,
            'interval': self.interval,
            'priority': self.priority,
            'status': self.status,
            'total_count': self.total_count,
            'processing_count': self.processing_count,
            'processed_count': self.processed_count,
            'failed_count': self.failed_count,
            'progress': self.progress,
            'execution_count': self.execution_count,
            'total_success_count': self.total_success_count,
            'total_failed_count': self.total_failed_count,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'last_run_at': self.last_run_at.strftime('%Y-%m-%d %H:%M:%S') if self.last_run_at else None,
            'next_run_at': self.next_run_at.strftime('%Y-%m-%d %H:%M:%S') if self.next_run_at else None,
            'last_error': self.last_error
        }

class TokenUsage(db.Model):
    """Token使用记录"""
    __tablename__ = 'token_usage'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    api_provider = db.Column(db.String(50), nullable=False)  # 新增：API提供商
    request_tokens = db.Column(db.Integer, default=0)  # 请求token数
    response_tokens = db.Column(db.Integer, default=0)  # 响应token数
    total_tokens = db.Column(db.Integer, default=0)    # 总token数
    created_at = db.Column(db.DateTime, default=beijing_now)
    
    __table_args__ = (
        db.Index('idx_date_provider', 'date', 'api_provider'),  # 复合索引
    )
    
    @classmethod
    def add_usage(cls, request_tokens: int, response_tokens: int, api_provider: str = 'deepseek_official'):
        """添加token使用记录"""
        today = beijing_now().date()
        record = cls.query.filter_by(date=today, api_provider=api_provider).first()
        
        if record:
            record.request_tokens += request_tokens
            record.response_tokens += response_tokens
            record.total_tokens += (request_tokens + response_tokens)
        else:
            record = cls(
                date=today,
                api_provider=api_provider,
                request_tokens=request_tokens,
                response_tokens=response_tokens,
                total_tokens=request_tokens + response_tokens
            )
            db.session.add(record)
            
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to save token usage: {str(e)}")
    
    @classmethod
    def get_usage_stats(cls, api_provider: str = 'deepseek_official'):
        """获取使用统计"""
        today = beijing_now().date()
        yesterday = today - timedelta(days=1)
        
        # 构建基础查询
        base_query = cls.query.filter_by(api_provider=api_provider)
            
        today_stats = base_query.filter_by(date=today).first()
        yesterday_stats = base_query.filter_by(date=yesterday).first()
        
        # 总计统计需要按API提供商过滤
        total_stats = db.session.query(
            db.func.sum(cls.request_tokens).label('request_tokens'),
            db.func.sum(cls.response_tokens).label('response_tokens'),
            db.func.sum(cls.total_tokens).label('total_tokens')
        ).filter_by(api_provider=api_provider).first()
        
        return {
            'today': {
                'request_tokens': today_stats.request_tokens if today_stats else 0,
                'response_tokens': today_stats.response_tokens if today_stats else 0,
                'total_tokens': today_stats.total_tokens if today_stats else 0
            },
            'yesterday': {
                'request_tokens': yesterday_stats.request_tokens if yesterday_stats else 0,
                'response_tokens': yesterday_stats.response_tokens if yesterday_stats else 0,
                'total_tokens': yesterday_stats.total_tokens if yesterday_stats else 0
            },
            'total': {
                'request_tokens': total_stats.request_tokens or 0,
                'response_tokens': total_stats.response_tokens or 0,
                'total_tokens': total_stats.total_tokens or 0
            }
        }
    
class XmlConversionTask(db.Model):
    """XML转换任务模型"""
    __tablename__ = 'upload_xml_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.String(36), unique=True, nullable=False)
    seller_id = db.Column(db.String(50), nullable=False)
    product_count = db.Column(db.Integer, default=0)
    status = db.Column(db.String(20), default='processing')  # processing, completed, failed
    file_name = db.Column(db.String(255))
    file_path = db.Column(db.String(255))
    create_time = db.Column(db.DateTime, default=datetime.now)
    complete_time = db.Column(db.DateTime)
    error_message = db.Column(db.Text)

    def to_dict(self):
        return {
            'id': self.id,
            'task_id': self.task_id,
            'seller_id': self.seller_id,
            'product_count': self.product_count,
            'status': self.status,
            'file_name': self.file_name,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'complete_time': self.complete_time.strftime('%Y-%m-%d %H:%M:%S') if self.complete_time else None,
            'error_message': self.error_message
        } 