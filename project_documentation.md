# 项目重构文档

## 概述
本文档旨在提供对现有项目的详细描述，并指导将基于Flask的Python Web应用重构为前端使用Next.js、后端使用Node.js、数据库使用MySQL的新架构。重构将分阶段进行，以降低风险和开发成本。

## 当前项目详细描述

### 项目概述
该项目是一个名为"PHH商品在线管理"的Web应用，旨在帮助用户管理多个电商平台的店铺和产品数据，提供产品上传、翻译、任务调度等功能。系统支持多语言产品信息管理，集成第三方API进行翻译和平台数据同步。

### 技术栈
- **后端框架**：Flask 2.3.3 (Python)
- **数据库ORM**：Flask-SQLAlchemy 3.1.1, SQLAlchemy 2.0.21
- **数据库**：SQLite (通过SQLAlchemy配置，可在`app.py`中看到)
- **任务调度**：Flask-APScheduler 1.13.1
- **前端模板**：Jinja2 (Flask自带)
- **前端样式**：Tailwind CSS 2.2.19 (通过CDN加载)
- **前端图标**：FontAwesome 5.15.4 (通过CDN加载)
- **HTTP客户端**：requests 2.31.0, httpx 0.27.0
- **数据处理**：pandas 2.1.1, numpy 1.24.3
- **其他依赖**：参见[requirements.txt](requirements.txt)

### 项目文件结构
```
/
├── app.py                       # 主应用文件，Flask应用初始化和路由配置
├── models.py                    # 数据库模型定义，所有表结构
├── scheduler.py                 # 任务调度配置，定时任务逻辑
├── test.py                      # 测试脚本
├── requirements.txt             # 项目依赖列表
├── .gitattributes               # Git配置
├── .gitignore                   # Git忽略文件
├── project_documentation.md     # 本文档
├── README.md                    # 项目说明文件
├── 隐藏导入.txt                 # 未知用途文本文件
├── Product XML.xml              # 产品XML数据样本
├── Product.xml                  # 产品XML数据样本
├── Product0409.xml              # 产品XML数据样本
├── Z--categories_all_20250314_135923.json  # 分类数据JSON
├── data/                        # 数据存储目录
│   ├── docs/                    # 文档目录，包含XML样本
│   │   ├── XmlProduct copy.xml
│   │   └── XmlProduct.xml
│   ├── json/                    # JSON数据目录
│   │   ├── categories.json     # 分类数据
│   │   └── mapping.json        # 映射数据
│   └── xmloutput/              # XML输出目录，包含多个XML文件
│       ├── XML_9001111_20250505101733.xml
│       └── ...                 # 其他XML文件
├── logs/                       # 日志目录（空）
├── migrations/                 # 数据库迁移目录，使用Alembic
│   ├── alembic.ini            # 迁移配置文件
│   ├── env.py                 # 迁移环境配置
│   ├── README                 # 迁移说明
│   ├── script.py.mako         # 迁移脚本模板
│   └── versions/              # 迁移版本目录
│       └── ac9057a81ea7_add_publish_time_to_products.py  # 具体迁移脚本
├── models/                    # 模型目录（空）
├── routes/                    # API路由和视图目录
│   ├── product_list.py        # 产品列表相关路由
│   ├── product_publish.py     # 产品发布相关路由
│   ├── upload_task.py         # 上传任务相关路由
│   ├── offer_product/         # 报价产品相关API
│   │   ├── __init__.py
│   │   ├── countries.py       # 国家相关API
│   │   ├── editor.py          # 编辑器相关API
│   │   ├── products.py        # 产品相关API
│   │   ├── stats.py           # 统计相关API
│   │   ├── sync.py            # 同步相关API
│   │   └── utils.py           # 工具函数
│   ├── phh_store/             # PHH店铺相关API
│   │   ├── __init__.py
│   │   └── routes.py          # 店铺路由
│   ├── settings/              # 设置相关API
│   │   ├── __init__.py
│   │   ├── multi_model.py     # 多模型配置API
│   │   └── system.py          # 系统设置API
│   ├── task_center/           # 任务中心相关API
│   │   ├── __init__.py
│   │   ├── handlers.py        # 任务处理器
│   │   ├── routes.py          # 任务路由
│   │   └── services.py        # 任务服务
│   └── xml_task/              # XML任务相关API
│       ├── __init__.py
│       └── views.py           # XML任务视图
├── scripts/                   # 辅助脚本目录
│   ├── product_editor.py      # 产品编辑脚本
│   └── sync_handler.py        # 同步处理脚本
├── static/                    # 静态资源目录
│   ├── image/                 # 图片目录
│   │   └── logo.ico           # 应用图标
│   └── js/                    # JavaScript目录
│       └── notification.js    # 通知相关JS
├── templates/                 # 前端模板目录
│   ├── auto_pricing.html      # 自动定价页面
│   ├── base.html              # 基础模板
│   ├── index.html             # 首页模板
│   ├── product_publish.html   # 产品发布页面
│   ├── publish_success_modal.html  # 发布成功模态框
│   ├── store_management.html  # 店铺管理页面
│   ├── system.html            # 系统设置页面
│   ├── task-center.html       # 任务中心页面
│   ├── xml_convert_modal.html # XML转换模态框
│   └── layouts/               # 布局模板目录
│       └── base_table_layout.html  # 基础表格布局
├── translate/                 # 翻译服务模块目录
│   ├── 多模型报错.py          # 多模型报错处理
│   ├── deeplvercel_api.py     # DeepL Vercel API接口
│   ├── deeplx_api.py          # DeepLX API接口
│   ├── deepseek_api.py        # DeepSeek API接口
│   ├── deepseekhuoshan_api.py # DeepSeek火山引擎API接口
│   ├── deepseekmtserver_api.py # DeepSeek MT服务器API接口
│   ├── deepseekofficial_api.py # DeepSeek官方API接口
│   ├── mtranserver_api.py     # MTran服务器API接口
│   ├── selling51_api.py       # Selling51 API接口
│   ├── translator_factory.py  # 翻译器工厂类
│   └── yuanlai.py            # 源来翻译接口
└── utils/                    # 工具类目录（空）
```

### 核心功能模块
1. **店铺管理 (Store Management)**：
   - 功能：管理多个电商平台的店铺信息，包括店铺ID、名称、用户名、密码、令牌等
   - 关键文件：[routes/phh_store/routes.py](routes/phh_store/routes.py)
   - 数据库表：`stores` (在[models.py](models.py:115-255)中定义)
   - 主要操作：添加、编辑、删除店铺；自动刷新令牌

2. **产品管理 (Product Management)**：
   - 功能：管理产品信息，支持多语言标题和描述，产品状态跟踪
   - 关键文件：[routes/product_list.py](routes/product_list.py), [routes/product_publish.py](routes/product_publish.py)
   - 数据库表：
     - `unified_list_products` (在[models.py](models.py:20-113)中定义)：产品列表，支持多语言字段
     - `unified_storeoffer_products` (在[models.py](models.py:345-373)中定义)：店铺报价产品
   - 主要操作：
     - 产品列表：分页显示、搜索、按状态和店铺筛选
     - 产品编辑：修改产品信息，包括标题、描述、图片、尺寸、重量等
     - 产品发布：将产品数据发布到电商平台

3. **任务管理 (Task Management)**：
   - 功能：处理批量任务，如产品上传、翻译、数据同步
   - 关键文件：[routes/upload_task.py](routes/upload_task.py), [routes/task_center/](routes/task_center/)
   - 数据库表：
     - `upload_tasks` (在[models.py](models.py:257-294)中定义)：上传任务记录
     - `upload_task_details` (在[models.py](models.py:296-332)中定义)：上传任务详情
     - `task_center` (在[models.py](models.py:481-561)中定义)：任务中心配置
   - 主要操作：创建任务、查看任务状态、定时执行任务

4. **翻译服务 (Translation Service)**：
   - 功能：为产品标题和描述提供多语言翻译，支持多个翻译API
   - 关键文件：[translate/](translate/)目录下多个API接口文件
   - 数据库表：`system_settings` (在[models.py](models.py:376-449)中定义)用于存储翻译配置
   - 主要操作：配置翻译服务、批量翻译产品内容、处理翻译失败重试

5. **XML数据处理 (XML Processing)**：
   - 功能：处理产品数据的XML格式转换和导入
   - 关键文件：[routes/xml_task/views.py](routes/xml_task/views.py)
   - 数据库表：`xml_conversion_task` (在[models.py](models.py:642-668)中定义)
   - 主要操作：上传XML文件、转换为内部数据格式、生成XML输出

6. **系统设置 (System Settings)**：
   - 功能：配置系统参数，如代理设置、任务过期时间、翻译服务选择等
   - 关键文件：[routes/settings/system.py](routes/settings/system.py), [routes/settings/multi_model.py](routes/settings/multi_model.py)
   - 数据库表：
     - `system_settings` (在[models.py](models.py:376-449)中定义)：系统设置键值对
     - `multi_model_config` (在[models.py](models.py:451-479)中定义)：多模型配置
   - 主要操作：查看和修改系统配置、配置多模型翻译服务

### 数据库模型详细说明
以下是主要数据库表的结构和字段说明：

1. **stores** ([models.py](models.py:115-255))
   - `id`: 主键
   - `seller_id`: 店铺唯一标识符
   - `name`: 店铺名称
   - `username`: 登录用户名
   - `password`: 登录密码
   - `table_name`: 关联的产品表名（现已统一）
   - `status`: 店铺状态（默认'active'）
   - `token`: API访问令牌
   - `token_expires_at`: 令牌过期时间
   - `token_refresh_attempts`: 令牌刷新尝试次数
   - `last_refresh_error`: 最后刷新错误信息

2. **unified_list_products** ([models.py](models.py:20-113))
   - `id`: 主键
   - `seller_id`: 关联店铺ID
   - `sku`: 产品SKU
   - `ean`: 产品EAN码
   - `category_id`: 分类ID
   - `title_en`, `title_cn`, `title_lt`, `title_lv`, `title_et`, `title_fi`: 多语言标题
   - `description_en`, `description_lt`, `description_lv`, `description_et`, `description_fi`: 多语言描述
   - `image_url1` to `image_url5`: 产品图片URL
   - `video_url`: 产品视频URL
   - `status`: 产品状态（枚举值，如'pending_edit', 'upload_successful'等）
   - `package_length`, `package_width`, `package_height`, `package_weight`: 包装尺寸和重量
   - `created_at`, `publish_time`: 创建和发布时间
   - `platform_message`, `notes`: 平台消息和备注

3. **unified_storeoffer_products** ([models.py](models.py:345-373))
   - `id`: 主键
   - `seller_id`: 关联店铺ID
   - `external_id`, `pigu_external_id`: 外部ID
   - `app_name`: 应用/国家名称
   - `title`: 产品标题
   - `insult_price`, `buybox_price`, `relevant_market_price`: 价格信息
   - `sku`, `ean`: 产品标识
   - `delivery_hours`: 发货时间
   - `amount`: 库存数量
   - `sell_price`, `sell_price_after_discount`: 销售价格和折扣后价格
   - `status`: 状态
   - `base_price`: 基准价格

4. **upload_tasks** 和 **upload_task_details** ([models.py](models.py:257-332))
   - 记录产品上传任务和每个产品的处理状态
   - 包含任务ID、执行ID、店铺ID、任务类型、产品数量、成功/失败统计等信息

5. **task_center** ([models.py](models.py:481-561))
   - 管理定时任务配置
   - 包含任务类型（翻译、发布）、批次大小、执行间隔、优先级等

### 关键API端点
以下是系统中的主要API端点，基于对代码的分析：
1. **产品相关**：
   - GET `/api/list-products` ([app.py](app.py:133-220))：获取产品列表，支持分页和搜索
   - POST `/api/offer_product/sync`：同步产品数据
   - POST `/api/offer_product/editor`：编辑产品信息

2. **店铺相关**：
   - GET `/api/phh_store/stores`：获取店铺列表
   - POST `/api/phh_store/add`：添加新店铺
   - POST `/api/phh_store/update`：更新店铺信息

3. **任务相关**：
   - POST `/api/upload-tasks`：创建上传任务
   - GET `/api/upload-tasks`：获取任务列表
   - GET `/task-center/list`：获取任务中心任务列表
   - POST `/task-center/create`：创建新任务

4. **系统设置**：
   - GET `/api/settings/system`：获取系统设置
   - POST `/api/settings/system`：更新系统设置
   - GET `/api/settings/multi-model`：获取多模型配置
   - POST `/api/settings/multi-model`：更新多模型配置

### 前端页面和功能
1. **首页 (index.html)**：
   - 显示仪表盘统计信息（产品总数、活跃产品等）
   - 提供产品列表视图，支持筛选和搜索
   - 包含批量操作功能，如同步和编辑

2. **店铺管理 (store_management.html)**：
   - 列出所有店铺及其状态
   - 允许添加、编辑和删除店铺

3. **产品发布 (product_publish.html)**：
   - 提供产品上传和发布到平台的界面
   - 显示发布状态和结果

4. **自动定价 (auto_pricing.html)**：
   - 提供价格调整和自动定价规则配置

5. **任务中心 (task-center.html)**：
   - 显示定时任务配置和状态
   - 允许创建和修改任务

6. **系统设置 (system.html)**：
   - 提供系统参数配置界面，包括代理和翻译设置

### 代码逻辑和流程
1. **应用启动 ([app.py](app.py:248-291))**：
   - 检查端口是否被占用
   - 创建必要的目录和数据库文件
   - 初始化数据库表和系统设置
   - 启动Flask服务器和任务调度器
   - 自动在浏览器中打开应用

2. **令牌管理 ([models.py](models.py:141-226))**：
   - 自动检查令牌是否即将过期
   - 在后台异步刷新令牌
   - 处理刷新失败情况，记录错误信息

3. **产品上传流程**：
   - 用户创建上传任务
   - 系统记录任务和产品详情
   - 定时任务检查状态并更新
   - 显示最终结果给用户

4. **翻译流程**：
   - 用户选择需要翻译的产品
   - 系统根据配置选择翻译API
   - 批量发送翻译请求并处理结果
   - 支持失败重试机制

## 重构目标架构
- **前端**：Next.js (React框架，支持SSR和静态优化)
- **后端**：Node.js with Express (轻量级API框架)
- **数据库**：MySQL (关系型数据库，提供更好的性能和扩展性)
- **部署**：可考虑使用Vercel部署前端，Heroku或AWS部署后端

## 重构策略
考虑到项目的复杂性，建议采用分阶段重构策略，逐步替换现有组件，确保每个阶段系统都能正常运行。

### 阶段一：前端迁移到Next.js
**目标**：将前端从Jinja2模板迁移到Next.js，保持后端不变，通过API与现有Flask后端通信。

#### 步骤：
1. **设置Next.js项目**：
   - 创建新的Next.js项目：`npx create-next-app@latest phh-web`
   - 选择TypeScript、Tailwind CSS和App Router
   - 将项目放在与现有项目平行的目录：`/phh-web`

2. **API集成**：
   - 在Next.js中配置API代理，指向现有Flask后端（默认端口5000）
   - 使用`next.config.js`配置代理：
     ```javascript
     module.exports = {
       async rewrites() {
         return [
           {
             source: '/api/:path*',
             destination: 'http://localhost:5000/api/:path*',
           },
         ]
       },
     }
     ```

3. **页面迁移**：
   - 逐个页面迁移现有模板到Next.js组件
   - 优先级：首页 -> 店铺管理 -> 产品发布 -> 其他页面
   - 将Jinja2模板转换为React组件，保持UI一致性
   - 使用`getServerSideProps`或`getStaticProps`获取数据

4. **状态管理**：
   - 使用React Context或Redux管理全局状态（如当前选择的店铺）
   - 将现有JavaScript逻辑转换为React钩子

5. **测试与验证**：
   - 确保每个页面在新前端中的功能与旧前端一致
   - 测试API调用是否正常返回数据

#### 时间估计：2-3周
#### 风险与缓解措施：
- 现有Flask API可能不完全适合前端需求，需要调整
- 解决方案：在Flask中添加必要的API端点作为过渡

### 阶段二：数据库迁移到MySQL
**目标**：将数据库从SQLite迁移到MySQL，提高性能和扩展性，同时保持Flask后端。

#### 步骤：
1. **MySQL设置**：
   - 安装MySQL服务器并创建数据库：`phh_db`
   - 更新Flask配置中的数据库URI：
     ```python
     app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://username:password@localhost/phh_db'
     ```

2. **模型调整**：
   - 检查现有模型定义，确保与MySQL兼容
   - 调整数据类型（如Text字段长度限制）
   - 使用Alembic生成新的迁移脚本：`alembic revision --autogenerate -m "migrate to mysql"`

3. **数据迁移**：
   - 导出SQLite数据：使用脚本或工具导出所有表数据
   - 导入到MySQL：调整字段类型后导入数据
   - 验证数据完整性：检查记录数和关键数据是否一致

4. **测试与验证**：
   - 运行完整测试套件，确保所有功能正常
   - 监控性能变化，优化查询如果需要

#### 时间估计：1周
#### 风险与缓解措施：
- 数据类型不匹配可能导致数据丢失
- 解决方案：在迁移前后备份数据，逐步验证每张表的数据

### 阶段三：后端迁移到Node.js
**目标**：将后端从Flask迁移到Node.js，完成技术栈的统一。

#### 步骤：
1. **设置Node.js项目**：
   - 创建新的Express项目：`npx express-generator phh-api`
   - 安装必要依赖：`npm install mysql2 sequelize dotenv cors`
   - 将项目放在与Next.js项目平行的目录：`/phh-api`

2. **数据库模型迁移**：
   - 使用Sequelize定义与现有SQLAlchemy模型相同的结构
   - 确保字段类型、关系和约束一致
   - 示例：
     ```javascript
     const { Sequelize, DataTypes } = require('sequelize');
     const sequelize = new Sequelize('mysql://username:password@localhost/phh_db');

     const Store = sequelize.define('Store', {
       seller_id: {
         type: DataTypes.STRING(100),
         allowNull: false,
         unique: true
       },
       name: {
         type: DataTypes.STRING(100),
         allowNull: false
       },
       // ... 其他字段
     });
     ```

3. **API端点迁移**：
   - 逐个迁移现有Flask路由到Express路由
   - 保持API接口一致，确保Next.js前端无需修改
   - 优先级：身份验证 -> 店铺管理 -> 产品管理 -> 任务处理 -> 其他API
   - 示例：
     ```javascript
     const express = require('express');
     const router = express.Router();

     router.get('/api/stores', async (req, res) => {
       const stores = await Store.findAll();
       res.json(stores);
     });
     ```

4. **业务逻辑迁移**：
   - 将Flask中的业务逻辑转换为JavaScript
   - 包括令牌刷新、产品上传、翻译服务等复杂逻辑
   - 对于外部API调用，使用`axios`替换`requests`

5. **任务调度**：
   - 使用`node-cron`或`bull`替换APScheduler
   - 迁移现有定时任务逻辑

6. **测试与验证**：
   - 使用Jest或Mocha进行单元测试
   - 进行集成测试，确保API与前端和数据库的交互正常
   - 逐步切换流量到新后端，监控错误和性能

#### 时间估计：3-4周
#### 风险与缓解措施：
- 复杂业务逻辑迁移可能引入错误
- 解决方案：采用测试驱动开发，优先迁移核心功能，逐步验证

### 阶段四：优化与现代化
**目标**：利用新架构的优势进行优化，添加新功能。

#### 步骤：
1. **前端优化**：
   - 利用Next.js的图像优化、代码分割等功能提高性能
   - 实现静态页面生成（SSG）用于不经常变化的页面

2. **后端优化**：
   - 实现API缓存减少数据库负载
   - 使用PM2进行进程管理和负载均衡

3. **新功能**：
   - 添加实时通知功能（使用WebSocket或SSE）
   - 增强数据分析和报告功能

#### 时间估计：持续进行
#### 风险与缓解措施：
- 新功能可能影响系统稳定性
- 解决方案：使用特性标志控制新功能发布，逐步推广

## 总结
此重构计划分为四个阶段，预计总时间为6-8周。分阶段方法确保了在重构过程中系统始终可用，降低了整体风险。建议组建一个小型团队（2-3名开发人员）来执行此计划，确保每个阶段完成后进行充分测试。本文档提供了对现有项目的详细描述，可作为创新重构的参考基础。

## 附录
### 推荐工具
- **前端**：VS Code, ESLint, Prettier
- **后端**：VS Code, Nodemon for development
- **数据库**：MySQL Workbench for management