import asyncio
import aiohttp
import logging
import time
from typing import List, Dict, Optional
from models import SystemSettings, TokenUsage, UnifiedListProducts

logger = logging.getLogger(__name__)

class MTranServer:
    def __init__(self):
        """初始化翻译器"""
        # 获取Mtran Server API地址
        self.base_url = SystemSettings.get_setting('translation_api_key')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        
    async def translate_text(self, text: str, source_lang: str = "en", target_lang: str = "lt", content_type: str = "title") -> Dict:
        """翻译单个文本到指定语言
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            content_type: 内容类型，'title' 或 'description'
        """
        try:
            # 确保语言代码是小写的
            source_lang = source_lang.lower()
            target_lang = target_lang.lower()
            
            # 如果是标题且目标语言是et，使用中转翻译
            if target_lang == 'et' and source_lang == 'en' and content_type == 'title':
                # 第一步：翻译成中文
                zh_request_data = {
                    "from": source_lang,
                    "to": "zh",
                    "text": text
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.base_url}/translate",
                        json=zh_request_data
                    ) as response:
                        if response.status == 200:
                            zh_result = await response.json()
                            # logger.info(f"中文翻译结果: {zh_result}")
                            
                            if not isinstance(zh_result, dict) or 'result' not in zh_result:
                                raise Exception("中文翻译返回格式异常")
                            
                            zh_text = zh_result['result'].replace(' ', '')
                            logger.info(f"中文翻译结果: {zh_text}")
                            
                            et_request_data = {
                                "from": "zh",
                                "to": "et",
                                "text": zh_text
                            }
                            # logger.info(f"ET翻译请求内容: {et_request_data}")
                            async with session.post(
                                f"{self.base_url}/translate",
                                json=et_request_data
                            ) as et_response:
                                if et_response.status == 200:
                                    et_result = await et_response.json()
                                    # logger.info(f"ET翻译结果: {et_result}")
                                    
                                    if not isinstance(et_result, dict) or 'result' not in et_result:
                                        raise Exception("ET翻译返回格式异常")
                                    
                                    translated_text = et_result['result']
                                    if not translated_text or translated_text == '[object Object]':
                                        raise Exception("ET翻译结果异常")
                                    
                                    return {
                                        'success': True,
                                        'text': translated_text
                                    }
                                else:
                                    error_text = await et_response.text()
                                    raise Exception(f"ET翻译请求失败: {error_text}")
                        else:
                            error_text = await response.text()
                            raise Exception(f"中文翻译请求失败: {error_text}")
            
            # 其他情况直接翻译
            request_data = {
                "from": source_lang,
                "to": target_lang,
                "text": text
            }
            # logger.info(f"直接翻译请求内容[{content_type}]: {request_data}")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/translate",
                    json=request_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        # 打印原始返回结果
                        # logger.info(f"翻译返回结果: {result}")
                        
                        # 确保返回的是字符串类型
                        if isinstance(result, dict) and 'result' in result:
                            translated_text = result['result']
                            if not translated_text or translated_text == '[object Object]':
                                raise Exception(f"翻译结果异常")
    
                        else:
                            # logger.warning(f"翻译返回格式异常: {result}")
                            translated_text = text
                        
                        return {
                            'success': True,
                            'text': translated_text
                        }
                    else:
                        error_text = await response.text()
                        # logger.error(f"翻译请求失败: {error_text}")
                        return {
                            'success': False,
                            'error': f'翻译请求失败: {error_text}'
                        }
        except Exception as e:
            # logger.error(f"翻译失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def translate(self, text: str, source_lang: str = "en", target_lang: str = "lt", content_type: str = "title") -> Dict:
        """同步翻译方法"""
        if not text:
            return {
                'success': False,
                'error': '翻译文本不能为空'
            }
        
        # 创建新的事件循环
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        
        try:
            result = new_loop.run_until_complete(
                self.translate_text(text, source_lang, target_lang, content_type)
            )
            return result
        finally:
            new_loop.close()

    async def translate_product_async(self, text, source_lang="en", target_langs=["lt", "lv", "et", "fi"], content_type="title"):
        """异步翻译产品的标题或描述到四个目标语言
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_langs: 目标语言代码列表
            content_type: 内容类型，'title' 或 'description'
        Returns:
            dict: 各个目标语言的翻译结果
        """
        translations = {lang.upper(): None for lang in target_langs}
        failed_langs = []

        # 为每个目标语言创建翻译任务
        tasks = []
        for lang in target_langs:
            tasks.append(self.translate_text(text, source_lang, lang, content_type))
        
        # 并行执行所有翻译任务
        results = await asyncio.gather(*tasks)
        
        # 处理结果
        for i, lang in enumerate(target_langs):
            result = results[i]
            if result['success']:
                translations[lang.upper()] = result['text']
            else:
                failed_langs.append(lang)
                
        # 重试失败的语言
        for lang in failed_langs:
            for retry in range(self.retry_count):
                await asyncio.sleep(self.retry_interval)
                result = await self.translate_text(text, source_lang, lang, content_type)
                if result['success']:
                    translations[lang.upper()] = result['text']
                    break
        
        return translations

    async def translate_product_full_async(self, title, description, source_lang="en"):
        """异步翻译产品的标题和描述到多个语言
        Args:
            title: 产品标题
            description: 产品描述
            source_lang: 源语言代码
        Returns:
            dict: 包含多语言翻译结果的字典
        """
        # 翻译标题到多个语言
        title_translations = await self.translate_product_async(title, source_lang, content_type="title")
        await asyncio.sleep(0.5)
        
        # 翻译描述到多个语言
        desc_translations = await self.translate_product_async(description, source_lang, content_type="description")
        
        return {
            'title': title_translations,
            'description': desc_translations
        }

    async def translate_batch_products_async(self, products_data):
        """批量翻译多个产品的标题和描述
        Args:
            products_data: 包含产品信息的列表，每项包含sku、title和description
        Returns:
            list: 每个产品的翻译结果列表
        """
        start_time = time.time()

        # 存储所有产品的翻译结果
        results = []
        success_count = 0
        failed_count = 0
        
        # 创建所有产品的翻译任务
        tasks = []
        for product in products_data:
            sku = product.get('sku', 'unknown')
            title = product.get('title', '')
            description = product.get('description', '')
            
            # 检查输入数据
            if not title or not isinstance(title, str):
                results.append({
                    'sku': sku,
                    'error': '标题为空或格式错误',
                    'title': None,
                    'description': None
                })
                failed_count += 1
                continue
                
            # 创建产品翻译任务
            task = self.translate_product_full_async(
                title=title,
                description=description,
                source_lang="EN"
            )
            tasks.append((sku, task))
        
        # 并行执行所有产品的翻译任务
        for sku, task in tasks:
            try:
                # 执行翻译
                translations = await task
                
                # 添加结果
                results.append({
                    'sku': sku,
                    'title': translations.get('title', {}),
                    'description': translations.get('description', {})
                })
                success_count += 1
                
            except Exception as e:
                results.append({
                    'sku': sku,
                    'error': str(e),
                    'title': None,
                    'description': None
                })
                failed_count += 1
        
        # 记录统计信息
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"=== 批量翻译统计 ===")
        logger.info(f"总产品数: {len(products_data)}")
        logger.info(f"成功数: {success_count}")
        logger.info(f"失败数: {failed_count}")
        logger.info(f"耗时: {duration:.2f}秒")
        logger.info(f"平均每个产品耗时: {(duration/len(products_data) if products_data else 0):.2f}秒")
        
        return results

if __name__ == "__main__":
    translator = MTranServer()