<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PHH商品在线管理{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    {% block styles %}{% endblock %}
</head>
<body class="gradient-bg min-h-screen">

    <!-- 主题切换按钮 -->
    <button class="theme-switch p-2 rounded-full bg-white shadow-md" onclick="toggleTheme()">
        <i class="fas fa-moon"></i>
    </button>

    <!-- 侧边导航栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2 class="text-xl font-semibold text-gray-800">PHH 商品管理</h2>
        </div>
        <div class="mt-4">
            <a href="/product-publish" class="nav-item {{ 'active' if active_page == 'product-publish' else '' }}">
                <span class="nav-icon"><i class="fas fa-upload"></i></span>
                产品刊登
            </a>
            <a href="/store-management" class="nav-item {{ 'active' if active_page == 'store-management' else '' }}">
                <span class="nav-icon"><i class="fas fa-store"></i></span>
                店铺管理
            </a>
            <a href="/task-center" class="nav-item {{ 'active' if active_page == 'task-center' else '' }}">
                <span class="nav-icon"><i class="fas fa-tasks"></i></span>
                任务中心
            </a>
            <a href="{{ url_for('system.system_settings') }}" class="nav-item {{ 'active' if active_page == 'system-settings' else '' }}">
                <span class="nav-icon"><i class="fas fa-cog"></i></span>
                系统设置
            </a>
        </div>
    </nav>


    <div class="main-content">
        {% block content %}{% endblock %}
    </div>
    
    <!-- 基础脚本 -->
    <script src="{{ url_for('static', filename='js/notification.js') }}" defer></script>
    
    <!-- 页面特定脚本 -->
    {% block scripts %}{% endblock %}

    <!-- 主题切换脚本 -->
    <script>
        // 等待通知组件加载完成
        document.addEventListener('DOMContentLoaded', function() {
            function toggleTheme() {
                const body = document.body;
                const button = document.querySelector('.theme-switch i');
                
                if (body.classList.contains('dark')) {
                    body.classList.remove('dark');
                    button.className = 'fas fa-moon';
                } else {
                    body.classList.add('dark');
                    button.className = 'fas fa-sun';
                }
            }
        });
    </script>

    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f6f8fd 0%, #f1f4f9 100%);
        }
        .sidebar {
            width: 220px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 40;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: #1a1a1a;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .nav-item:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        .nav-item.active {
            background: rgba(0, 122, 255, 0.1);
            color: #007AFF;
        }

        .nav-icon {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        .main-content {
            margin-left: 220px;
            transition: all 0.3s ease;
            padding: 1rem;
            width: calc(100% - 220px);
        }

        .theme-switch {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 30;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }
    </style>
</body>
</html> 