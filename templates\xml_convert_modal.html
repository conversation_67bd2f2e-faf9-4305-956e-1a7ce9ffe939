<!-- XML转换查看模态框 -->
<div id="xmlConvertModal" class="fixed inset-0 z-40 hidden">
    <!-- 背景遮罩层 -->
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <!-- 容器 -->
    <div class="fixed inset-0 flex items-center justify-center" style="padding: 1rem;">
        <!-- 内容卡片 - 使用固定像素值和最小尺寸 -->
        <div class="bg-white rounded-lg shadow-xl flex flex-col overflow-hidden" 
             style="width: 1200px; height: 800px; min-width: 1200px; min-height: 800px;">
            <!-- 头部 -->
            <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
                <h2 class="text-base font-medium text-gray-900">XML转换任务</h2>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="xmlConvertModal.toggle()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 筛选区域 -->
            <div class="p-3 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <!-- 店铺筛选 -->
                    <div class="flex-1">
                        <select class="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="xmlStoreFilter">
                            <!-- 将由JavaScript动态填充 -->
                        </select>
                    </div>
                    <!-- 时间范围 -->
                    <div class="flex-1 flex space-x-2">
                        <input type="date" class="flex-1 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="xmlDateStart">
                        <span class="text-gray-500 text-sm">至</span>
                        <input type="date" class="flex-1 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="xmlDateEnd">
                    </div>
                    <!-- 状态筛选 -->
                    <div class="flex-1">
                        <select class="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" id="xmlStatusFilter">
                            <option value="">所有状态</option>
                            <option value="completed">已完成</option>
                            <option value="processing">处理中</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <!-- 搜索按钮 -->
                    <button type="button" class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700" onclick="xmlConvertModal.handleSearch()">
                        <i class="fas fa-search mr-1"></i>搜索
                    </button>
                    <!-- 重置按钮 -->
                    <button type="button" class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50" onclick="xmlConvertModal.handleReset()">
                        <i class="fas fa-redo mr-1"></i>重置
                    </button>
                    <!-- 新建转换任务按钮 -->
                    <button type="button" class="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700" onclick="xmlConvertModal.createNewTask()">
                        <i class="fas fa-plus mr-1"></i>新建转换
                    </button>
                </div>
            </div>

            <!-- 表格区域 -->
            <div class="flex-1 overflow-y-auto overflow-x-hidden">
                <table class="w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0">
                        <tr>
                            <!-- 任务ID -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 120px;">
                                任务ID
                            </th>
                            <!-- 店铺名称 -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 140px;">
                                店铺名称
                            </th>
                            <!-- 创建时间 -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 160px;">
                                创建时间
                            </th>
                            <!-- 总产品数 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 100px;">
                                产品数
                            </th>
                            <!-- 状态 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 100px;">
                                状态
                            </th>
                            <!-- 文件名 -->
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500" style="width: 200px;">
                                文件名
                            </th>
                            <!-- 操作 -->
                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500" style="width: 180px;">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200 text-sm" id="xmlTaskTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控制 -->
            <div class="px-4 py-3 border-t border-gray-200 bg-white">
                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-700" id="xmlPageInfo">
                        显示 0 到 0 条，共 0 条
                    </div>
                    <div class="flex space-x-1" id="xmlPagination">
                        <button id="xmlPrevPage" class="px-2 py-1 border border-gray-300 rounded-md text-xs" onclick="xmlConvertModal.changePage('prev')">
                            上一页
                        </button>
                        <!-- 页码按钮将通过JavaScript动态生成 -->
                        <button id="xmlNextPage" class="px-2 py-1 border border-gray-300 rounded-md text-xs" onclick="xmlConvertModal.changePage('next')">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建XML转换任务模态框 -->
<div id="createXmlTaskModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
    <div class="fixed inset-0 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-[500px] flex flex-col overflow-hidden m-4">
            <!-- 头部 -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 flex-shrink-0">
                <h2 class="text-lg font-medium text-gray-900">创建XML转换任务</h2>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="xmlConvertModal.toggleCreateTaskModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 内容区域 -->
            <div class="p-6">
                <form id="createXmlTaskForm">
                    <!-- 店铺选择 -->
                    <div class="mb-4">
                        <label for="xmlTaskStore" class="block text-sm font-medium text-gray-700 mb-1">选择店铺</label>
                        <div class="flex">
                            <select id="xmlTaskStore" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" onchange="xmlConvertModal.updateProductCount()">
                                <!-- 将由JavaScript动态填充 -->
                            </select>
                            <div class="ml-2 text-sm text-gray-500 flex items-center">
                                可转换: <span id="availableProductCount" class="font-medium text-blue-600">0</span> 个
                            </div>
                        </div>
                    </div>
                    
                    <!-- 产品数量 -->
                    <div class="mb-4">
                        <label for="xmlProductCount" class="block text-sm font-medium text-gray-700 mb-1">转换产品数量</label>
                        <input type="number" id="xmlProductCount" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" min="1" value="10">
                        <p class="mt-1 text-sm text-gray-500">设置要转换的产品数量，最多不超过可转换数量</p>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="mt-6 flex justify-end">
                        <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md mr-2 hover:bg-gray-300" onclick="xmlConvertModal.toggleCreateTaskModal()">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" onclick="xmlConvertModal.submitCreateTask()">
                            开始转换
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 使用命名空间模式避免全局变量污染
const xmlConvertModal = {
    currentPage: 1,
    itemsPerPage: 30,  // 每页显示数量
    
    // 获取XML转换任务列表数据
    async fetchTasks(page = 1, filters = {}) {
        try {
            const queryParams = new URLSearchParams({
                page: page.toString(),
                per_page: this.itemsPerPage.toString()
            });
            
            // 添加筛选条件
            if (filters.seller_id) queryParams.append('seller_id', filters.seller_id);
            if (filters.date_start) queryParams.append('date_start', filters.date_start);
            if (filters.date_end) queryParams.append('date_end', filters.date_end);
            if (filters.status) queryParams.append('status', filters.status);
            
            const response = await fetch(`/api/xml-tasks/?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            if (data.code === 200) {
                return data.data;
            } else {
                throw new Error(data.message || '获取数据失败');
            }
        } catch (error) {
            console.error('获取XML任务列表失败:', error);
            return null;
        }
    },
    
    // 获取店铺可转换产品数量
    async fetchAvailableProductCount(sellerId) {
        try {
            const response = await fetch(`/api/xml-tasks/available-products/?seller_id=${sellerId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            if (data.code === 200) {
                return data.data.count || 0;
            } else {
                throw new Error(data.message || '获取可转换产品数量失败');
            }
        } catch (error) {
            console.error('获取可转换产品数量失败:', error);
            return 0;
        }
    },
    
    // 更新可转换产品数量
    async updateProductCount() {
        const sellerId = document.getElementById('xmlTaskStore').value;
        if (!sellerId) {
            document.getElementById('availableProductCount').textContent = "0";
            return;
        }
        
        const count = await this.fetchAvailableProductCount(sellerId);
        document.getElementById('availableProductCount').textContent = count;
        
        // 更新产品数量输入框的最大值
        const productCountInput = document.getElementById('xmlProductCount');
        productCountInput.max = count;
        if (parseInt(productCountInput.value) > count) {
            productCountInput.value = count;
        }
    },
    
    // 获取店铺名称的函数
    getStoreName(sellerId) {
        const storeFilter = document.getElementById('xmlStoreFilter');
        // 将 seller_id 转换为字符串并进行比较
        const option = Array.from(storeFilter.options).find(opt => opt.value === sellerId.toString());
        return option ? option.textContent : sellerId.toString();
    },
    
    // 渲染表格数据
    async renderTable() {
        const tableBody = document.getElementById('xmlTaskTableBody');
        const filters = this.getFilters();
        
        // 显示加载状态
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4">加载中...</td></tr>';
        
        try {
            const data = await this.fetchTasks(this.currentPage, filters);
            if (!data || !data.items) {
                throw new Error('无效的数据格式');
            }
            
            if (data.items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4 text-gray-500">暂无数据</td></tr>';
                return;
            }

            tableBody.innerHTML = '';
            data.items.forEach(item => {
                // 根据状态设置样式
                const statusClass = 
                    item.status === 'completed' ? 'bg-green-100 text-green-800' : 
                    item.status === 'processing' ? 'bg-yellow-100 text-yellow-800' : 
                    'bg-red-100 text-red-800';
                
                const statusText = 
                    item.status === 'completed' ? '已完成' : 
                    item.status === 'processing' ? '处理中' : 
                    '失败';
                
                const row = `
                    <tr class="h-[50px] hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap">
                            ${item.task_id}
                            <button class="ml-2 text-blue-600 hover:text-blue-900" onclick="xmlConvertModal.checkBarcodes('${item.task_id}')">
                                <i class="fas fa-search"></i>
                            </button>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">${this.getStoreName(item.seller_id)}</td>
                        <td class="px-4 py-3 whitespace-nowrap">${item.create_time}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">${item.product_count}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-left">${item.file_name || '-'}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-center">
                            ${item.status === 'completed' ? 
                                `<button class="text-blue-600 hover:text-blue-900 mr-2" onclick="xmlConvertModal.downloadFile('${item.task_id}')">
                                    <i class="fas fa-download mr-1"></i>下载
                                </button>` : ''
                            }
                            <button class="text-red-600 hover:text-red-900" onclick="xmlConvertModal.deleteTask('${item.task_id}')">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
            
            this.updatePagination(data);
        } catch (error) {
            console.error('渲染表格失败:', error);
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4 text-red-600">加载失败: ' + error.message + '</td></tr>';
        }
    },
    
    // 获取筛选条件
    getFilters() {
        const storeFilter = document.getElementById('xmlStoreFilter').value;
        const dateStart = document.getElementById('xmlDateStart').value;
        const dateEnd = document.getElementById('xmlDateEnd').value;
        const statusFilter = document.getElementById('xmlStatusFilter').value;
        
        const filters = {};
        if (storeFilter) filters.seller_id = storeFilter;
        if (dateStart) filters.date_start = dateStart;
        if (dateEnd) filters.date_end = dateEnd;
        if (statusFilter) filters.status = statusFilter;
        
        return filters;
    },
    
    // 更新分页信息和按钮状态
    updatePagination(data) {
        const pageInfo = document.getElementById('xmlPageInfo');
        const pagination = document.getElementById('xmlPagination');
        const prevButton = document.getElementById('xmlPrevPage');
        const nextButton = document.getElementById('xmlNextPage');
        
        const start = (data.page - 1) * data.per_page + 1;
        const end = Math.min(data.page * data.per_page, data.total);
        
        // 更新显示信息
        pageInfo.textContent = `显示 ${start} 到 ${end} 条，共 ${data.total} 条`;
        
        // 更新上一页/下一页按钮状态
        prevButton.disabled = data.page === 1;
        nextButton.disabled = data.page === data.pages;
        prevButton.classList.toggle('opacity-50', data.page === 1);
        nextButton.classList.toggle('opacity-50', data.page === data.pages);
        
        // 重新生成页码按钮
        const pageButtons = Array.from(pagination.querySelectorAll('button')).filter(
            button => !button.id.includes('xmlPrevPage') && !button.id.includes('xmlNextPage')
        );
        pageButtons.forEach(button => button.remove());
        
        // 在上一页和下一页按钮之间插入页码按钮
        const nextPageButton = document.getElementById('xmlNextPage');
        for (let i = 1; i <= data.pages; i++) {
            const button = document.createElement('button');
            button.className = `px-2 py-1 border border-gray-300 rounded-md text-xs ${
                i === data.page ? 'bg-blue-50 text-blue-600' : ''
            }`;
            button.textContent = i;
            button.onclick = () => this.changePage(i);
            pagination.insertBefore(button, nextPageButton);
        }
    },
    
    // 切换页面
    async changePage(page) {
        if (page === 'prev' && this.currentPage > 1) {
            this.currentPage--;
        } else if (page === 'next') {
            this.currentPage++;
        } else if (typeof page === 'number') {
            this.currentPage = page;
        }
        await this.renderTable();
    },
    
    // 切换模态框显示状态
    toggle() {
        const modal = document.getElementById('xmlConvertModal');
        if (modal) {
            modal.classList.toggle('hidden');
        }
    },
    
    // 切换创建任务模态框
    toggleCreateTaskModal() {
        const modal = document.getElementById('createXmlTaskModal');
        if (modal) {
            modal.classList.toggle('hidden');
            if (!modal.classList.contains('hidden')) {
                this.updateProductCount();
            }
        }
    },
    
    // 创建新转换任务
    createNewTask() {
        this.toggleCreateTaskModal();
    },
    
    // 提交创建任务表单
    async submitCreateTask() {
        try {
            const sellerId = document.getElementById('xmlTaskStore').value;
            const productCount = document.getElementById('xmlProductCount').value;
            
            if (!sellerId) {
                notification.error('请选择店铺');
                return;
            }
            
            if (!productCount || productCount <= 0) {
                notification.error('请输入有效的产品数量');
                return;
            }
            
            // 禁用按钮，显示加载状态
            const submitButton = document.querySelector('#createXmlTaskForm button[type="button"].bg-blue-600');
            const originalText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>处理中...';
            
            const response = await fetch('/api/xml-tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    seller_id: sellerId,
                    product_count: parseInt(productCount)
                })
            });
            
            const data = await response.json();
            
            if (data.code === 200) {
                notification.success('创建XML转换任务成功');
                this.toggleCreateTaskModal();
                this.renderTable();
            } else {
                throw new Error(data.message || '创建XML转换任务失败');
            }
        } catch (error) {
            console.error('创建XML转换任务失败:', error);
            notification.error('创建XML转换任务失败: ' + error.message);
        } finally {
            // 恢复按钮状态
            const submitButton = document.querySelector('#createXmlTaskForm button[type="button"].bg-blue-600');
            submitButton.disabled = false;
            submitButton.innerHTML = '开始转换';
        }
    },
    
    // 下载XML文件
    async downloadFile(taskId) {
        try {
            window.location.href = `/api/xml-tasks/${taskId}/download`;
        } catch (error) {
            console.error('下载XML文件失败:', error);
            notification.error('下载XML文件失败: ' + error.message);
        }
    },
    
    // 删除任务
    async deleteTask(taskId) {
        try {
            // 显示确认对话框
            const confirmed = await notification.confirm(
                '确定要删除该任务吗？\n删除后将无法恢复！',
                '删除确认'
            );
            
            if (!confirmed) {
                return;
            }

            const response = await fetch(`/api/xml-tasks/${taskId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            
            if (data.code === 200) {
                // 删除成功，刷新表格
                notification.success('删除成功');
                await this.renderTable();
            } else {
                throw new Error(data.message || '删除失败');
            }
        } catch (error) {
            console.error('删除任务失败:', error);
            notification.error('删除失败: ' + error.message);
        }
    },
    
    // 处理搜索按钮点击
    handleSearch() {
        this.currentPage = 1;  // 重置到第一页
        this.renderTable();
    },
    
    // 处理重置按钮点击
    handleReset() {
        const today = new Date();
        const twoWeeksAgo = new Date(today.getTime() - (14 * 24 * 60 * 60 * 1000));
        
        document.getElementById('xmlStoreFilter').value = '';
        document.getElementById('xmlStatusFilter').value = '';
        document.getElementById('xmlDateStart').valueAsDate = twoWeeksAgo;
        document.getElementById('xmlDateEnd').valueAsDate = today;
        
        this.currentPage = 1;  // 重置到第一页
        this.renderTable();
    },
    
    // 添加检查EAN码的方法
    async checkBarcodes(taskId) {
        try {
            // 显示加载状态
            notification.info('正在查询EAN码状态...');
            
            const response = await fetch(`/api/xml-tasks/${taskId}/check-barcodes`);
            const data = await response.json();
            
            if (data.code === 200) {
                // 构建结果消息
                const resultMessage = `总EAN数量：${data.data.total_eans}
                                        已创建数量：${data.data.created_count}
                                        未创建数量：${data.data.not_created_count}
                                        频率限制：${data.data.rate_limit_count}
                                        错误数量：${data.data.error_count}
                                        总批次数：${data.data.batch_count}`;
                
                // 显示结果，设置显示时间为10秒
                notification.success(resultMessage, 10000);
            } else {
                throw new Error(data.message || '查询失败');
            }
        } catch (error) {
            console.error('查询EAN码状态失败:', error);
            notification.error('查询EAN码状态失败: ' + error.message);
        }
    },
    
    // 初始化
    init() {
        const today = new Date();
        const twoWeeksAgo = new Date(today.getTime() - (14 * 24 * 60 * 60 * 1000));
        
        document.getElementById('xmlDateStart').valueAsDate = twoWeeksAgo;
        document.getElementById('xmlDateEnd').valueAsDate = today;
        
        // 从产品发布页面获取店铺选择器
        const productPublishStoreSelect = document.querySelector('#importStoreSelect');
        if (productPublishStoreSelect) {
            const storeFilter = document.getElementById('xmlStoreFilter');
            const taskStoreSelect = document.getElementById('xmlTaskStore');
            
            // 清空现有选项
            storeFilter.innerHTML = '<option value="">所有店铺</option>';
            taskStoreSelect.innerHTML = '<option value="">请选择店铺</option>';
            
            // 复制店铺选项
            Array.from(productPublishStoreSelect.options).forEach(option => {
                if (option.value) { // 跳过空值选项
                    // 添加到筛选下拉框
                    const filterOption = document.createElement('option');
                    filterOption.value = option.value;
                    filterOption.textContent = option.textContent;
                    storeFilter.appendChild(filterOption);
                    
                    // 添加到创建任务下拉框
                    const taskOption = document.createElement('option');
                    taskOption.value = option.value;
                    taskOption.textContent = option.textContent;
                    taskStoreSelect.appendChild(taskOption);
                }
            });
        }
        
        this.renderTable();
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    xmlConvertModal.init();
});

// 提供全局访问函数
function toggleXmlConvertModal() {
    xmlConvertModal.toggle();
}
</script> 