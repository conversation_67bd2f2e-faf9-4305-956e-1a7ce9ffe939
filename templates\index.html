<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PHH商品在线管理{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css" rel="stylesheet">
    {% block styles %}
    <style>
        .dashboard-card {
            display: flex;
            flex-direction: column;
            height: 100%;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .fixed-header {
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem;
            background: white;
        }

        .scrollable-content {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }

        .fixed-footer {
            border-top: 1px solid #e5e7eb;
            padding: 0.5rem 1rem;
            background: white;
        }

        /* 调整表格样式 */
        .table-header {
            position: sticky;
            top: 0;
            background: #f8fafc;
            z-index: 10;
        }

        /* 调整分页样式 */
        .pagination-button {
            padding: 0.25rem 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            color: #374151;
            background: white;
            transition: all 0.2s;
        }

        .pagination-button:hover:not(:disabled) {
            background: #f3f4f6;
        }

        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .table-container {
            position: relative;
            height: calc(100vh - 320px);
            overflow-y: auto;
            border-radius: 10px;
            background: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #f6f8fd 0%, #f1f4f9 100%);
        }
        .pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 1rem;
        }
        .pagination-info {
            margin: 0 1rem;
        }
        .pagination-button {
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            background-color: white;
            color: #4a5568;
            cursor: pointer;
        }
        .pagination-button:hover {
            background-color: #f7fafc;
        }
        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .table-header {
            position: sticky;
            top: 0;
            background-color: #f8fafc;
            z-index: 10;
            border-bottom: 1px solid #e5e7eb;
        }
        .sort-icon {
            display: inline-block;
            margin-left: 0.5rem;
            cursor: pointer;
        }
        .sort-icon.active {
            color: #4299e1;
        }
        .column-toggle {
            position: relative;
        }
        .column-list {
            position: absolute;
            top: 100%;
            right: 0;
            width: 160px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            padding: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: none;
            margin-top: 0.5rem;
        }
        .column-list.show {
            display: block;
        }
        .column-list div {
            padding: 0.5rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .column-list div:hover {
            background-color: #f3f4f6;
        }
        .column-list input[type="checkbox"] {
            margin-right: 0.5rem;
        }
        .column-list::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 16px;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid white;
            z-index: 2;
        }
        .column-list::after {
            content: '';
            position: absolute;
            top: -9px;
            right: 16px;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #e2e8f0;
            z-index: 1;
        }
        .theme-switch {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 30;
        }
        .sidebar {
            width: 220px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 40;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: #1a1a1a;
            transition: all 0.2s ease;
        }

        .nav-item:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        .nav-item.active {
            background: rgba(0, 122, 255, 0.1);
            color: #007AFF;
        }

        .nav-icon {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        .main-content {
            margin-left: 220px;
            transition: all 0.3s ease;
            padding: 1rem;
            width: calc(100% - 220px);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        /* 添加复选框样式 */
        input[type="checkbox"] {
            cursor: pointer;
        }
        
        /* 调整表格行高度 */
        .table-container td {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }

        .table-header th {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
            font-size: 0.75rem;
            line-height: 1rem;
        }

        .table-container td {
            padding-top: 0.375rem;
            padding-bottom: 0.375rem;
        }

        /* 调整状态标签的样式 */
        .status-badge {
            padding: 0.125rem 0.5rem;
            font-size: 0.75rem;
            line-height: 1rem;
        }

        .pagination-container {
            position: fixed;
            bottom: 0;
            left: 220px;
            right: 0;
            background: white;
            padding: 0.75rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 20;
        }

        .pagination-left {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .pagination-right {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-size-select {
            padding: 0.25rem 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .pagination-container {
                left: 0;
            }
        }

        /* 调整分页按钮样式 */
        .pagination-button {
            padding: 0.375rem 0.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            background-color: white;
            color: #374151;
            font-size: 0.875rem;
            transition: all 0.2s;
        }
        
        .pagination-button:hover:not(:disabled) {
            background-color: #f3f4f6;
        }
        
        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 调整表格样式 */
        .table-header {
            position: sticky;
            top: 0;
            background-color: #f8fafc;
            z-index: 10;
            border-bottom: 1px solid #e5e7eb;
        }

        .main-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            margin-left: 220px; /* 侧边栏宽度 */
            padding: 1rem;
        }

        .content-wrapper {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-height: 0; /* 重要：允许内容区域收缩 */
        }

        .dashboard-card {
            display: flex;
            flex-direction: column;
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 0; /* 重要：允许卡片收缩 */
        }

        .fixed-header {
            background: white;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .table-container {
            flex: 1;
            overflow-y: auto;
            min-height: 0; /* 重要：允许表格区域收缩 */
            padding-bottom: 56px; /* 增加底部内边距，确保最后一行数据可见 */
        }

        .fixed-footer {
            position: fixed;
            bottom: 0;
            left: 220px; /* 侧边栏宽度 */
            right: 0;
            height: 48px; /* 固定高度 */
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 0.5rem 1rem;
            z-index: 40;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05); /* 添加顶部阴影 */
        }

        /* 调整表格行的样式 */
        .table-container tr:last-child td {
            border-bottom: none; /* 移除后一行的底部边框 */
        }

        /* 确保表格内容区域有足够的底部空间 */
        .scrollable-content {
            position: relative;
            flex: 1;
            min-height: 0;
            margin-bottom: 48px; /* 与固定底栏高度相同 */
        }

        @media (max-width: 768px) {
            .main-container {
                margin-left: 0;
            }
            .fixed-footer {
                left: 0;
            }
        }

        /* 确保表格内容不会被固定底部遮挡 */
        .table-container {
            margin-bottom: 48px; /* 底部分页栏的高度 */
        }

        /* 添加表格样式 */
        .table-container table {
            table-layout: fixed;  /* 固定表格布局 */
            width: max-content;   /* 根据内容设置宽度 */
            min-width: 100%;      /* 最小宽度100% */
        }

        .table-container th,
        .table-container td {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 标题列允许换行 */
        .table-container td:nth-child(3) {
            white-space: normal;
            line-height: 1.2;
        }

        /* 确保滚动条始终显示 */
        .table-container {
            overflow-x: auto;
            scrollbar-width: thin;
        }

        /* 美化滚动条 */
        .table-container::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #666;
        }

        /* 添加下拉菜单样式 */
        #store-dropdown {
            max-height: 300px;
            overflow-y: auto;
        }

        #store-dropdown a {
            transition: all 0.2s;
        }

        #store-dropdown a:hover {
            background-color: #f3f4f6;
        }

        /* 添加模态窗口样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(5px);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
        }

        .modal-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.95);
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 16px;
            width: 90%;
            max-width: 480px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .modal-overlay.show .modal-container {
            transform: translate(-50%, -50%) scale(1);
        }

        /* 输入控件样式 */
        .apple-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            transition: all 0.2s ease;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* 去掉数字输入框的上下箭头 */
        .apple-input[type="number"] {
            -moz-appearance: textfield;
            -webkit-appearance: textfield;
            appearance: textfield;
        }

        .apple-input[type="number"]::-webkit-outer-spin-button,
        .apple-input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            margin: 0;
        }

        .apple-input:focus {
            outline: none;
            border-color: #0071e3;
            box-shadow: 0 0 0 4px rgba(0, 113, 227, 0.1);
        }

        .apple-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            transition: all 0.2s ease;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23666'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 20px;
            padding-right: 40px;
        }

        .apple-button {
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .apple-button-primary {
            background-color: #0071e3;
            color: white;
            border: none;
        }

        .apple-button-primary:hover {
            background-color: #0077ed;
        }

        .apple-button-secondary {
            background-color: #f5f5f7;
            color: #1d1d1f;
            border: none;
        }

        .apple-button-secondary:hover {
            background-color: #e8e8ed;
        }

        /* 店铺选择器样式 */
        #store-selector {
            min-width: 160px;
            height: 32px;
            padding: 0 0.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            background-color: white;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        #store-selector:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        #store-selector option {
            padding: 0.5rem;
        }
    </style>
    {% endblock %}
</head>
<body class="gradient-bg">
    {% extends "base.html" %}
    {% block content %}
    <!-- 同步参数选择模态窗口 -->
    <div id="syncModal" class="modal-overlay" onclick="if(event.target === this) closeSyncModal()">
        <div class="modal-container">
            <div class="p-8">
                <div class="flex justify-between items-center mb-8">
                    <h3 class="text-2xl font-medium text-gray-900">同步确认</h3>
                    <button onclick="closeSyncModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <div class="bg-blue-50 rounded-lg p-6 mb-8">
                    <div class="space-y-4">
                        <p class="text-base text-gray-700 font-medium">确认要同步当前店铺的所有产品吗？</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                            <p>此操作可能需要几分钟时间，期间请勿关闭窗口</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4">
                    <button onclick="closeSyncModal()" 
                            class="px-6 py-2.5 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors duration-200">
                        取消
                    </button>
                    <button onclick="startSync()"
                            class="px-6 py-2.5 bg-blue-500 text-white text-sm font-medium rounded-lg hover:bg-blue-600 transition-colors duration-200">
                        开始同步
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 同步进度模态窗口 -->
    <div id="syncProgressModal" class="modal-overlay" style="display: none;">
        <div class="modal-container" style="max-width: 480px;">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-medium text-gray-900">同步进度</h3>
                    <button onclick="closeSyncProgressModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 进度条 -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-gray-600">正在同步产品...</span>
                        <span id="sync-percentage" class="text-sm font-medium text-blue-600">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div id="sync-progress-bar" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- 同步数量统计 -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">已同步数量</p>
                            <p id="sync-current-count" class="text-2xl font-semibold text-gray-900">0</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">总数量</p>
                            <p id="sync-total-count" class="text-2xl font-semibold text-gray-900">0</p>
                        </div>
                    </div>
                </div>
                
                <!-- 同步状态和操作按钮 -->
                <div class="flex justify-between items-center">
                    <span id="sync-status" class="text-sm text-gray-600">正在准备同步...</span>
                    <button id="cancel-sync-button" 
                            onclick="cancelSync()" 
                            class="px-4 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors duration-200">
                        取消同步
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div class="dashboard-card p-6" data-stat="total">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                    <i class="fas fa-box-open text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">总报价数</p>
                    <h3 class="text-xl font-semibold text-gray-800">0</h3>
                </div>
            </div>
        </div>
        
        <div class="dashboard-card p-6" data-stat="active">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">活跃报价</p>
                    <h3 class="text-xl font-semibold text-gray-800">0</h3>
                </div>
            </div>
        </div>
        
        <div class="dashboard-card p-6" data-stat="inactive">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-500">
                    <i class="fas fa-times-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">不活跃报价</p>
                    <h3 class="text-xl font-semibold text-gray-800">0</h3>
                </div>
            </div>
        </div>
        
        <div class="dashboard-card p-6" data-stat="buybox">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                    <i class="fas fa-shopping-cart text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">购物车报价</p>
                    <h3 class="text-xl font-semibold text-gray-800">0</h3>
                    <p class="text-xs text-gray-400">占比 0.0%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-switch p-2 rounded-full bg-white shadow-md" onclick="toggleTheme()">
        <i class="fas fa-moon"></i>
    </button>

    <!-- 主要内容域 -->
    <div class="dashboard-card">
        <!-- 固定顶部区域 -->
        <div class="fixed-header">
            <!-- 功能按钮行 -->
            <div class="flex justify-between items-center mb-4">
                <div class="flex space-x-3">
                    <div class="flex space-x-2">
                        <button onclick="openSyncModal()" 
                                class="h-8 px-3 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-200 flex items-center"
                                id="sync-button">
                            <i class="fas fa-sync-alt mr-1.5"></i>
                            <span>批量同步</span>
                        </button>
                        <button onclick="cancelSync()"
                                class="h-8 px-3 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors duration-200 flex items-center hidden"
                                id="cancel-button">
                            <i class="fas fa-stop mr-1.5"></i>
                            <span>取消同步</span>
                        </button>
                    </div>
                    <button onclick="openBatchEditModal()" 
                            class="h-8 px-3 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-200 flex items-center">
                        <i class="fas fa-edit mr-1.5"></i>批量编辑
                    </button>
                    <button class="h-8 px-3 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors duration-200 flex items-center">
                        <i class="fas fa-archive mr-1.5"></i>批量下架
                    </button>
                </div>
                <div class="flex items-center space-x-3">
                    <select id="store-selector" class="h-8 px-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <!-- 店铺选项将通过 JavaScript 动态加载 -->
                    </select>
                    <div class="relative">
                        <button class="h-8 px-3 bg-white border text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors duration-200 flex items-center"
                                onclick="toggleColumnList()">
                            <i class="fas fa-columns mr-1.5"></i>显示列
                        </button>
                        <div class="column-list absolute right-0 mt-2 bg-white border rounded-md shadow-lg" id="column-list">
                            <!-- 列选项将通过JavaScript动态填充 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">搜索</label>
                    <input type="text" id="search" placeholder="搜索标题/SKU/EAN" 
                           class="w-full px-2.5 py-1.5 text-sm border rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">产品状态</label>
                    <select id="status" class="w-full px-2.5 py-1.5 text-sm border rounded-md">
                        <option value="">全部</option>
                        <option value="active">活跃</option>
                        <option value="inactive">未活跃</option>
                        <option value="buybox">购物车</option>
                    </select>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">销售国</label>
                    <select id="country" class="w-full px-2.5 py-1.5 text-sm border rounded-md">
                        <option value="">全部</option>
                    </select>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">价格范围</label>
                    <div class="flex space-x-2">
                        <input type="number" id="min-price" placeholder="最小" 
                               class="w-1/2 px-2.5 py-1.5 text-sm border rounded-md">
                        <input type="number" id="max-price" placeholder="最大" 
                               class="w-1/2 px-2.5 py-1.5 text-sm border rounded-md">
                    </div>
                </div>
            </div>
        </div>

        <!-- 可滚动的表格区域 -->
        <div class="scrollable-content">
            <table class="min-w-full bg-white">
                <thead class="sticky top-0 bg-gray-50 border-b border-gray-200">
                    <tr>
                        <th class="w-10 px-3 py-2">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-500 focus:ring-blue-500">
                        </th>
                        <th class="w-48 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            ID/销售国家
                            <span class="sort-icon" onclick="sortTable('id')">
                                <i class="fas fa-sort"></i>
                            </span>
                        </th>
                        <th class="w-96 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            商品标题
                            <span class="sort-icon" onclick="sortTable('title')">
                                <i class="fas fa-sort"></i>
                            </span>
                        </th>
                        <th class="w-48 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            SKU/EAN
                        </th>
                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            配送时间
                            <span class="sort-icon" onclick="sortTable('delivery_hours')">
                                <i class="fas fa-sort"></i>
                            </span>
                        </th>
                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            库存数量
                            <span class="sort-icon" onclick="sortTable('amount')">
                                <i class="fas fa-sort"></i>
                            </span>
                        </th>
                        <th class="w-48 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            价格设置
                            <span class="sort-icon" onclick="sortTable('sell_price')">
                                <i class="fas fa-sort"></i>
                            </span>
                        </th>
                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            折扣前价格
                           <span class="sort-icon" onclick="sortTable('sell_price')">
                               <i class="fas fa-sort"></i>
                           </span>
                        </th>
                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            折扣后价格
                           <span class="sort-icon" onclick="sortTable('sell_price_after_discount')">
                               <i class="fas fa-sort"></i>
                           </span>
                        </th>
                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                        </th>
                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            基准价格
                           <span class="sort-icon" onclick="sortTable('base_price')">
                               <i class="fas fa-sort"></i>
                           </span>
                        </th>
                        <th class="w-48 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            创建/更新时间
                            <span class="sort-icon" onclick="sortTable('created_at')">
                                <i class="fas fa-sort"></i>
                            </span>
                        </th>
                        <th class="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                        </th>
                    </tr>
                </thead>
                <tbody id="products-table-body">
                    <!-- 产品数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
        </div>

        <!-- 固定底部分页 -->
        <div class="fixed-footer">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4 text-xs text-gray-700">
                    <span id="pagination-info">当前: 0 - 0 行, 共 0 行</span>
                    <div class="flex items-center space-x-2">
                        <span>每页</span>
                        <select id="page-size" class="border border-gray-300 rounded px-2 py-1 text-xs" onchange="changePageSize(this.value)">
                            <option value="20">20</option>
                            <option value="50" selected>50</option>
                            <option value="100">100</option>
                        </select>
                        <span>行</span>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button id="first-page" class="pagination-button">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button id="prev-page" class="pagination-button">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <span id="page-info" class="px-3 text-xs">第 0/0 页</span>
                    <button id="next-page" class="pagination-button">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button id="last-page" class="pagination-button">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 单个产品编辑模态窗口 -->
    <div id="editModal" class="modal-overlay" onclick="if(event.target === this) closeEditModal()">
        <div class="modal-container">
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">编辑产品</h3>
                <div class="space-y-3">
                    <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">配送时间（小时）</label>
                        <input type="number" id="edit-delivery-hours" class="apple-input" min="0">
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">库存数量</label>
                        <input type="number" id="edit-amount" class="apple-input" min="0">
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">销售价格</label>
                        <input type="number" step="0.01" id="edit-sell-price" class="apple-input" min="0">
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">折扣后价格</label>
                        <input type="number" step="0.01" id="edit-sell-price-after-discount" class="apple-input" min="0">
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">状态</label>
                        <select id="edit-status" class="apple-select">
                            <option value="active">活跃</option>
                            <option value="inactive">不活跃</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4 flex justify-end space-x-3">
                    <button onclick="closeEditModal()" class="apple-button apple-button-secondary">取消</button>
                    <button onclick="saveProductEdit()" class="apple-button apple-button-primary">保存</button>
                </div>
            </div>
        </div>
    </div>
    {% endblock %}

    {% block scripts %}
    <script>
        // 全局变量
        let currentFilters = {
            search: '',
            status: '',
            country: '',
            min_price: null,
            max_price: null,
            sort_by: '',
            sort_order: 'asc'
        };

        let columnSettings = {
            'id_country': { label: 'ID/销售国家', visible: true },
            'title': { label: '商品标题', visible: true },
            'sku_ean': { label: 'SKU/EAN', visible: true },
            'delivery': { label: '配送时间', visible: true },
            'amount': { label: '库存数量', visible: true },
            'price': { label: '价格设置', visible: true },
            'original_price': { label: '折扣前价格', visible: true },
            'discount_price': { label: '折扣后价格', visible: true },
            'status': { label: '状态', visible: true },
            'base_price': { label: '基准价格', visible: true },
            'time': { label: '创建/更新时间', visible: true }
        };

        let dashboardStats = {
            total: 0,
            active: 0,
            inactive: 0,
            buybox_match: 0
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载国家列表
            loadCountries();
            
            // 设置搜索和筛选事件监听
            setupEventListeners();
            
            // 初始化列显示设置
            initializeColumnSettings();
            
            // 加载数据
            loadProducts(1);
            
            // 加载统计数据
            loadDashboardStats();
        });

        // 加载国家列表
        function loadCountries() {
            fetch('/api/offer_product/countries')
                .then(response => response.json())
                .then(countries => {
                    const select = document.getElementById('country');
                    countries.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country;
                        option.textContent = country;
                        select.appendChild(option);
                    });
                });
        }

        // 设置事件监听
        function setupEventListeners() {
            // 搜索防抖
            let searchTimeout;
            document.getElementById('search').addEventListener('input', function(e) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentFilters.search = e.target.value;
                    loadProducts(1);
                }, 300);
            });

            // 状态筛选
            document.getElementById('status').addEventListener('change', function(e) {
                currentFilters.status = e.target.value;
                loadProducts(1);
            });

            // 国家筛选
            document.getElementById('country').addEventListener('change', function(e) {
                currentFilters.country = e.target.value;
                loadProducts(1);
            });

            // 价格范围筛选
            document.getElementById('min-price').addEventListener('change', function(e) {
                currentFilters.min_price = e.target.value ? parseFloat(e.target.value) : null;
                loadProducts(1);
            });

            document.getElementById('max-price').addEventListener('change', function(e) {
                currentFilters.max_price = e.target.value ? parseFloat(e.target.value) : null;
                loadProducts(1);
            });
        }

        // 初始化列显示设置
        function initializeColumnSettings() {
            const columnList = document.getElementById('column-list');
            Object.entries(columnSettings).forEach(([key, setting]) => {
                const div = document.createElement('div');
                div.className = 'flex items-center space-x-2 p-1';
                div.innerHTML = `
                    <input type="checkbox" id="col-${key}" 
                           ${setting.visible ? 'checked' : ''} 
                           onchange="toggleColumn('${key}')">
                    <label for="col-${key}">${setting.label}</label>
                `;
                columnList.appendChild(div);
            });
            applyColumnVisibility();
        }

        // 切换列显示
        function toggleColumn(key) {
            columnSettings[key].visible = !columnSettings[key].visible;
            applyColumnVisibility();
        }

        // 应用列显示设置
        function applyColumnVisibility() {
            const table = document.querySelector('table');
            Object.entries(columnSettings).forEach(([key, setting], index) => {
                const cells = table.querySelectorAll(`th:nth-child(${index + 1}), td:nth-child(${index + 1})`);
                cells.forEach(cell => {
                    cell.style.display = setting.visible ? '' : 'none';
                });
            });
        }

        // 切换列表显示
        function toggleColumnList() {
            const list = document.getElementById('column-list');
            list.classList.toggle('show');
        }

        // 排序表格
        function sortTable(column) {
            if (currentFilters.sort_by === column) {
                currentFilters.sort_order = currentFilters.sort_order === 'asc' ? 'desc' : 'asc';
            } else {
                currentFilters.sort_by = column;
                currentFilters.sort_order = 'asc';
            }
            
            // 更新排序图标
            document.querySelectorAll('.sort-icon').forEach(icon => {
                icon.classList.remove('active');
                icon.querySelector('i').className = 'fas fa-sort';
            });
            
            const currentIcon = document.querySelector(`[onclick="sortTable('${column}')"]`);
            if (currentIcon) {
                currentIcon.classList.add('active');
                currentIcon.querySelector('i').className = 
                    `fas fa-sort-${currentFilters.sort_order === 'asc' ? 'up' : 'down'}`;
            }
            
            loadProducts(1);
        }

        // 格式化价格
        function formatPrice(price) {
            return price ? '€' + parseFloat(price).toFixed(2) : '-';
        }

        // 格式化日期时间
        function formatDateTime(datetime) {
            return datetime || '-';
        }

        // 加载产品数据
        function loadProducts(page = 1) {
            const queryParams = new URLSearchParams({
                page: page,
                ...currentFilters
            });

            // 修改：如果 store_id 为空，则删除该参数
            if (!currentFilters.store_id) {
                queryParams.delete('store_id');
            }

            fetch(`/api/offer_product/products?${queryParams}`)
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('products-table-body');
                    tbody.innerHTML = data.products.map(product => `
                        <tr class="hover:bg-gray-50 border-t border-gray-200">
                            <td class="w-10 px-3 py-1.5">
                                <input type="checkbox" class="product-select rounded border-gray-300 text-blue-500 focus:ring-blue-500"
                                       data-product-id="${product.id}">
                            </td>
                            <td class="w-48 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900">${product.id}</div>
                                <div class="text-xs text-gray-500">销售国家: ${product.app_name || '-'}</div>
                            </td>
                            <td class="w-96 px-3 py-1.5">
                                <div class="text-xs text-gray-900">${product.title || '-'}</div>
                            </td>
                            <td class="w-48 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900">SKU: ${product.sku || '-'}</div>
                                <div class="text-xs text-gray-500">EAN: ${product.ean || '-'}</div>
                            </td>
                            <td class="w-24 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900" data-delivery-hours="${product.delivery_hours || ''}">${product.delivery_hours || '-'} 小时</div>
                            </td>
                            <td class="w-24 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900" data-amount="${product.amount || ''}">${product.amount || '0'}</div>
                            </td>
                            <td class="w-48 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs font-bold text-black" data-sell-price="${product.sell_price || ''}" data-sell-price-after-discount="${product.sell_price_after_discount || ''}">
                                    实际售价: ${formatPrice(product.sell_price_after_discount || product.sell_price)}
                                </div>
                                <div class="text-xs text-gray-500">限制最高价: ${formatPrice(product.insult_price)}</div>
                                <div class="text-xs text-gray-500">购物车价格: ${formatPrice(product.buybox_price)}</div>
                            </td>
                            <td class="w-24 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900">${formatPrice(product.sell_price)}</div>
                            </td>
                            <td class="w-24 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900">${formatPrice(product.sell_price_after_discount)}</div>
                            </td>
                            <td class="w-24 px-3 py-1.5 whitespace-nowrap">
                                <span class="px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(product.status)}"
                                      data-status="${product.status || 'inactive'}">
                                    ${getStatusText(product.status)}
                                </span>
                            </td>
                            <td class="w-24 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900">${formatPrice(product.base_price)}</div>
                            </td>
                            <td class="w-48 px-3 py-1.5 whitespace-nowrap">
                                <div class="text-xs text-gray-900">创建: ${formatDateTime(product.created_at)}</div>
                                <div class="text-xs text-gray-500">更新: ${formatDateTime(product.updated_at)}</div>
                            </td>
                            <td class="w-24 px-3 py-1.5 whitespace-nowrap">
                                <div class="flex space-x-2">
                                    <button onclick="openEditModal('${product.id}')" 
                                            class="px-2 py-1 text-xs text-blue-600 hover:text-blue-800">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('');

                    updatePagination(data.pagination);
                    applyColumnVisibility();
                });
        }

        // 更新分页控件
        function updatePagination(pagination) {
            const { current_page, total_pages, total_count, per_page } = pagination;
            const start = (current_page - 1) * per_page + 1;
            const end = Math.min(current_page * per_page, total_count);
            
            // 更新分页信息
            document.getElementById('pagination-info').textContent = 
                `当前: ${start} - ${end} 行, 共 ${total_count} 行`;
            document.getElementById('page-info').textContent = 
                `第 ${current_page}/${total_pages} 页`;
            
            // 更新按钮状态
            document.getElementById('first-page').disabled = current_page === 1;
            document.getElementById('prev-page').disabled = current_page === 1;
            document.getElementById('next-page').disabled = current_page === total_pages;
            document.getElementById('last-page').disabled = current_page === total_pages;
            
            // 设置按钮点击事件
            document.getElementById('first-page').onclick = () => loadProducts(1);
            document.getElementById('prev-page').onclick = () => loadProducts(current_page - 1);
            document.getElementById('next-page').onclick = () => loadProducts(current_page + 1);
            document.getElementById('last-page').onclick = () => loadProducts(total_pages);
        }

        // 切换主题
        function toggleTheme() {
            const body = document.body;
            const button = document.querySelector('.theme-switch i');
            
            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                button.className = 'fas fa-moon';
                // 切换到亮色主题的其他样式
            } else {
                body.classList.add('dark');
                button.className = 'fas fa-sun';
                // 切换到色主题的其他样式
            }
        }

        // 加载仪表盘统计数据
        function loadDashboardStats() {
            const url = new URL('/api/offer_product/stats', window.location.origin);
            
            // 修改：只有在有 store_id 时才添加参数
            if (currentFilters.store_id) {
                url.searchParams.append('store_id', currentFilters.store_id);
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.error);
                        return;
                    }
                    
                    // 更新统计卡片
                    document.querySelector('[data-stat="total"] h3').textContent = data.total;
                    document.querySelector('[data-stat="active"] h3').textContent = data.active;
                    document.querySelector('[data-stat="inactive"] h3').textContent = data.inactive;
                    document.querySelector('[data-stat="buybox"] h3').textContent = data.buybox_match;
                    
                    // 更新购物车占比
                    const percentage = data.total > 0 ? ((data.buybox_match / data.total) * 100).toFixed(1) : '0.0';
                    document.querySelector('[data-stat="buybox"] .text-xs').textContent = `占比 ${percentage}%`;
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        // 添加全选功能
        document.getElementById('select-all').addEventListener('change', function(e) {
            const checkboxes = document.querySelectorAll('.product-select');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
        });

        // 获取选中的产品ID
        function getSelectedProductIds() {
            const checkboxes = document.querySelectorAll('.product-select:checked');
            return Array.from(checkboxes).map(checkbox => checkbox.dataset.productId);
        }

        function changePageSize(size) {
            currentFilters.per_page = parseInt(size);
            loadProducts(1);
        }

        // 加载店铺列表
        function loadStoreDropdown() {
            fetch('/api/phh_store/stores')
                .then(response => response.json())
                .then(stores => {
                    const selector = document.getElementById('store-selector');
                    // 保留"所有店铺"选项
                    selector.innerHTML = '<option value="">所有店铺</option>';
                    // 添加店铺选项
                    stores.forEach(store => {
                        const option = document.createElement('option');
                        option.value = store.seller_id;
                        option.textContent = store.name;
                        selector.appendChild(option);
                    });

                    // 从 sessionStorage 恢复选中的店铺
                    const savedSellerId = sessionStorage.getItem('currentSellerId');
                    if (savedSellerId) {
                        selector.value = savedSellerId;
                    }
                })
                .catch(error => {
                    console.error('加载店铺列表失败:', error);
                });
        }

        // 监听店铺选择变化
        document.addEventListener('DOMContentLoaded', function() {
            const storeSelector = document.getElementById('store-selector');
            storeSelector.addEventListener('change', function() {
                const selectedId = this.value;
                // 修改：确保 store_id 为 null 而不是空字符串
                currentFilters.store_id = selectedId || null;
                
                // 修改：如果是空字符串，则从 sessionStorage 中移除
                if (!selectedId) {
                    sessionStorage.removeItem('currentSellerId');
                } else {
                    sessionStorage.setItem('currentSellerId', selectedId);
                }
                
                // 重新加载数据
                loadProducts(1);
                loadDashboardStats();
            });

            // 初始化加载店铺列表
            loadStoreDropdown();
            
            // 修改：初始化时立即加载数据
            loadProducts(1);
            loadDashboardStats();
        });

        // 当前编辑的产品ID
        let currentEditingId = null;

        // 打开单个编辑模态窗口
        function openEditModal(productId) {
            currentEditingId = productId;
            const product = findProductById(productId);
            
            if (product) {
                // 设置数值，如果是 NaN 则设置为空字符串
                document.getElementById('edit-delivery-hours').value = !isNaN(product.delivery_hours) ? product.delivery_hours : '';
                document.getElementById('edit-amount').value = !isNaN(product.amount) ? product.amount : '';
                document.getElementById('edit-sell-price').value = !isNaN(product.sell_price) ? product.sell_price.toFixed(2) : '';
                document.getElementById('edit-sell-price-after-discount').value = !isNaN(product.sell_price_after_discount) ? product.sell_price_after_discount.toFixed(2) : '';
                document.getElementById('edit-status').value = product.status || 'active';
            }
            
            const modal = document.getElementById('editModal');
            modal.style.display = 'block';
            setTimeout(() => modal.classList.add('show'), 10);
        }

        // 关闭单个编辑模态窗口
        function closeEditModal() {
            const modal = document.getElementById('editModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
                currentEditingId = null;
            }, 300);
        }

        // 保存单个产品编辑
        async function saveProductEdit() {
            if (!currentEditingId) return;

            const updateData = {
                delivery_hours: parseInt(document.getElementById('edit-delivery-hours').value) || null,
                amount: parseInt(document.getElementById('edit-amount').value) || null,
                sell_price: parseFloat(document.getElementById('edit-sell-price').value) || null,
                sell_price_after_discount: parseFloat(document.getElementById('edit-sell-price-after-discount').value) || null,
                status: document.getElementById('edit-status').value
            };

            try {
                const response = await fetch(`/api/offer_product/update`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        store_id: currentFilters.store_id,
                        offer_ids: [currentEditingId],
                        update_data: updateData
                    })
                });

                const result = await response.json();
                if (result.success) {
                    alert('更新成功');
                    closeEditModal();
                    loadProducts(currentPage);
                } else {
                    alert(`更新失败: ${result.message}`);
                }
            } catch (error) {
                alert(`更新失败: ${error.message}`);
            }
        }

        // 根据ID查找产品
        function findProductById(productId) {
            const row = document.querySelector(`[data-product-id="${productId}"]`).closest('tr');
            if (!row) return null;
            
            return {
                id: productId,
                delivery_hours: parseInt(row.querySelector('[data-delivery-hours]').textContent),
                amount: parseInt(row.querySelector('[data-amount]').textContent),
                sell_price: parseFloat(row.querySelector('[data-sell-price]').getAttribute('data-sell-price')),
                sell_price_after_discount: parseFloat(row.querySelector('[data-sell-price]').getAttribute('data-sell-price-after-discount')),
                status: row.querySelector('[data-status]').getAttribute('data-status')
            };
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'active':
                    return 'bg-green-100 text-green-800';
                case 'inactive':
                    return 'bg-red-100 text-red-800';
                case 'buybox':
                    return 'bg-yellow-100 text-yellow-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 获取状态显示文本
        function getStatusText(status) {
            switch(status) {
                case 'active':
                    return 'active';
                case 'inactive':
                    return 'inactive';
                case 'buybox':
                    return 'buybox';
                default:
                    return '-';
            }
        }

        // 同步相关函数
        function openSyncModal() {
            const modal = document.getElementById('syncModal');
            modal.style.display = 'block';
            setTimeout(() => modal.classList.add('show'), 10);
        }

        function closeSyncModal() {
            const modal = document.getElementById('syncModal');
            modal.classList.remove('show');
            setTimeout(() => modal.style.display = 'none', 300);
        }

        function startSync() {
            // 获取当前选中的店铺ID
            const storeId = currentFilters.store_id;
            if (!storeId) {
                alert('请先选择店铺');
                return;
            }

            // 关闭确认模态窗口
            closeSyncModal();
            
            // 显示进度模态窗口
            const progressModal = document.getElementById('syncProgressModal');
            progressModal.style.display = 'block';
            setTimeout(() => progressModal.classList.add('show'), 10);

            // 开始同步
            fetch(`/api/offer_product/sync`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ store_id: storeId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                    closeSyncProgressModal();
                } else {
                    // 开始轮询进度
                    pollSyncProgress(storeId);
                }
            })
            .catch(error => {
                alert('同步启动失败: ' + error);
                closeSyncProgressModal();
            });
        }

        function pollSyncProgress(storeId) {
            const progressInterval = setInterval(() => {
                fetch(`/api/offer_product/sync/progress`)
                    .then(response => response.json())
                    .then(data => {
                        // 更新进度条
                        const progressBar = document.getElementById('sync-progress-bar');
                        const percentageText = document.getElementById('sync-percentage');
                        const currentCount = document.getElementById('sync-current-count');
                        const totalCount = document.getElementById('sync-total-count');
                        const statusText = document.getElementById('sync-status');

                        progressBar.style.width = `${data.progress}%`;
                        percentageText.textContent = `${data.progress}%`;
                        currentCount.textContent = data.current_count || '0';
                        totalCount.textContent = data.total_count || '0';
                        statusText.textContent = data.status || '正在同步...';

                        // 如果同步完成或被取消
                        if (data.is_completed || data.is_cancelled) {
                            clearInterval(progressInterval);
                            setTimeout(() => {
                                closeSyncProgressModal();
                                loadProducts(1);  // 重新加载产品列表
                                loadDashboardStats();  // 更新统计数据
                            }, 1000);
                        }
                    });
            }, 1000);  // 每秒更新一次
        }

        function closeSyncProgressModal() {
            const modal = document.getElementById('syncProgressModal');
            modal.classList.remove('show');
            setTimeout(() => modal.style.display = 'none', 300);
        }

        function cancelSync() {
            const storeId = currentFilters.store_id;
            if (!storeId) return;

            fetch(`/api/offer_product/sync/cancel`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                }
            });
        }
    </script>
    {% endblock %}
</body>
</html> 