from DrissionPage import Chromium, ChromiumOptions
from DataRecorder import Filler
import re
import time
import signal
import sys
import os

# 移除多线程相关导入

# 设置数据保存目录
DATA_DIR = r"D:\A_Driss_pageWork"
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# 全局变量
collected_data = []
sellers_filler = None
shops_filler = None


def signal_handler(signum, frame):
    """处理中断信号，保存已采集的数据"""
    print('\n检测到程序中断，正在保存已获取的数据...')

    # 保存卖家信息表
    if sellers_filler and sellers_filler.data:
        sellers_filler.record()
        print(f'卖家信息表已保存，共 {len(sellers_filler.data)} 条数据')

    # 保存店铺信息表
    if shops_filler and shops_filler.data:
        shops_filler.record()
        print(f'店铺信息表已保存，共 {len(shops_filler.data)} 条数据')

    sys.exit(0)


def get_sellers_info():
    """第一步：启动1个浏览器获取卖家信息"""
    print("=== 第一步：获取卖家信息 ===")

    # 创建浏览器配置，指定用户数据目录
    co = ChromiumOptions().no_imgs(True).set_user_data_path(os.path.join(DATA_DIR, 'browser_data_1'))
    browser = Chromium(co)
    page = browser.latest_tab
    page.set.blocked_urls(['*.jpg*', '*.png*', '*.gif*',])

    try:
        print("正在访问: https://pigu.lt/lt/parduotuves/all")
        page.get('https://pigu.lt/lt/parduotuves/all')

        # 等待页面加载
        time.sleep(3)

        print(f"当前页面URL: {page.url}")
        print(f"页面内容长度: {len(page.html)}")

        # 查找卖家元素
        sellers = page('.category-list all-categories-visible').eles('t:a')
        print(f"找到的卖家元素数量: {len(sellers)}")

        # 存储卖家信息
        sellers_info = []
        sellers_data_list = []  # 用于批量添加到表格

        for i, seller in enumerate(sellers, 1):
            try:
                # 获取链接和名称
                name_element = seller.ele('t:p')
                seller_name = name_element.text if name_element else None
                seller_link = seller.link

                if seller_name and seller_link:
                    # 添加到返回列表
                    sellers_info.append({
                        'name': seller_name,
                        'url': seller_link
                    })

                    # 添加到表格数据列表
                    sellers_data_list.append([seller_name, seller_link])

            except Exception as e:
                print(f"处理第 {i} 个卖家时出错: {str(e)}")
                continue

        # 一次性保存所有卖家信息到表格
        if sellers_data_list:
            global sellers_filler
            sellers_filler = Filler(os.path.join(DATA_DIR, '卖家信息表.xlsx'))
            sellers_filler.set.head(['卖家名称', '卖家链接'])

            # 批量添加所有数据
            for seller_data in sellers_data_list:
                sellers_filler.add_data(seller_data)

            # 一次性保存
            sellers_filler.record()
            print(f"卖家信息表已保存，共 {len(sellers_info)} 条数据")

        return sellers_info

    except Exception as e:
        print(f"获取卖家信息时出错: {str(e)}")
        return []
    finally:
        browser.quit()
        print("第一个浏览器已关闭")


def extract_shop_data_from_html(html_content):
    """从页面源码中提取完整的店铺数据"""
    shop_data = {
        'rating': None,
        'review_count': None,
        'established_time': None,
        'sold_count': None,
        'product_count': None,  # 产品数量 - Visos prekės (3 207)
        'seller_id': None,  # 卖家ID - getSellerCompanyInfo/9000608
        'company_name': None,
        'company_address': None,
        'company_country': None,
        'company_email': None
    }

    # 提取评分
    rating_match = re.search(r'<[^>]*class="[^"]*c-rating__average[^"]*"[^>]*>([^<]+)</[^>]*>', html_content)
    if rating_match:
        shop_data['rating'] = rating_match.group(1).strip()

    # 提取评论数量
    review_match = re.search(r'(\d+[\s\d]*)\s*pirkėjų įvertinimų', html_content)
    if review_match:
        shop_data['review_count'] = ''.join(review_match.group(1).split())

    # 提取成立时间（支持两种格式）
    established_match = re.search(r'Pardavėjas (?:daugiau nei|nuo) <span class="h-fw--semibold">([^<]+)</span>',
                                  html_content)
    if established_match:
        shop_data['established_time'] = established_match.group(1).strip()

    # 提取销售数量
    sold_match = re.search(r'Parduota prekių: daugiau nei <span class="h-fw--semibold">([^<]+)</span>', html_content)
    if sold_match:
        shop_data['sold_count'] = ''.join(sold_match.group(1).split())

    # 提取产品数量 - Visos prekės (3 207)
    product_match = re.search(r'Visos prekės \((\d+[\s\d]*)\)', html_content)
    if product_match:
        shop_data['product_count'] = ''.join(product_match.group(1).split())

    # 提取卖家ID - pigu.lt/lt/marketplace/marketplace_search/getSellerCompanyInfo/9000608
    seller_id_match = re.search(r'pigu\.lt/lt/marketplace/marketplace_search/getSellerCompanyInfo/(\d+)', html_content)
    if seller_id_match:
        shop_data['seller_id'] = seller_id_match.group(1)

    # 提取公司信息（从模态框）
    company_patterns = {
        'company_name': r'Įmonės pavadinimas:\s*</strong>([^<]+)<br>',
        'company_address': r'Įmonės adresas:\s*</strong>([^<]+)<br>',
        'company_country': r'Įmonės šalis:\s*</strong>([^<]+)<br>',
        'company_email': r'El\. paštas:\s*</strong>([^<]+)</div>'
    }

    for key, pattern in company_patterns.items():
        match = re.search(pattern, html_content)
        if match:
            shop_data[key] = match.group(1).strip()

    return shop_data


def extract_products_from_html(html_content):
    """从页面源码中提取产品信息"""
    import json

    products = []

    # 匹配所有产品卡片 - class="c-product-card   product-block-"
    product_pattern = r'<div class="c-product-card\s+product-block-[^"]*"[^>]*widget-data="([^"]*)"[^>]*>'
    product_matches = re.finditer(product_pattern, html_content)

    for match in product_matches:
        try:
            # 获取 widget-data 内容并解码HTML实体
            widget_data = match.group(1)
            # 解码HTML实体
            widget_data = widget_data.replace('&quot;', '"').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;',
                                                                                                                '>')

            # 解析JSON数据
            product_data = json.loads(widget_data)

            # 提取需要的信息
            product_info = {
                'product_id': product_data.get('productId'),
                'title': product_data.get('title'),
                'url': product_data.get('url'),
                'sell_price': product_data.get('meta', {}).get('sell_price'),
                'photo_url': product_data.get('photoUrl')
            }

            # 只添加有效的产品信息
            if product_info['product_id'] and product_info['url'] and product_info['sell_price']:
                products.append(product_info)

        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            # 跳过解析失败的产品
            continue

    return products


def get_shop_info(page, shop_url, seller_name, browser_id):
    """获取单个店铺信息 - 使用新的源码提取方式"""
    try:
        print(f"[浏览器{browser_id}] 正在访问店铺: {seller_name}")
        page.get(shop_url)
        # 点击公司信息链接获取模态框
        try:
            company_link = page.ele('.c-seller__details c-link')
            if company_link:
                company_link.click(by_js=None)
                time.sleep(1)  # 等待模态框加载
                print(f"[浏览器{browser_id}] 已点击公司信息链接")
        except:
            print(f"[浏览器{browser_id}] 未找到或无法点击公司信息链接")

        # 获取页面源码
        html_content = page.html

        # 使用新的提取函数获取店铺数据
        shop_data_dict = extract_shop_data_from_html(html_content)

        # 获取产品数据
        # products = extract_products_from_html(html_content)

        # 转换为列表格式以兼容现有的数据结构
        shop_data = [
            seller_name,  # 卖家名称
            shop_url,  # 店铺链接
            shop_data_dict.get('product_count'),  # 产品数量
            shop_data_dict.get('rating'),  # 评分
            shop_data_dict.get('sold_count'),  # 销售数量
            shop_data_dict.get('established_time'),  # 成立日期
            shop_data_dict.get('company_name'),  # 公司名称
            shop_data_dict.get('company_address'),  # 公司地址
            shop_data_dict.get('company_country'),  # 公司国家
            shop_data_dict.get('company_email'),  # 公司邮箱
            shop_data_dict.get('seller_id')  # 卖家ID
        ]

        print(f"[浏览器{browser_id}] 成功获取店铺信息: {seller_name} )")
        return shop_data

    except Exception as e:
        print(f"[浏览器{browser_id}] 访问店铺 {seller_name} 失败: {str(e)}")
        return None


# 已移除多线程浏览器工作函数

def get_all_shops_info(sellers_info):
    """第二步：使用单个浏览器获取所有店铺信息"""
    print("=== 第二步：获取店铺信息 ===")

    # 初始化店铺信息记录器
    global shops_filler
    shops_filler = Filler(os.path.join(DATA_DIR, '店铺信息表.xlsx'))
    shops_filler.set.head([
        '卖家名称', '店铺链接', '产品数量', '评分', '销售数量',
        '成立日期', '公司名称', '公司地址', '公司国家', '公司邮箱', '卖家ID'
    ])
    shops_filler.set.cache_size(50)  # 每50条保存一次

    # 创建单个浏览器实例
    co = ChromiumOptions().no_imgs(True).set_user_data_path(os.path.join(DATA_DIR, 'browser_data_shops'))
    browser = Chromium(co)
    page = browser.latest_tab
    page.set.blocked_urls(['*.jpg*', '*.png*', '*.gif*',])

    print("已启动单个浏览器进行店铺信息采集")

    try:
        completed_count = 0
        total_tasks = len(sellers_info)

        for i, seller in enumerate(sellers_info, 1):
            try:
                seller_name = seller['name']
                shop_url = seller['url']

                print(f"进度: {i}/{total_tasks} - 正在访问店铺: {seller_name}")

                # 获取店铺信息
                shop_data = get_shop_info(page, shop_url, seller_name, 1)

                if shop_data:
                    shops_filler.add_data(shop_data)
                    completed_count += 1
                    print(f"成功获取店铺信息: {seller_name}")

                    # 每处理50条数据自动保存一次
                    if completed_count % 50 == 0:
                        shops_filler.record()
                        print(f"已自动保存 {completed_count} 条店铺数据")
                else:
                    print(f"获取店铺信息失败: {seller_name}")

                # 添加延迟避免请求过快
                time.sleep(2)

            except Exception as e:
                print(f"处理店铺 {seller_name} 时出错: {str(e)}")
                continue

        # 最终保存
        shops_filler.record()
        print(f"店铺信息表已保存，共处理 {completed_count} 条数据")

    except Exception as e:
        print(f"获取店铺信息时出错: {str(e)}")
    finally:
        browser.quit()
        print("浏览器已关闭")


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    print(f"数据将保存到: {DATA_DIR}")

    try:
        # 第一步：获取卖家信息
        sellers_info = get_sellers_info()
        if not sellers_info:
            return

        print(f"\n成功获取 {len(sellers_info)} 个卖家信息")

        # 第二步：获取店铺信息
        get_all_shops_info(sellers_info)

        print("\n=== 采集完成 ===")
        print(f"卖家信息表: {os.path.join(DATA_DIR, '卖家信息表.xlsx')}")
        print(f"店铺信息表: {os.path.join(DATA_DIR, '店铺信息表.xlsx')}")

    except Exception as e:
        print(f"程序执行出错: {str(e)}")
    finally:
        # 确保数据已保存
        if sellers_filler and sellers_filler.data:
            sellers_filler.record()
        if shops_filler and shops_filler.data:
            shops_filler.record()


if __name__ == '__main__':
    main()