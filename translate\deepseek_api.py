from openai import OpenAI
from models import SystemSettings
import logging
import time

logger = logging.getLogger(__name__)

class DeepSeekTranslator:
    def __init__(self):
        """初始化翻译器"""
        self.base_url = "https://api.deepseek.com/v1"
        self.model = "deepseek-chat"
        self.api_key = SystemSettings.get_setting('translation_api_key', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        
        # 初始化OpenAI客户端
        if self.api_key:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
    
    def refresh_settings(self):
        """刷新设置"""
        self.api_key = SystemSettings.get_setting('translation_api_key', '')
        self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
        self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
        
        if self.api_key:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            
    def translate(self, text, source_lang="AUTO", target_lang="auto"):
        """翻译文本"""
        if SystemSettings.get_setting('translation_enabled', 'true').lower() != 'true':
            return {
                'success': False,
                'error': '翻译功能已禁用'
            }
            
        if not self.api_key:
            return {
                'success': False,
                'error': '未设置API密钥'
            }
            
        if not text:
            return {
                'success': False,
                'error': '翻译文本不能为空'
            }
            
        system_prompt = (
            "You are a professional translator. "
            "Translate the following text accurately and naturally. "
            f"Source language: {source_lang}, Target language: {target_lang}. "
            "Only return the translated text without any explanations or additional content."
        )
        
        for retry in range(self.retry_count):
            try:
                completion = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": text}
                    ],
                    stream=False
                )
                
                translated_text = completion.choices[0].message.content.strip()
                return {
                    'success': True,
                    'text': translated_text,
                    'source_lang': source_lang,
                    'target_lang': target_lang
                }
                
            except Exception as e:
                if retry < self.retry_count - 1:
                    time.sleep(self.retry_interval)
                else:
                    return {
                        'success': False,
                        'error': f'翻译失败: {str(e)}'
                    }
    
    def translate_batch(self, texts, source_lang="AUTO", target_lang="auto"):
        """批量翻译文本"""
        results = []
        for text in texts:
            result = self.translate(text, source_lang, target_lang)
            results.append(result)
        return results

if __name__ == "__main__":
    translator = DeepSeekTranslator()
    # try:
    #     # 测试单个翻译
    #     print("\n🧪 测试单个翻译:")
    #     result = translator.translate("Hello, world!", source_lang="EN", target_lang="ZH")
    #     print(f"翻译结果: {result}")
        
    #     if result['success']:
    #         # 测试批量翻译
    #         print("\n🧪 测试批量翻译:")
    #         texts = ["Hello!", "How are you?", "Good morning!"]
    #         results = translator.translate_batch(texts, source_lang="EN", target_lang="ZH")
    #         for i, result in enumerate(results, 1):
    #             print(f"第 {i} 个结果: {result}")
    # except Exception as e:
    #     print(f"测试失败: {str(e)}") 