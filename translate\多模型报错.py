import os
import json
import asyncio
import aiohttp
import logging
import time
from openai import AsyncOpenAI
from typing import List, Dict, Optional
from models import SystemSettings, TokenUsage
from models import MultiModelConfig

logger = logging.getLogger(__name__)

class DeepSeekhuoshan:
    def __init__(self):
        """初始化翻译器"""
        # 检查是否启用多模型
        multi_model = MultiModelConfig.get_available_model()
        if multi_model:
            self.api_key = multi_model.api_key
            self.model_id = multi_model.model_id
            self.model_name = multi_model.model_name
            self.current_model = multi_model
            logger.info(f"使用多模型模式，模型别名: {self.model_name}, 模型ID: {self.model_id}, API Key: {self.api_key}")
        else:
            # 使用默认设置
            self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
            self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
            self.api_key = SystemSettings.get_setting('translation_api_key', '')
            self.model_id = SystemSettings.get_setting('translation_model', '')
            self.current_model = None
            logger.info(f"使用默认设置，模型ID: {self.model_id}, API Key: {self.api_key}")
        
        # 保持基础URL和客户端初始化
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )
        
        self.refresh_settings()
        self._title_prompt = """你是电商标题优化翻译专家。请将输入的内容翻译成四种语言并以JSON格式输出。\n\n目标语言：立陶宛语(LT)、拉脱维亚语(LV)、爱沙尼亚语(ET)、芬兰语(FI)\n\n标题处理规则：\n1. 提取并重组信息：\n   - 主要产品名称（必须）\n   - 数量信息（如有）\n   - 颜色/材质（如有）\n   - 用途/场合（如有）\n   - 如果产品有多个用途，只保留最主要的用途\n   - 如果产品标题有知名品牌信息或者产品兼容型号，那么输出的内容中在品牌前或者型号前加上'兼容于'这个信息，并且要带上品牌或者型号，因为知名品牌会侵权，只能销售配件而不是销售成品\n\n2. 标题格式化：\n   - 产品名称+数量+材质/颜色+用途+兼容型号或品牌(型号或品牌如果存在则加上,否则不加)\n   - 删除不必要的修饰词\n   - 确保语法正确\n   - 不要带有.,!等标点符号\n\n3. 长度控制：\n   - 保持简洁（建议20-40字符）\n   - 突出关键信息\n\n输出格式要求：\n1. 必须使用标准JSON格式\n2. 包含且仅包含四个语言代码的键值对\n3. 示例输出：\n{\n    "LT": "Moteriškas raudonas sijonas",\n    "LV": "Sarkani sieviešu zīda svārki",\n    "ET": "Punane naiste siidiseelik",\n    "FI": "Punainen naisten silkkihame"\n}\n\n请确保输出的是可解析的JSON格式，不要添加任何其他说明文字。"""
        self._desc_prompt = """你是电商产品描述翻译专家。请将输入的产品描述翻译成四种语言并以JSON格式输出。\n\n目标语言：立陶宛语(LT)、拉脱维亚语(LV)、爱沙尼亚语(ET)、芬兰语(FI)\n\n请注意：\n1. 保持原有格式，包括HTML标签。\n2. 不进行任何优化，只需正常翻译。\n\n输出格式要求：\n1. 必须使用标准JSON格式\n2. 包含且仅包含四个语言代码的键值对\n3. 示例输出：\n{\n    "LT": "...",\n    "LV": "...",\n    "ET": "...",\n    "FI": "..."\n}\n\n请确保输出的是可解析的JSON格式，不要添加任何其他说明文字。"""

    def refresh_settings(self):
        """刷新设置"""
        # 检查是否启用多模型
        multi_model = MultiModelConfig.get_available_model()
        if multi_model:
            self.api_key = multi_model.api_key
            self.model_id = multi_model.model_id
            self.current_model = multi_model
        else:
            # 使用默认设置
            self.retry_count = int(SystemSettings.get_setting('translation_retry_count', '3'))
            self.retry_interval = int(SystemSettings.get_setting('translation_retry_interval', '5'))
            self.api_key = SystemSettings.get_setting('translation_api_key', '')
            self.model_id = SystemSettings.get_setting('translation_model', '')
            self.current_model = None
        
        # 更新 client
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )

    def translate(self, text: str, source_lang: str = "AUTO", target_lang: str = "auto") -> Dict:
        """兼容单个翻译API的同步方法"""
        if SystemSettings.get_setting('translation_enabled', 'true').lower() != 'true':
            return {
                'success': False,
                'error': '翻译功能已禁用'
            }
        if not self.api_key:
            return {
                'success': False,
                'error': '检查DeepSeek火山引擎API Key'
            }
        if not text:
            return {
                'success': False,
                'error': '翻译文本不能为空'
            }

        # 判断是标题还是描述（根据文本特征）
        is_title = not text.startswith('<') and len(text.split()) < 20
        
        # 创建新的事件循环
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        
        # 创建新的异步客户端
        async_client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )
        
        try:
            result = new_loop.run_until_complete(self._translate_text_with_client(text, is_title, async_client))
        finally:
            # 关闭异步客户端
            new_loop.run_until_complete(async_client.close())
            new_loop.close()
        
        if result['success']:
            return {
                'success': True,
                'text': result['text'],
                'source_lang': source_lang,
                'target_lang': target_lang
            }
        return {
            'success': False,
            'error': result.get('error', 'Translation failed')
        }

    async def _translate_text_with_client(self, text: str, is_title: bool, client: AsyncOpenAI) -> Dict:
        """使用指定客户端的翻译处理函数"""
        try:
            # 每次翻译前刷新设置，确保使用正确的模型
            self.refresh_settings()
            
            completion = await client.chat.completions.create(
                model=self.model_id,
                messages=[
                    {"role": "system", "content": self._title_prompt if is_title else self._desc_prompt},
                    {"role": "user", "content": f"Translate this {'title' if is_title else 'description'}: {text}"}
                ]
            )
            result = completion.choices[0].message.content
            
            # 记录 token 使用情况
            request_tokens = completion.usage.prompt_tokens
            response_tokens = completion.usage.completion_tokens
            total_tokens = completion.usage.total_tokens
            
            # 如果是多模型模式，更新对应模型的使用量
            if self.current_model:
                self.current_model.update_token_usage(total_tokens)
                # 如果当前模型超出限制，自动切换到下一个可用模型
                if not self.current_model.is_active:
                    self.refresh_settings()
            
            # 记录常规token统计
            TokenUsage.add_usage(request_tokens, response_tokens, api_provider='deepseek_huoshan')
            
            try:
                # 解析JSON响应
                translated_data = json.loads(result)
                # 验证返回的数据包含所有必需的语言代码
                required_langs = ['LT', 'LV', 'ET', 'FI']
                if all(lang in translated_data for lang in required_langs):
                    return {
                        'success': True,
                        'text': translated_data,
                        'usage': {
                            'request_tokens': request_tokens,
                            'response_tokens': response_tokens,
                            'total_tokens': total_tokens
                        }
                    }
                else:
                    missing_langs = [lang for lang in required_langs if lang not in translated_data]
                    return {
                        'success': False,
                        'error': f'Missing translations for languages: {", ".join(missing_langs)}',
                        'usage': {
                            'request_tokens': request_tokens,
                            'response_tokens': response_tokens,
                            'total_tokens': total_tokens
                        }
                    }
            except json.JSONDecodeError as e:
                return {
                    'success': False,
                    'error': f'Invalid JSON response: {str(e)}',
                    'usage': {
                        'request_tokens': request_tokens,
                        'response_tokens': response_tokens,
                        'total_tokens': total_tokens
                    }
                }
        except Exception as e:
            logger.error(f"翻译失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    async def translate_product_full_async(self, title: str, description: str, source_lang: str = "AUTO") -> Dict:
        """同时翻译产品的标题和描述"""
        if SystemSettings.get_setting('translation_enabled', 'true').lower() != 'true':
            return {
                'title': None,
                'description': None,
                'error': '翻译功能已禁用'
            }

        # 创建新的异步客户端
        async_client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )

        try:
            # 同时发送标题和描述的翻译请求
            title_result, desc_result = await asyncio.gather(
                self._translate_text_with_client(title, True, async_client),
                self._translate_text_with_client(description, False, async_client)
            )
            
            return {
                'title': title_result.get('text', {}) if title_result['success'] else None,
                'description': desc_result.get('text', {}) if desc_result['success'] else None
            }
        finally:
            # 确保关闭异步客户端
            await async_client.close()

    async def translate_batch_products_async(self, products: List[Dict]) -> List[Dict]:
        """批量翻译多个产品的标题和描述
        
        Args:
            products: 包含多个产品信息的列表，每个产品都应该有 title 和 description 字段
            
        Returns:
            翻译结果列表，每个元素包含一个产品的翻译结果
        """
        if SystemSettings.get_setting('translation_enabled', 'true').lower() != 'true':
            return [{'error': '翻译功能已禁用'} for _ in products]

        # 创建一个共享的异步客户端
        async_client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )

        # 统计信息
        stats = {
            "total_requests": len(products) * 2,  # 每个产品有标题和描述两个请求
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens": 0,
            "total_time": 0,
            "errors": {}
        }

        batch_start_time = time.time()
        logger.info(f"开始批量翻译，共 {len(products)} 个产品，总计 {stats['total_requests']} 个请求")

        try:
            # 为所有产品创建翻译任务
            translation_tasks = []
            for product in products:
                title = product.get('title', '')
                description = product.get('description', '')
                if title and description:
                    # 对每个产品的标题和描述创建翻译任务
                    translation_tasks.append(
                        asyncio.gather(
                            self._translate_text_with_client(title, True, async_client),
                            self._translate_text_with_client(description, False, async_client)
                        )
                    )

            # 执行所有翻译任务
            all_results = await asyncio.gather(*translation_tasks)
            
            # 处理结果
            translated_products = []
            for i, (title_result, desc_result) in enumerate(all_results):
                product_result = {}
                
                if title_result['success']:
                    stats['successful_requests'] += 1
                    product_result['title'] = title_result['text']
                else:
                    stats['failed_requests'] += 1
                    error_type = title_result.get('error', 'Unknown error')
                    stats['errors'][error_type] = stats['errors'].get(error_type, 0) + 1
                
                if desc_result['success']:
                    stats['successful_requests'] += 1
                    product_result['description'] = desc_result['text']
                else:
                    stats['failed_requests'] += 1
                    error_type = desc_result.get('error', 'Unknown error')
                    stats['errors'][error_type] = stats['errors'].get(error_type, 0) + 1
                
                if not product_result.get('title') or not product_result.get('description'):
                    product_result['error'] = '部分翻译失败'
                
                translated_products.append(product_result)

            # 计算统计信息
            stats['total_time'] = time.time() - batch_start_time
            success_rate = (stats['successful_requests'] / stats['total_requests']) * 100 if stats['total_requests'] > 0 else 0

            # 记录日志
            logger.info(f"=== Batch Translation Statistics ===")
            logger.info(f"Total Requests: {stats['total_requests']}")
            logger.info(f"Successful Requests: {stats['successful_requests']}")
            logger.info(f"Failed Requests: {stats['failed_requests']}")
            logger.info(f"Success Rate: {success_rate:.2f}%")
            logger.info(f"Total Time Taken: {stats['total_time']:.2f} seconds")
            if stats['errors']:
                logger.info("Error Distribution:")
                for error_type, count in stats['errors'].items():
                    logger.info(f"  {error_type}: {count}")

            return translated_products

        finally:
            # 确保关闭异步客户端
            await async_client.close()

if __name__ == "__main__":
    translator = DeepSeekhuoshan()