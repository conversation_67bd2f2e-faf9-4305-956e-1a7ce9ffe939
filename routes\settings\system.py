from flask import Blueprint, render_template, jsonify, request
from models import db, SystemSettings, TokenUsage, beijing_now, MultiModelConfig
import logging

bp = Blueprint('system', __name__)
logger = logging.getLogger(__name__)

@bp.route('/system-settings', methods=['GET'])
def system_settings():
    """系统设置页面"""
    settings = {
        setting.key: setting.value 
        for setting in SystemSettings.query.all()
    }
    return render_template('system.html', settings=settings, active_page='system-settings')

@bp.route('/system-settings/save', methods=['POST'])
def update_settings():
    """更新系统设置"""
    try:
        if not request.is_json:
            return jsonify({
                'code': 400,
                'message': '请求格式错误，需要 JSON 格式'
            }), 400

        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '没有接收到数据'
            }), 400

        for key, value in data.items():
            setting = SystemSettings.query.filter_by(key=key).first()
            if setting:
                setting.value = value
            else:
                setting = SystemSettings(key=key, value=value)
                db.session.add(setting)
        
        db.session.commit()
        return jsonify({
            'code': 0,
            'message': '设置已更新'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新设置失败: {str(e)}'
        }), 500 

@bp.route('/api/settings/translation', methods=['GET'])
def get_translation_settings():
    try:
        translation_enabled = SystemSettings.get_setting('translation_enabled')
        return jsonify({
            'translation_enabled': translation_enabled == 'true' or translation_enabled == True,
            'translation_service': SystemSettings.get_setting('translation_service', 'deeplx'),
            'translation_api_key': SystemSettings.get_setting('translation_api_key', ''),
            'translation_retry_count': int(SystemSettings.get_setting('translation_retry_count', '3')),
            'translation_retry_interval': int(SystemSettings.get_setting('translation_retry_interval', '5')),
            'translation_model': SystemSettings.get_setting('translation_model', ''),
            'zh_deepseek_api_key': SystemSettings.get_setting('zh_deepseek_api_key', ''),
            'zh_mtran_api_key': SystemSettings.get_setting('zh_mtran_api_key', ''),
            'zh_translation_model': SystemSettings.get_setting('zh_translation_model', '')
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/settings/save', methods=['POST'])
def save_settings():
    """保存系统设置"""
    try:
        data = request.get_json()
        
        # 保存常规设置
        settings_to_save = [
            'proxy_enabled', 'proxy_host', 'proxy_port',
            'task_expire_days', 'max_products_per_task', 'auto_check_interval',
            'translation_service', 'translation_api_key',
            'translation_retry_count', 'translation_retry_interval', 'translation_model',
            'token_input_rate', 'token_output_rate',
            'zh_deepseek_api_key', 'zh_mtran_api_key', 'zh_translation_model'
        ]
        
        # 特殊处理布尔值
        if 'translation_enabled' in data:
            SystemSettings.set_setting('translation_enabled', str(data['translation_enabled']).lower())
        
        for key in settings_to_save:
            if key in data:
                SystemSettings.set_setting(key, str(data[key]))
                logger.debug(f"保存设置: {key}={data[key]}")
        
        # 处理多模型配置
        if 'multi_model_config' in data:
            multi_model_config = data['multi_model_config']
            enabled = multi_model_config.get('enabled', False)
            models = multi_model_config.get('models', [])
            
            # 删除所有现有的模型配置
            MultiModelConfig.query.delete()
            
            # 添加新的模型配置
            for model in models:
                new_model = MultiModelConfig(
                    enabled=enabled,
                    model_name=model.get('model_name', ''),
                    model_id=model['model_id'],
                    api_key=model['api_key'],
                    token_limit=model.get('token_limit', 5000000),
                    used_tokens=model.get('used_tokens', 0),
                    is_active=model.get('is_active', True)
                )
                db.session.add(new_model)
            
            db.session.commit()
        
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/settings/token-usage', methods=['GET'])
def get_token_usage():
    """获取token使用统计"""
    try:
        # 获取API提供商参数，默认为deepseek
        api_provider = request.args.get('provider', 'deepseek_official')
        
        # 获取指定API提供商的统计数据
        stats = TokenUsage.get_usage_stats(api_provider)
        
        # 获取费率设置
        # 不同API提供商可能有不同的费率设置
        rate_suffix = 'deepseek_huoshan' if api_provider == 'deepseek_huoshan' else ''
        input_rate = float(SystemSettings.get_setting(f'token_input_rate{rate_suffix}', '0.0020'))
        output_rate = float(SystemSettings.get_setting(f'token_output_rate{rate_suffix}', '0.0080'))
        
        # 计算费用
        def calculate_cost(usage):
            input_cost = (usage['request_tokens'] / 1000) * input_rate
            output_cost = (usage['response_tokens'] / 1000) * output_rate
            return {
                'tokens': usage,
                'cost': {
                    'input': round(input_cost, 4),
                    'output': round(output_cost, 4),
                    'total': round(input_cost + output_cost, 4)
                }
            }
        
        return jsonify({
            'today': calculate_cost(stats['today']),
            'yesterday': calculate_cost(stats['yesterday']),
            'total': calculate_cost(stats['total']),
            'rates': {
                'input': input_rate,
                'output': output_rate
            }
        })
    except Exception as e:
        logger.error(f"获取token使用统计失败: {str(e)}")
        return jsonify({'error': str(e)}), 500 