from flask_apscheduler import APScheduler
from datetime import datetime
from models import db, TaskCenter, beijing_now
from routes.task_center.services import TaskService
from routes.task_center.handlers import TaskHandler
import logging
import pytz
import asyncio
from apscheduler.schedulers.background import BackgroundScheduler
from threading import Lock

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 设置APScheduler的日志级别为WARNING，关闭初始化信息打印
logging.getLogger('apscheduler').setLevel(logging.ERROR)

logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器类"""
    def __init__(self):
        self.scheduler = APScheduler()
        self.flask_app = None
        self._task_lock = Lock()
        self._event_loop = None
        
    def init_app(self, app):
        """初始化调度器"""
        self.flask_app = app
        
        # 配置 APScheduler
        app.config['SCHEDULER_API_ENABLED'] = True
        app.config['SCHEDULER_TIMEZONE'] = 'Asia/Shanghai'
        
        # 初始化调度器
        self.scheduler.init_app(app)
        
        # 添加任务检查作业
        self.scheduler.add_job(
            id='check_tasks',
            func=self._check_tasks,
            trigger='interval',
            seconds=10,  # 增加检查间隔到10秒
            replace_existing=True,
            max_instances=1
        )
        
        # 启动调度器
        self.scheduler.start()
        logger.info("✅ 任务调度器初始化成功")
        
    def _check_tasks(self):
        """检查待执行的任务"""
        if not self.flask_app:
            return
            
        # 使用线程锁确保同一时间只有一个检查在运行
        if not self._task_lock.acquire(blocking=False):
            return
            
        try:
            with self.flask_app.app_context():
                # 确保事件循环在当前线程中可用
                if not self._event_loop:
                    self._event_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(self._event_loop)
                
                # 运行异步任务检查
                self._event_loop.run_until_complete(self._process_tasks())
        except Exception as e:
            logger.error(f"任务检查过程出错: {str(e)}")
        finally:
            self._task_lock.release()
            
    async def _process_tasks(self):
        """处理待执行的任务"""
        try:
            current_time = beijing_now()
            
            # 获取所有需要执行的任务
            tasks = TaskCenter.query.filter(
                TaskCenter.status == 'running',
                TaskCenter.next_run_at <= current_time
            ).all()
            
            if not tasks:
                return
                
            # logger.info(f"找到 {len(tasks)} 个待执行任务")
            
            # 一次只处理一个任务，避免资源竞争
            for task in tasks:
                if task.status == 'executing':
                    continue
                    
                await execute_single_task(task)
                # 每个任务执行完后暂停一下，避免资源占用过高
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"处理任务时发生错误: {str(e)}")
            
    def shutdown(self):
        """关闭调度器"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown()
            if self._event_loop:
                self._event_loop.close()
        except Exception as e:
            logger.error(f"关闭调度器时发生错误: {str(e)}")

# 创建全局调度器实例
task_scheduler = TaskScheduler()

# 为了保持与现有代码兼容的接口
def init_scheduler(app):
    """初始化调度器的兼容函数"""
    task_scheduler.init_app(app)

def shutdown_scheduler():
    """关闭调度器的兼容函数"""
    task_scheduler.shutdown()

# 保持execute_single_task函数不变
async def execute_single_task(task):
    """
    执行单个任务的异步函数
    :param task: 任务对象
    """
    try:
        # logger.info("正在处理任务 [ID: %s, 类型: %s]", task.id, task.task_type)
        
        # 再次检查任务状态（防止并发情况下状态已改变）
        task = db.session.merge(task)  # 重新获取最新状态
        if task.status == 'executing':
            logger.info("任务 %s: 已在其他进程中执行，跳过", task.id)
            return
            
        # 获取要处理的产品
        products = TaskService.get_batch_products(task)
        if not products:
            logger.info("任务 %s: 没有可处理的产品，跳过执行", task.id)
            task.status = 'stopped'
            db.session.commit()
            return
        
        # logger.info("任务 %s: 获取到 %d 个待处理产品", task.id, len(products))
        task.status = 'executing'  # 设置状态为执行中
        db.session.commit()  # 提交状态更新

        try:
            # 根据任务类型执行相应的处理器
            if task.task_type == 'translation':
                logger.info("任务 %s: 开始执行翻译...", task.id)
                result = await TaskHandler.handle_translation(task, products)
                logger.info("任务 %s: 翻译执行完成", task.id)
            
            # 发布任务  
            elif task.task_type == 'publish':
                logger.info("任务 %s: 开始执行发布...", task.id)
                result = await TaskHandler.handle_publish(task, products)
                logger.info("任务 %s: 发布执行完成", task.id)
            
            # 更新任务进度和累计数量（只更新当前批次的进度）
            task.current_success = result['success_count']
            task.current_failed = result['failed_count']
            task.total_success_count += result['success_count']  # 累加到总成功数
            task.total_failed_count += result['failed_count']    # 累加到总失败数
            task.last_error = result['last_error']
            task.status = 'running'  # 执行完成后恢复为running状态
            db.session.commit()
            
            # 更新最后执行时间，同时会自动更新下次执行时间
            TaskService.update_last_run_time(task)
            logger.info("任务 %s: 更新执行时间 -> %s", task.id, 
                    task.last_run_at.strftime('%Y-%m-%d %H:%M:%S') if task.last_run_at else 'None')
                    
        except Exception as e:
            logger.error("任务 %s: 执行出错 ❌ [错误: %s]", task.id, str(e))
            task.status = 'running'  # 发生错误时也恢复running状态
            task.last_error = str(e)
            db.session.commit()
            
    except Exception as e:
        logger.error("任务 %s: 处理过程出错 ❌ [错误: %s]", task.id, str(e))
        try:
            task.status = 'running'  # 确保任务状态被重置
            db.session.commit()
        except:
            pass 